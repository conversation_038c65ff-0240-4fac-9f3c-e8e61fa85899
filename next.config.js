/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  serverExternalPackages: ['mysql2'],

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'droobhajer.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
    ],
    // تحسين معالجة الأخطاء
    unoptimized: false,
    // السماح بـ SVG للـ placeholder API
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/uploads/:path*',
      },
      // إعادة توجيه المسارات العربية إلى المسارات الصحيحة
      {
        source: '/ar/المنتجات',
        destination: '/ar/products',
      },
      {
        source: '/ar/الفئات',
        destination: '/ar/categories',
      },
      {
        source: '/ar/التصنيفات',
        destination: '/ar/categories',
      },
      {
        source: '/ar/حول',
        destination: '/ar/about',
      },
      {
        source: '/ar/اتصل',
        destination: '/ar/contact',
      },
      {
        source: '/ar/تواصل',
        destination: '/ar/contact',
      },
      {
        source: '/ar/من-نحن',
        destination: '/ar/about',
      },
      {
        source: '/ar/تواصل-معنا',
        destination: '/ar/contact',
      },
    ];
  },
};

module.exports = nextConfig;
