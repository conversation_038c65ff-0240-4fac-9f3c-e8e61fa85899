'use client';

import { useState, useEffect } from 'react';

interface UsePageSplashOptions {
  duration?: number;
  showOnMount?: boolean;
  showOnRouteChange?: boolean;
  minDisplayTime?: number;
}

interface UsePageSplashReturn {
  showSplash: boolean;
  triggerSplash: () => void;
  hideSplash: () => void;
  isLoading: boolean;
}

export const usePageSplash = (options: UsePageSplashOptions = {}): UsePageSplashReturn => {
  const {
    duration = 1500,
    showOnMount = false,
    showOnRouteChange = false,
    minDisplayTime = 800
  } = options;

  const [showSplash, setShowSplash] = useState(showOnMount);
  const [isLoading, setIsLoading] = useState(false);
  const [startTime, setStartTime] = useState<number | null>(null);

  useEffect(() => {
    if (showOnMount) {
      setStartTime(Date.now());
      setIsLoading(true);
    }
  }, [showOnMount]);

  const triggerSplash = () => {
    setStartTime(Date.now());
    setShowSplash(true);
    setIsLoading(true);
  };

  const hideSplash = () => {
    const now = Date.now();
    const elapsed = startTime ? now - startTime : 0;
    const remainingTime = Math.max(0, minDisplayTime - elapsed);

    setTimeout(() => {
      setShowSplash(false);
      setIsLoading(false);
      setStartTime(null);
    }, remainingTime);
  };

  // Auto hide after duration
  useEffect(() => {
    if (showSplash && startTime) {
      const timer = setTimeout(() => {
        hideSplash();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [showSplash, startTime, duration]);

  return {
    showSplash,
    triggerSplash,
    hideSplash,
    isLoading
  };
};
