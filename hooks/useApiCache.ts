'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

// نوع البيانات المخزنة في الكاش
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// إعدادات الكاش
interface CacheOptions {
  ttl?: number; // مدة البقاء بالميلي ثانية (افتراضي: 5 دقائق)
  staleWhileRevalidate?: number; // مدة استخدام البيانات القديمة أثناء التحديث (افتراضي: 10 دقائق)
  revalidateOnFocus?: boolean; // إعادة التحقق عند التركيز على النافذة
  revalidateOnReconnect?: boolean; // إعادة التحقق عند إعادة الاتصال
}

// الكاش العام في الذاكرة
const globalCache = new Map<string, CacheEntry<any>>();

// دالة لإنشاء مفتاح الكاش
function createCacheKey(url: string, params?: Record<string, any>): string {
  const paramString = params ? JSON.stringify(params) : '';
  return `${url}${paramString}`;
}

// دالة للتحقق من انتهاء صلاحية البيانات
function isExpired(entry: CacheEntry<any>): boolean {
  return Date.now() > entry.expiresAt;
}

// دالة للتحقق من كون البيانات قديمة
function isStale(entry: CacheEntry<any>, staleTime: number): boolean {
  return Date.now() > (entry.timestamp + staleTime);
}

/**
 * Hook لإدارة الكاش للـ API calls
 */
export function useApiCache<T>(
  url: string,
  options: CacheOptions = {},
  params?: Record<string, any>
) {
  const {
    ttl = 5 * 60 * 1000, // 5 دقائق
    staleWhileRevalidate = 10 * 60 * 1000, // 10 دقائق
    revalidateOnFocus = true,
    revalidateOnReconnect = true
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const cacheKey = createCacheKey(url, params);
  const abortControllerRef = useRef<AbortController | null>(null);

  // دالة لجلب البيانات
  const fetchData = useCallback(async (isRevalidation = false) => {
    try {
      // إلغاء الطلب السابق إذا كان موجوداً
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      if (!isRevalidation) {
        setLoading(true);
      } else {
        setIsValidating(true);
      }
      setError(null);

      const response = await fetch(url, {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      const responseData = result.success ? result.data : result;

      // حفظ البيانات في الكاش
      const cacheEntry: CacheEntry<T> = {
        data: responseData,
        timestamp: Date.now(),
        expiresAt: Date.now() + ttl
      };
      globalCache.set(cacheKey, cacheEntry);

      setData(responseData);
      setError(null);
    } catch (err: any) {
      if (err.name !== 'AbortError') {
        console.error('API Cache Error:', err);
        setError(err.message || 'حدث خطأ في جلب البيانات');
      }
    } finally {
      setLoading(false);
      setIsValidating(false);
    }
  }, [url, cacheKey, ttl]);

  // دالة لإعادة التحقق من البيانات
  const revalidate = useCallback(() => {
    fetchData(true);
  }, [fetchData]);

  // دالة لمسح البيانات من الكاش
  const mutate = useCallback((newData?: T) => {
    if (newData !== undefined) {
      const cacheEntry: CacheEntry<T> = {
        data: newData,
        timestamp: Date.now(),
        expiresAt: Date.now() + ttl
      };
      globalCache.set(cacheKey, cacheEntry);
      setData(newData);
    } else {
      globalCache.delete(cacheKey);
      fetchData();
    }
  }, [cacheKey, ttl, fetchData]);

  // التحقق من البيانات المخزنة عند التحميل
  useEffect(() => {
    const cachedEntry = globalCache.get(cacheKey);

    if (cachedEntry) {
      // إذا كانت البيانات صالحة، استخدمها
      if (!isExpired(cachedEntry)) {
        setData(cachedEntry.data);
        setLoading(false);

        // إذا كانت البيانات قديمة، أعد التحقق في الخلفية
        if (isStale(cachedEntry, staleWhileRevalidate)) {
          fetchData(true);
        }
        return;
      }
    }

    // جلب البيانات إذا لم تكن موجودة في الكاش أو انتهت صلاحيتها
    fetchData();
  }, [cacheKey, fetchData, staleWhileRevalidate]);

  // إعادة التحقق عند التركيز على النافذة
  useEffect(() => {
    if (!revalidateOnFocus) return;

    const handleFocus = () => {
      const cachedEntry = globalCache.get(cacheKey);
      if (cachedEntry && isStale(cachedEntry, 30000)) { // 30 ثانية
        revalidate();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [cacheKey, revalidate, revalidateOnFocus]);

  // إعادة التحقق عند إعادة الاتصال
  useEffect(() => {
    if (!revalidateOnReconnect) return;

    const handleOnline = () => {
      revalidate();
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [revalidate, revalidateOnReconnect]);

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    isValidating,
    revalidate,
    mutate
  };
}

/**
 * دالة لمسح الكاش بالكامل
 */
export function clearCache(): void {
  globalCache.clear();
  console.log('🗑️ تم مسح جميع بيانات الكاش');
}

/**
 * دالة لمسح كاش معين
 */
export function clearCacheKey(url: string, params?: Record<string, any>): void {
  const key = createCacheKey(url, params);
  globalCache.delete(key);
  console.log(`🗑️ تم مسح كاش: ${key}`);
}

/**
 * دالة للحصول على معلومات الكاش (للتطوير)
 */
export function getCacheInfo(): { size: number; keys: string[] } {
  return {
    size: globalCache.size,
    keys: Array.from(globalCache.keys())
  };
}
