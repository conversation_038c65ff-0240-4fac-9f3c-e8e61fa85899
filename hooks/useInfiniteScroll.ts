'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

interface UseInfiniteScrollOptions {
  threshold?: number; // المسافة من أسفل الصفحة لبدء التحميل (بالبكسل)
  rootMargin?: string; // هامش للـ Intersection Observer
  enabled?: boolean; // تفعيل أو إلغاء الـ infinite scroll
}

interface UseInfiniteScrollReturn {
  isLoading: boolean;
  hasMore: boolean;
  loadMore: () => void;
  setHasMore: (hasMore: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  observerRef: React.RefObject<HTMLDivElement | null>;
}

export function useInfiniteScroll(
  onLoadMore: () => Promise<void> | void,
  options: UseInfiniteScrollOptions = {}
): UseInfiniteScrollReturn {
  const {
    threshold = 100,
    rootMargin = '0px',
    enabled = true
  } = options;

  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observerRef = useRef<HTMLDivElement>(null);
  const loadingRef = useRef(false);

  const loadMore = useCallback(async () => {
    if (loadingRef.current || !hasMore || !enabled) return;

    try {
      loadingRef.current = true;
      setIsLoading(true);
      await onLoadMore();
    } catch (error) {
      console.error('Error loading more items:', error);
    } finally {
      loadingRef.current = false;
      setIsLoading(false);
    }
  }, [onLoadMore, hasMore, enabled]);

  useEffect(() => {
    const observer = observerRef.current;
    if (!observer || !enabled) return;

    const intersectionObserver = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !loadingRef.current) {
          loadMore();
        }
      },
      {
        rootMargin,
        threshold: 0.1
      }
    );

    intersectionObserver.observe(observer);

    return () => {
      intersectionObserver.disconnect();
    };
  }, [loadMore, hasMore, enabled, rootMargin]);

  // Alternative scroll-based detection (fallback)
  useEffect(() => {
    if (!enabled) return;

    const handleScroll = () => {
      if (loadingRef.current || !hasMore) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = window.innerHeight;

      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        loadMore();
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [loadMore, hasMore, enabled, threshold]);

  return {
    isLoading,
    hasMore,
    loadMore,
    setHasMore,
    setIsLoading,
    observerRef
  };
}
