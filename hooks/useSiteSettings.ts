'use client';

import { useState, useEffect } from 'react';
import { SiteSettings } from '../types/admin';
import { getSiteSettings } from '../data/settings';

/**
 * Hook مخصص لاستخدام إعدادات الموقع
 * يقوم بجلب الإعدادات من التخزين المحلي ويعيد تحديثها عند تغييرها
 */
export const useSiteSettings = () => {
  const [settings, setSettings] = useState<SiteSettings | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        // محاولة جلب الإعدادات من API أولاً
        const response = await fetch('/api/settings');

        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        } else {
          // في حالة فشل API، استخدام الإعدادات المحلية
          console.warn('Failed to fetch settings from API, using local settings');
          const localSettings = getSiteSettings();
          setSettings(localSettings);
        }
      } catch (error) {
        console.error('Error loading site settings:', error);
        // في حالة الخطأ، استخدام الإعدادات المحلية
        const localSettings = getSiteSettings();
        setSettings(localSettings);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();

    // الاستماع لتغييرات التخزين المحلي
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteSettings') {
        loadSettings();
      }
    };

    // الاستماع لحدث مخصص لإعادة تحميل الإعدادات
    const handleSettingsUpdate = () => {
      loadSettings();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('siteSettingsUpdated', handleSettingsUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('siteSettingsUpdated', handleSettingsUpdate);
    };
  }, []);

  // دالة لإعادة تحميل الإعدادات يدوياً
  const reload = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/settings');

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else {
        const localSettings = getSiteSettings();
        setSettings(localSettings);
      }
    } catch (error) {
      console.error('Error reloading settings:', error);
      const localSettings = getSiteSettings();
      setSettings(localSettings);
    } finally {
      setLoading(false);
    }
  };

  return {
    settings,
    loading,
    reload
  };
};

/**
 * Hook مبسط للحصول على إعدادات محددة
 */
export const useHeaderSettings = () => {
  const { settings, loading } = useSiteSettings();
  return {
    headerSettings: settings?.headerSettings,
    loading
  };
};

export const useFooterSettings = () => {
  const { settings, loading } = useSiteSettings();
  return {
    footerSettings: settings?.footerSettings,
    loading
  };
};

export const useSocialLinks = () => {
  const { settings, loading } = useSiteSettings();
  return {
    socialLinks: settings?.socialLinks,
    loading
  };
};

export const useContactInfo = () => {
  const { settings, loading } = useSiteSettings();
  return {
    contactInfo: {
      email: settings?.contactEmail,
      phone: settings?.phone,
      whatsapp: settings?.whatsappNumber,
      address: settings?.address,
      addressAr: settings?.addressAr,
      workingHours: settings?.workingHours,
      workingHoursAr: settings?.workingHoursAr
    },
    loading
  };
};

// Hook للحصول على الفئات الرئيسية للفوتر
export const useFooterCategories = () => {
  const [categories, setCategories] = useState<Array<{
    id: string;
    name: string;
    name_ar: string;
    subcategories?: Array<{
      id: string;
      name: string;
      name_ar: string;
    }>;
  }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/navbar/categories');
        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }

        const result = await response.json();
        if (result.success && result.data) {
          // أخذ أول 6 فئات رئيسية للفوتر
          const footerCategories = result.data.slice(0, 6).map((cat: any) => ({
            id: cat.id,
            name: cat.name,
            name_ar: cat.name_ar,
            // أخذ أول 4 فئات فرعية لكل فئة رئيسية
            subcategories: cat.subcategories?.slice(0, 4).map((sub: any) => ({
              id: sub.id,
              name: sub.name,
              name_ar: sub.name_ar
            })) || []
          }));

          setCategories(footerCategories);
        }
      } catch (err) {
        console.error('Error fetching footer categories:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return { categories, loading, error };
};

export const useCompanyInfo = () => {
  const { settings, loading } = useSiteSettings();
  return {
    companyInfo: {
      name: settings?.siteName,
      nameAr: settings?.siteNameAr,
      about: settings?.aboutText,
      aboutAr: settings?.aboutTextAr,
      email: settings?.contactEmail,
      phone: settings?.phone,
      whatsapp: settings?.whatsappNumber,
      address: settings?.address,
      addressAr: settings?.addressAr,
      workingHours: settings?.workingHours,
      workingHoursAr: settings?.workingHoursAr
    },
    loading
  };
};
