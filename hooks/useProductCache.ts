'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { ProductWithDetails, Category, Subcategory } from '../types/mysql-database';

interface ProductData {
  product: ProductWithDetails;
  category: Category | null;
  subcategory: Subcategory | null;
}

interface CacheEntry {
  data: ProductData;
  timestamp: number;
  expiresAt: number;
}

// Cache في الذاكرة للمنتجات
const productCache = new Map<string, CacheEntry>();

// مدة انتهاء صلاحية الكاش (5 دقائق)
const CACHE_DURATION = 5 * 60 * 1000;

// مدة الكاش المؤقت (30 دقيقة)
const STALE_CACHE_DURATION = 30 * 60 * 1000;

export function useProductCache(productId: string) {
  const [data, setData] = useState<ProductData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // دالة للحصول على البيانات من الكاش
  const getCachedData = useCallback((id: string): ProductData | null => {
    const cached = productCache.get(id);
    if (!cached) return null;

    const now = Date.now();
    
    // إذا انتهت صلاحية الكاش تماماً، احذفه
    if (now > cached.expiresAt) {
      productCache.delete(id);
      return null;
    }

    return cached.data;
  }, []);

  // دالة لحفظ البيانات في الكاش
  const setCachedData = useCallback((id: string, productData: ProductData) => {
    const now = Date.now();
    productCache.set(id, {
      data: productData,
      timestamp: now,
      expiresAt: now + STALE_CACHE_DURATION
    });
  }, []);

  // دالة للتحقق من صحة الكاش
  const isCacheValid = useCallback((id: string): boolean => {
    const cached = productCache.get(id);
    if (!cached) return false;

    const now = Date.now();
    return now - cached.timestamp < CACHE_DURATION;
  }, []);

  // دالة لجلب البيانات من API
  const fetchProductData = useCallback(async (id: string, useCache = true): Promise<ProductData | null> => {
    // التحقق من الكاش أولاً
    if (useCache) {
      const cachedData = getCachedData(id);
      if (cachedData && isCacheValid(id)) {
        console.log('🎯 Using cached product data for:', id);
        return cachedData;
      }
    }

    try {
      // إلغاء أي طلب سابق
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // إنشاء controller جديد
      abortControllerRef.current = new AbortController();

      console.log('🔄 Fetching fresh product data for:', id);

      const response = await fetch(`/api/products/${id}`, {
        headers: {
          'Content-Type': 'application/json',
        },
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success || !result.data) {
        throw new Error('Invalid response data');
      }

      const { product, category, subcategory } = result.data;
      const productData: ProductData = { product, category, subcategory };

      // حفظ في الكاش
      setCachedData(id, productData);

      return productData;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request aborted for product:', id);
        return null;
      }
      
      // في حالة الخطأ، حاول استخدام الكاش المؤقت
      const staleData = getCachedData(id);
      if (staleData) {
        console.log('🔄 Using stale cached data due to error for:', id);
        return staleData;
      }
      
      throw error;
    }
  }, [getCachedData, isCacheValid, setCachedData]);

  // تحميل البيانات
  useEffect(() => {
    if (!productId) return;

    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        const productData = await fetchProductData(productId);
        
        if (productData) {
          setData(productData);
        } else {
          setError('Product not found');
        }
      } catch (err) {
        console.error('Error loading product:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // تنظيف عند إلغاء التحميل
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [productId, fetchProductData]);

  // دالة لإعادة تحميل البيانات
  const refetch = useCallback(async () => {
    if (!productId) return;

    try {
      setLoading(true);
      setError(null);

      const productData = await fetchProductData(productId, false); // تجاهل الكاش
      
      if (productData) {
        setData(productData);
      } else {
        setError('Product not found');
      }
    } catch (err) {
      console.error('Error refetching product:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [productId, fetchProductData]);

  // دالة لمسح الكاش
  const clearCache = useCallback((id?: string) => {
    if (id) {
      productCache.delete(id);
    } else {
      productCache.clear();
    }
  }, []);

  return {
    data,
    loading,
    error,
    refetch,
    clearCache,
    isCached: data ? isCacheValid(productId) : false
  };
}

// دالة مساعدة لتنظيف الكاش المنتهي الصلاحية
export function cleanupExpiredCache() {
  const now = Date.now();
  for (const [key, entry] of productCache.entries()) {
    if (now > entry.expiresAt) {
      productCache.delete(key);
    }
  }
}

// تنظيف دوري للكاش
if (typeof window !== 'undefined') {
  setInterval(cleanupExpiredCache, 10 * 60 * 1000); // كل 10 دقائق
}
