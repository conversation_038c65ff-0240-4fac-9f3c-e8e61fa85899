'use client';

import { useEffect, useRef, useCallback } from 'react';

interface ScrollPosition {
  x: number;
  y: number;
  timestamp: number;
}

// مخزن عام لمواضع التمرير
const scrollPositions = new Map<string, ScrollPosition>();

// مدة انتهاء صلاحية موضع التمرير (30 دقيقة)
const SCROLL_POSITION_TTL = 30 * 60 * 1000;

export function useScrollPosition(key: string) {
  const isRestoringRef = useRef(false);
  const lastSavedPosition = useRef<ScrollPosition | null>(null);

  // حفظ موضع التمرير
  const saveScrollPosition = useCallback(() => {
    const position: ScrollPosition = {
      x: window.pageXOffset || document.documentElement.scrollLeft,
      y: window.pageYOffset || document.documentElement.scrollTop,
      timestamp: Date.now()
    };

    scrollPositions.set(key, position);
    lastSavedPosition.current = position;
    
    // حفظ في sessionStorage كنسخة احتياطية
    try {
      sessionStorage.setItem(`scroll_${key}`, JSON.stringify(position));
    } catch (error) {
      console.warn('Failed to save scroll position to sessionStorage:', error);
    }
  }, [key]);

  // استعادة موضع التمرير
  const restoreScrollPosition = useCallback(() => {
    if (isRestoringRef.current) return;

    let savedPosition = scrollPositions.get(key);

    // إذا لم توجد في الذاكرة، حاول من sessionStorage
    if (!savedPosition) {
      try {
        const stored = sessionStorage.getItem(`scroll_${key}`);
        if (stored) {
          savedPosition = JSON.parse(stored);
        }
      } catch (error) {
        console.warn('Failed to restore scroll position from sessionStorage:', error);
      }
    }

    if (savedPosition) {
      const now = Date.now();
      
      // تحقق من انتهاء صلاحية الموضع
      if (now - savedPosition.timestamp > SCROLL_POSITION_TTL) {
        scrollPositions.delete(key);
        try {
          sessionStorage.removeItem(`scroll_${key}`);
        } catch (error) {
          // تجاهل الخطأ
        }
        return;
      }

      isRestoringRef.current = true;
      
      // استعادة الموضع مع تأخير قصير للسماح بتحميل المحتوى
      const restoreWithDelay = () => {
        window.scrollTo({
          left: savedPosition!.x,
          top: savedPosition!.y,
          behavior: 'auto' // استعادة فورية
        });
        
        // تأكد من الاستعادة بعد تأخير قصير
        setTimeout(() => {
          window.scrollTo({
            left: savedPosition!.x,
            top: savedPosition!.y,
            behavior: 'auto'
          });
          isRestoringRef.current = false;
        }, 100);
      };

      // استعادة فورية
      restoreWithDelay();
      
      // استعادة إضافية بعد تحميل الصور
      setTimeout(restoreWithDelay, 500);
    }
  }, [key]);

  // مسح موضع التمرير
  const clearScrollPosition = useCallback(() => {
    scrollPositions.delete(key);
    try {
      sessionStorage.removeItem(`scroll_${key}`);
    } catch (error) {
      // تجاهل الخطأ
    }
  }, [key]);

  // حفظ موضع التمرير عند التنقل
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (!isRestoringRef.current) {
        saveScrollPosition();
      }
    };

    const handleScroll = () => {
      if (!isRestoringRef.current) {
        // حفظ الموضع مع throttling
        if (window.scrollSaveTimeout) {
          clearTimeout(window.scrollSaveTimeout);
        }
        window.scrollSaveTimeout = window.setTimeout(saveScrollPosition, 150);
      }
    };

    // حفظ عند إغلاق الصفحة أو التنقل
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('pagehide', handleBeforeUnload);
    
    // حفظ أثناء التمرير
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('pagehide', handleBeforeUnload);
      window.removeEventListener('scroll', handleScroll);
      if (window.scrollSaveTimeout) {
        clearTimeout(window.scrollSaveTimeout);
      }
    };
  }, [saveScrollPosition]);

  // استعادة الموضع عند تحميل الصفحة
  useEffect(() => {
    // تأخير قصير للسماح بتحميل المحتوى
    const timer = setTimeout(() => {
      restoreScrollPosition();
    }, 100);

    return () => clearTimeout(timer);
  }, [restoreScrollPosition]);

  return {
    saveScrollPosition,
    restoreScrollPosition,
    clearScrollPosition,
    hasStoredPosition: () => {
      const stored = scrollPositions.get(key);
      return stored && (Date.now() - stored.timestamp < SCROLL_POSITION_TTL);
    }
  };
}

// دالة مساعدة لتنظيف المواضع المنتهية الصلاحية
export function cleanupExpiredScrollPositions() {
  const now = Date.now();
  
  for (const [key, position] of scrollPositions.entries()) {
    if (now - position.timestamp > SCROLL_POSITION_TTL) {
      scrollPositions.delete(key);
    }
  }
  
  // تنظيف sessionStorage أيضاً
  try {
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key?.startsWith('scroll_')) {
        const stored = sessionStorage.getItem(key);
        if (stored) {
          const position = JSON.parse(stored);
          if (now - position.timestamp > SCROLL_POSITION_TTL) {
            sessionStorage.removeItem(key);
          }
        }
      }
    }
  } catch (error) {
    console.warn('Failed to cleanup sessionStorage:', error);
  }
}

// تنظيف دوري
if (typeof window !== 'undefined') {
  setInterval(cleanupExpiredScrollPositions, 5 * 60 * 1000); // كل 5 دقائق
}

// إضافة نوع للـ window object
declare global {
  interface Window {
    scrollSaveTimeout?: number;
  }
}
