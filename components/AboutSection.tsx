'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Locale } from '../lib/i18n';
import Link from 'next/link';

interface AboutSectionProps {
  locale: Locale;
}

const AboutSection: React.FC<AboutSectionProps> = ({ locale }) => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const content = {
    ar: {
      title: 'لماذا تختار دروب هجر؟',
      subtitle: 'رائدون في تجهيزات الفنادق والمطاعم الفاخرة',
      description: 'دروب هجر هي شركة رائدة في مجال توفير أطباق البوفيه الفاخرة ومعدات الضيافة الاحترافية. نحن متخصصون في تجهيز الفنادق والمنتجعات والمطاعم الراقية بأفضل المنتجات عالية الجودة. مع خبرة تزيد عن 15 عاماً في السوق السعودي، نقدم حلولاً شاملة تلبي احتياجات عملائنا من المستثمرين وشركات التصميم الداخلي.',
      features: [
        'أطباق بوفيه فاخرة من أجود المواد',
        'معدات عرض احترافية للفنادق',
        'تجهيزات مطابخ عالية الجودة',
        'أدوات ضيافة راقية ومتنوعة',
        'خدمة عملاء متميزة ودعم فني',
        'عروض أسعار مخصصة للمشاريع'
      ],
      stats: [
        { number: '500+', label: 'مشروع فندقي' },
        { number: '15+', label: 'سنة خبرة' },
        { number: '1000+', label: 'منتج متنوع' },
        { number: '24/7', label: 'دعم فني' }
      ],
      cta: 'تعرف على المزيد'
    },
    en: {
      title: 'Why Choose DROOB HAJER?',
      subtitle: 'Leaders in luxury hotel and restaurant equipment',
      description: 'DROOB HAJER is a leading company in providing luxury buffet plates and professional hospitality equipment. We specialize in equipping hotels, resorts, and fine dining restaurants with the best high-quality products. With over 15 years of experience in the Saudi market, we provide comprehensive solutions that meet the needs of our clients from investors and interior design companies.',
      features: [
        'Luxury buffet plates from finest materials',
        'Professional display equipment for hotels',
        'High-quality kitchen equipment',
        'Elegant and diverse hospitality tools',
        'Excellent customer service and technical support',
        'Custom quotations for projects'
      ],
      stats: [
        { number: '500+', label: 'Hotel Projects' },
        { number: '15+', label: 'Years Experience' },
        { number: '1000+', label: 'Diverse Products' },
        { number: '24/7', label: 'Technical Support' }
      ],
      cta: 'Learn More'
    }
  };

  const currentContent = content[locale];

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-96 h-96 bg-primary rounded-full -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-secondary rounded-full translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <div className={`text-center mb-16 transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <i className="ri-building-line"></i>
            <span>{locale === 'ar' ? 'من نحن' : 'About Us'}</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            {currentContent.title}
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {currentContent.subtitle}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className={`transform transition-all duration-1000 delay-200 ${isVisible ? 'translate-x-0 opacity-100' : locale === 'ar' ? 'translate-x-10 opacity-0' : '-translate-x-10 opacity-0'}`}>
            <p className="text-lg text-gray-700 leading-relaxed mb-8">
              {currentContent.description}
            </p>

            {/* Features */}
            <div className="space-y-4 mb-8">
              {currentContent.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <i className="ri-check-line text-primary text-sm"></i>
                  </div>
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>

            {/* CTA Button */}
            <Link
              href={`/${locale}/about`}
              className="inline-flex items-center gap-3 bg-primary hover:bg-primary/90 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
            >
              <span>{currentContent.cta}</span>
              <i className="ri-arrow-right-line"></i>
            </Link>
          </div>

          {/* Stats */}
          <div className={`transform transition-all duration-1000 delay-400 ${isVisible ? 'translate-x-0 opacity-100' : locale === 'ar' ? '-translate-x-10 opacity-0' : 'translate-x-10 opacity-0'}`}>
            <div className="grid grid-cols-2 gap-6">
              {currentContent.stats.map((stat, index) => (
                <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 text-center group">
                  <div className="text-4xl font-bold text-primary mb-2 group-hover:scale-110 transition-transform duration-300">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
