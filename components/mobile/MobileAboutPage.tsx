'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Locale } from '../../lib/i18n';
import { useSiteSettings } from '../../hooks/useSiteSettings';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import LoadingSpinner from '../LoadingSpinner';

interface MobileAboutPageProps {
  locale: Locale;
}

const MobileAboutPage: React.FC<MobileAboutPageProps> = ({ locale }) => {
  const { settings, loading } = useSiteSettings();
  const [isPageLoading, setIsPageLoading] = useState(true);

  // استخدام scroll position hook
  const scrollKey = `about-${locale}`;
  const { restoreScrollPosition } = useScrollPosition(scrollKey);

  // تحميل الصفحة
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsPageLoading(false);
      // استعادة موضع التمرير بعد التحميل
      setTimeout(() => {
        restoreScrollPosition();
      }, 100);
    }, 300);

    return () => clearTimeout(timer);
  }, [restoreScrollPosition]);

  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getAboutContent = () => {
    // القيم الافتراضية في حالة عدم وجود إعدادات
    const defaultContent = {
      ar: {
        title: 'من نحن',
        subtitle: 'تعرف على شركة دروب هجر المتخصصة في توفير أفضل تجهيزات الفنادق والمطاعم',
        description: 'شركة دروب هجر هي شركة رائدة في مجال توفير تجهيزات الفنادق والمطاعم بأعلى معايير الجودة والكفاءة.',
        vision: 'رؤيتنا',
        visionText: 'أن نكون الشركة الرائدة في المملكة العربية السعودية في مجال تجهيزات الفنادق والمطاعم.',
        mission: 'مهمتنا',
        missionText: 'نلتزم بتوفير معدات وتجهيزات عالية الجودة للفنادق والمطاعم.',
        values: 'قيمنا',
        valuesItems: [
          { title: 'الجودة', description: 'نلتزم بأعلى معايير الجودة في جميع منتجاتنا وخدماتنا', icon: 'ri-award-line' },
          { title: 'الابتكار', description: 'نسعى دائماً لتقديم حلول مبتكرة ومتطورة', icon: 'ri-lightbulb-line' },
          { title: 'الثقة', description: 'نبني علاقات طويلة الأمد مع عملائنا قائمة على الثقة والشفافية', icon: 'ri-shield-check-line' },
          { title: 'التميز', description: 'نسعى للتميز في كل ما نقوم به', icon: 'ri-star-line' }
        ],
        team: 'فريقنا',
        teamText: 'يضم فريقنا مجموعة من الخبراء والمتخصصين في مجال تجهيزات الفنادق والمطاعم.',
        contact: 'تواصل معنا',
        contactText: 'نحن هنا لخدمتك ومساعدتك في جميع احتياجاتك'
      },
      en: {
        title: 'About Us',
        subtitle: 'Learn about Droob Hajer, specialized in providing the best hotel and restaurant equipment',
        description: 'Droob Hajer is a leading company in providing hotel and restaurant equipment with the highest standards of quality and efficiency.',
        vision: 'Our Vision',
        visionText: 'To be the leading company in Saudi Arabia in the field of hotel and restaurant equipment.',
        mission: 'Our Mission',
        missionText: 'We are committed to providing high-quality equipment and supplies for hotels and restaurants.',
        values: 'Our Values',
        valuesItems: [
          { title: 'Quality', description: 'We are committed to the highest quality standards in all our products and services', icon: 'ri-award-line' },
          { title: 'Innovation', description: 'We always strive to provide innovative and advanced solutions', icon: 'ri-lightbulb-line' },
          { title: 'Trust', description: 'We build long-term relationships with our customers based on trust and transparency', icon: 'ri-shield-check-line' },
          { title: 'Excellence', description: 'We strive for excellence in everything we do', icon: 'ri-star-line' }
        ],
        team: 'Our Team',
        teamText: 'Our team includes a group of experts and specialists in the field of hotel and restaurant equipment.',
        contact: 'Contact Us',
        contactText: 'We are here to serve you and help you with all your needs'
      }
    };

    // إذا لم تكن الإعدادات متاحة، استخدم القيم الافتراضية
    if (!settings?.aboutSettings) {
      return defaultContent[locale];
    }

    return {
      title: locale === 'ar'
        ? (settings.aboutSettings.heroSection.titleAr || defaultContent[locale].title)
        : (settings.aboutSettings.heroSection.title || defaultContent[locale].title),
      subtitle: locale === 'ar'
        ? (settings.aboutSettings.heroSection.subtitleAr || defaultContent[locale].subtitle)
        : (settings.aboutSettings.heroSection.subtitle || defaultContent[locale].subtitle),
      description: locale === 'ar'
        ? (settings.aboutSettings.description.textAr || defaultContent[locale].description)
        : (settings.aboutSettings.description.text || defaultContent[locale].description),
      vision: locale === 'ar'
        ? (settings.aboutSettings.vision?.titleAr || defaultContent[locale].vision)
        : (settings.aboutSettings.vision?.title || defaultContent[locale].vision),
      visionText: locale === 'ar'
        ? (settings.aboutSettings.vision?.textAr || defaultContent[locale].visionText)
        : (settings.aboutSettings.vision?.text || defaultContent[locale].visionText),
      mission: locale === 'ar'
        ? (settings.aboutSettings.mission?.titleAr || defaultContent[locale].mission)
        : (settings.aboutSettings.mission?.title || defaultContent[locale].mission),
      missionText: locale === 'ar'
        ? (settings.aboutSettings.mission?.textAr || defaultContent[locale].missionText)
        : (settings.aboutSettings.mission?.text || defaultContent[locale].missionText),
      values: locale === 'ar'
        ? (settings.aboutSettings.values?.titleAr || defaultContent[locale].values)
        : (settings.aboutSettings.values?.title || defaultContent[locale].values),
      valuesItems: settings.aboutSettings.values?.items && settings.aboutSettings.values.items.length > 0
        ? settings.aboutSettings.values.items.map(item => ({
            title: locale === 'ar' ? item.titleAr : item.titleEn,
            description: locale === 'ar' ? item.descriptionAr : item.descriptionEn,
            icon: item.icon
          }))
        : defaultContent[locale].valuesItems,
      team: locale === 'ar'
        ? (settings.aboutSettings.team?.titleAr || defaultContent[locale].team)
        : (settings.aboutSettings.team?.title || defaultContent[locale].team),
      teamText: locale === 'ar'
        ? (settings.aboutSettings.team?.descriptionAr || defaultContent[locale].teamText)
        : (settings.aboutSettings.team?.description || defaultContent[locale].teamText),
      contact: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
      contactText: locale === 'ar'
        ? (settings.aboutSettings.contactCTA?.descriptionAr || defaultContent[locale].contactText)
        : (settings.aboutSettings.contactCTA?.description || defaultContent[locale].contactText)
    };
  };

  const currentContent = getAboutContent();

  if (loading || isPageLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={locale === 'ar' ? 'من نحن' : 'About Us'}
          showBackButton={true}
          backUrl={`/${locale}`}
          useHistory={true}
        />
        <LoadingSpinner
          size="lg"
          text={locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          fullScreen={false}
          className="py-16"
        />
        <MobileBottomNav locale={locale} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={currentContent.title}
        showBackButton={true}
        backUrl={`/${locale}`}
      />

      {/* Content */}
      <div className="px-4 py-4 space-y-6">
        {/* Hero Section */}
        <div className="bg-gradient-to-br from-primary to-primary/80 rounded-2xl p-6 text-white">
          <h1 className="text-2xl font-bold mb-3">
            {currentContent.title}
          </h1>
          <p className="text-white/90 text-sm leading-relaxed">
            {currentContent.subtitle}
          </p>
        </div>

        {/* Description */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <p className="text-gray-700 text-sm leading-relaxed">
            {currentContent.description}
          </p>
        </div>

        {/* Vision & Mission */}
        <div className="space-y-4">
          <div className="bg-white rounded-xl p-6 shadow-sm">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <i className="ri-eye-line text-blue-600"></i>
              </div>
              <h2 className="text-lg font-bold text-gray-800">{currentContent.vision}</h2>
            </div>
            <p className="text-gray-700 text-sm leading-relaxed">
              {currentContent.visionText}
            </p>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-sm">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <i className="ri-target-line text-green-600"></i>
              </div>
              <h2 className="text-lg font-bold text-gray-800">{currentContent.mission}</h2>
            </div>
            <p className="text-gray-700 text-sm leading-relaxed">
              {currentContent.missionText}
            </p>
          </div>
        </div>

        {/* Values */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <h2 className="text-lg font-bold text-gray-800 mb-4">{currentContent.values}</h2>
          <div className="space-y-4">
            {currentContent.valuesItems.map((value, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <i className={`${value.icon} text-primary text-sm`}></i>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-800 text-sm mb-1">
                    {value.title}
                  </h3>
                  <p className="text-gray-600 text-xs leading-relaxed">
                    {value.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Team */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <i className="ri-team-line text-purple-600"></i>
            </div>
            <h2 className="text-lg font-bold text-gray-800">{currentContent.team}</h2>
          </div>
          <p className="text-gray-700 text-sm leading-relaxed">
            {currentContent.teamText}
          </p>
        </div>

        {/* Contact CTA */}
        <div className="bg-gradient-to-br from-primary to-primary/80 rounded-2xl p-6 text-white">
          <div className="text-center">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <i className="ri-phone-line text-white text-xl"></i>
            </div>
            <h2 className="text-lg font-bold mb-2">
              {currentContent.contact}
            </h2>
            <p className="text-white/90 text-sm mb-4">
              {currentContent.contactText}
            </p>
            <Link
              href={`/${locale}/contact`}
              className="bg-white text-primary px-6 py-3 rounded-lg font-semibold text-sm inline-flex items-center gap-2"
            >
              <i className="ri-message-line"></i>
              {currentContent.contact}
            </Link>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />
    </div>
  );
};

export default MobileAboutPage;
