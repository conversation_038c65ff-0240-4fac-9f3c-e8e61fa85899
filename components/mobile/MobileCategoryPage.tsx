'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../../lib/i18n';
import { Category, Subcategory } from '../../types/mysql-database';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import LoadingSpinner from '../LoadingSpinner';

interface MobileCategoryPageProps {
  locale: Locale;
  categoryId: string;
  initialCategory?: Category | null;
  initialSubcategories?: Subcategory[];
}

const MobileCategoryPage: React.FC<MobileCategoryPageProps> = ({
  locale,
  categoryId,
  initialCategory,
  initialSubcategories
}) => {
  const [category, setCategory] = useState<Category | null>(initialCategory || null);
  const [subcategories, setSubcategories] = useState<Subcategory[]>(initialSubcategories || []);
  const [loading, setLoading] = useState(!initialCategory);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  // استخدام scroll position hook
  const scrollKey = `category-${categoryId}-${locale}`;
  useScrollPosition(scrollKey);

  // جلب البيانات إذا لم تكن متوفرة
  const fetchCategoryData = useCallback(async () => {
    try {
      setLoading(true);

      // جلب بيانات الفئة
      const categoryResponse = await fetch(`/api/categories?id=${categoryId}`);
      if (categoryResponse.ok) {
        const categoryResult = await categoryResponse.json();
        if (categoryResult.success && categoryResult.data) {
          setCategory(categoryResult.data);
        }
      }

      // جلب الفئات الفرعية
      const subcategoriesResponse = await fetch(`/api/subcategories?categoryId=${categoryId}`);
      if (subcategoriesResponse.ok) {
        const subcategoriesResult = await subcategoriesResponse.json();
        if (subcategoriesResult.success && subcategoriesResult.data) {
          setSubcategories(subcategoriesResult.data);
        }
      }
    } catch (error) {
      console.error('Error fetching category data:', error);
    } finally {
      setLoading(false);
    }
  }, [categoryId]);

  // جلب البيانات إذا لم تكن متوفرة
  useEffect(() => {
    if (!initialCategory && categoryId) {
      fetchCategoryData();
    }
  }, [categoryId, initialCategory, fetchCategoryData]);

  // تصفية الفئات الفرعية حسب البحث
  const filteredSubcategories = subcategories.filter(subcategory => {
    if (!searchQuery) return true;
    const name = locale === 'ar' ? subcategory.name_ar : subcategory.name;
    const description = locale === 'ar' ? subcategory.description_ar : subcategory.description;
    return name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           (description && description.toLowerCase().includes(searchQuery.toLowerCase()));
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!category) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={locale === 'ar' ? 'خطأ' : 'Error'}
          showBackButton={true}
          backUrl={`/${locale}/categories`}
        />
        <div className="flex items-center justify-center min-h-[60vh] px-4">
          <div className="text-center">
            <i className="ri-error-warning-line text-6xl text-red-400 mb-4"></i>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              {locale === 'ar' ? 'الفئة غير موجودة' : 'Category not found'}
            </h3>
            <p className="text-gray-600 mb-6">
              {locale === 'ar'
                ? 'لم يتم العثور على هذه الفئة'
                : 'This category could not be found'
              }
            </p>
            <Link
              href={`/${locale}/categories`}
              className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg font-medium"
            >
              <i className="ri-arrow-left-line"></i>
              <span>{locale === 'ar' ? 'العودة للفئات' : 'Back to Categories'}</span>
            </Link>
          </div>
        </div>
        <MobileBottomNav locale={locale} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={locale === 'ar' ? category.name_ar : category.name}
        subtitle={`${filteredSubcategories.length} ${locale === 'ar' ? 'فئة فرعية' : 'subcategories'}`}
        showBackButton={true}
        backUrl={`/${locale}/categories`}
        customActions={
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
          >
            <i className="ri-search-line text-gray-600"></i>
          </button>
        }
      />

      {/* Search Bar */}
      {showSearch && (
        <div className="bg-white border-b border-gray-100 px-4 py-3">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder={locale === 'ar' ? 'البحث في الفئات الفرعية...' : 'Search subcategories...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line"></i>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Category Description */}
      {category.description_ar && (
        <div className="bg-white border-b border-gray-100 px-4 py-4">
          <p className="text-gray-600 text-sm leading-relaxed">
            {locale === 'ar' ? category.description_ar : category.description}
          </p>
        </div>
      )}

      {/* Subcategories Grid */}
      <div className="px-4 py-4">
        {filteredSubcategories.length > 0 ? (
          <div className="grid grid-cols-2 gap-3">
            {filteredSubcategories.map((subcategory) => (
              <Link
                key={subcategory.id}
                href={`/${locale}/subcategory/${subcategory.id}`}
                className="bg-white rounded-xl shadow-sm overflow-hidden active:scale-95 transition-all duration-200"
              >
                {/* Subcategory Image */}
                <div className="aspect-square bg-gray-100 relative">
                  {subcategory.image_url ? (
                    <Image
                      src={subcategory.image_url}
                      alt={locale === 'ar' ? subcategory.name_ar : subcategory.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <i className="ri-folder-line text-gray-400 text-2xl"></i>
                    </div>
                  )}
                </div>

                {/* Subcategory Info */}
                <div className="p-3">
                  <h3 className="font-semibold text-gray-800 text-sm mb-1 line-clamp-2">
                    {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                  </h3>
                  {subcategory.description_ar && (
                    <p className="text-gray-500 text-xs line-clamp-2">
                      {locale === 'ar' ? subcategory.description_ar : subcategory.description}
                    </p>
                  )}
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-400">
                      {locale === 'ar' ? 'عرض المنتجات' : 'View Products'}
                    </span>
                    <i className="ri-arrow-right-line text-primary text-sm"></i>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <i className="ri-folder-line text-6xl text-gray-400 mb-4"></i>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              {searchQuery
                ? (locale === 'ar' ? 'لا توجد نتائج' : 'No results found')
                : (locale === 'ar' ? 'لا توجد فئات فرعية' : 'No subcategories')
              }
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery
                ? (locale === 'ar'
                    ? 'جرب البحث بكلمات مختلفة'
                    : 'Try searching with different keywords'
                  )
                : (locale === 'ar'
                    ? 'هذه الفئة لا تحتوي على فئات فرعية'
                    : 'This category has no subcategories'
                  )
              }
            </p>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="bg-primary text-white px-6 py-3 rounded-lg font-medium"
              >
                {locale === 'ar' ? 'مسح البحث' : 'Clear Search'}
              </button>
            )}
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />
    </div>
  );
};

export default MobileCategoryPage;
