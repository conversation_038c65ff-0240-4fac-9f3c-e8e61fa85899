'use client';

import React, { useEffect } from 'react';

interface MobileToastProps {
  message: string;
  type: 'success' | 'error' | 'info';
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
}

const MobileToast: React.FC<MobileToastProps> = ({
  message,
  type,
  isVisible,
  onClose,
  duration = 3000
}) => {
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);

  if (!isVisible) return null;

  const getToastStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500 text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'info':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-800 text-white';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return 'ri-check-line';
      case 'error':
        return 'ri-error-warning-line';
      case 'info':
        return 'ri-information-line';
      default:
        return 'ri-notification-line';
    }
  };

  return (
    <div className="fixed top-20 left-4 right-4 z-50 animate-slideInUp">
      <div className={`${getToastStyles()} rounded-lg shadow-lg p-4 flex items-center gap-3`}>
        <i className={`${getIcon()} text-xl`}></i>
        <span className="flex-1 text-sm font-medium">{message}</span>
        <button
          onClick={onClose}
          className="text-white/80 hover:text-white transition-colors"
        >
          <i className="ri-close-line text-lg"></i>
        </button>
      </div>
    </div>
  );
};

export default MobileToast;
