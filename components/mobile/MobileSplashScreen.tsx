'use client';

import React, { useState, useEffect } from 'react';

interface MobileSplashScreenProps {
  onComplete?: () => void;
  duration?: number;
  locale?: 'ar' | 'en';
}

const MobileSplashScreen: React.FC<MobileSplashScreenProps> = ({
  onComplete,
  duration = 3500,
  locale = 'ar'
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [currentPhase, setCurrentPhase] = useState(0); // 0: logo, 1: text, 2: complete

  const texts = {
    ar: {
      welcome: 'مرحباً بكم في',
      brandName: 'دروب هجر',
      tagline: 'التجهيزات الفندقية الاحترافية',
      subtitle: 'حلول الضيافة المتطورة',
      loading: 'جاري التحميل',
      excellence: 'التميز في كل التفاصيل',
      steps: ['تحميل البيانات', 'إعداد التطبيق', 'تجهيز المحتوى', 'مرحباً بك!']
    },
    en: {
      welcome: 'Welcome to',
      brandName: 'DROOB HAJER',
      tagline: 'Professional Hotel Equipment',
      subtitle: 'Advanced Hospitality Solutions',
      loading: 'Loading',
      excellence: 'Excellence in Every Detail',
      steps: ['Loading data', 'Setting up app', 'Preparing content', 'Welcome!']
    }
  };

  const t = texts[locale];

  useEffect(() => {
    // Phase transitions for mobile
    const phaseTimer1 = setTimeout(() => setCurrentPhase(1), 600);
    const phaseTimer2 = setTimeout(() => setCurrentPhase(2), 1200);

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
              onComplete?.();
            }, 600);
          }, 400);
          return 100;
        }
        return prev + 1.8;
      });
    }, duration / 56);

    return () => {
      clearInterval(interval);
      clearTimeout(phaseTimer1);
      clearTimeout(phaseTimer2);
    };
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex flex-col items-center justify-center overflow-hidden">
      {/* Modern Mobile Gradient Background */}
      <div
        className="absolute inset-0 transition-all duration-1000"
        style={{
          background: `
            radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(147, 51, 234, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(236, 72, 153, 0.3) 0%, transparent 50%),
            linear-gradient(135deg, #0c0a1e 0%, #1a1b3a 25%, #2d1b69 50%, #4c1d95 75%, #7c3aed 100%)
          `
        }}
      />

      {/* Mobile Animated Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Simplified geometric shapes for mobile */}
        <div className="absolute top-1/4 left-1/4 w-24 h-24 border border-white/15 rounded-2xl rotate-45 animate-float opacity-25" style={{animationDelay: '0s'}} />
        <div className="absolute top-1/3 right-1/4 w-20 h-20 border border-blue-300/25 rounded-full animate-float opacity-30" style={{animationDelay: '1s'}} />
        <div className="absolute bottom-1/4 left-1/3 w-16 h-16 border border-purple-300/20 rounded-xl animate-float opacity-25" style={{animationDelay: '1.5s'}} />

        {/* Mobile gradient orbs */}
        <div className="absolute top-16 right-16 w-32 h-32 bg-gradient-to-r from-blue-500/25 to-purple-500/25 rounded-full blur-2xl animate-pulse" />
        <div className="absolute bottom-16 left-16 w-28 h-28 bg-gradient-to-r from-pink-500/25 to-blue-500/25 rounded-full blur-xl animate-pulse delay-1000" />
      </div>

      {/* Modern Mobile Equipment Icons */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Glassmorphism equipment icons for mobile */}
        <div className="absolute top-12 left-6 w-10 h-10 bg-white/15 backdrop-blur-xl rounded-xl flex items-center justify-center text-white/50 text-lg animate-float border border-white/25 shadow-xl" style={{animationDelay: '0s'}}>
          🍽️
        </div>
        <div className="absolute top-16 right-6 w-9 h-9 bg-white/15 backdrop-blur-xl rounded-lg flex items-center justify-center text-white/50 text-base animate-float border border-white/25 shadow-lg" style={{animationDelay: '1.2s'}}>
          🏨
        </div>
        <div className="absolute bottom-16 left-6 w-9 h-9 bg-white/15 backdrop-blur-xl rounded-xl flex items-center justify-center text-white/50 text-base animate-float border border-white/25 shadow-lg" style={{animationDelay: '2s'}}>
          ⚡
        </div>
        <div className="absolute bottom-12 right-6 w-10 h-10 bg-white/15 backdrop-blur-xl rounded-lg flex items-center justify-center text-white/50 text-lg animate-float border border-white/25 shadow-xl" style={{animationDelay: '0.7s'}}>
          ✨
        </div>

        {/* Premium floating particles for mobile */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full opacity-35 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${4 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-6 max-w-sm mx-auto">
        {/* Modern Mobile Logo Container */}
        <div className={`relative mb-10 transition-all duration-1000 ${currentPhase >= 0 ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-6 scale-95'}`}>
          {/* Multi-layer glow effects for mobile */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/25 to-purple-500/25 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-pink-500/15 to-blue-500/15 rounded-full blur-xl animate-pulse delay-1000"></div>

          {/* Mobile Logo */}
          <div className="relative w-28 h-28 mx-auto">
            {/* Glassmorphism background for mobile */}
            <div className="absolute inset-0 bg-white/20 backdrop-blur-2xl rounded-2xl border border-white/30 shadow-2xl"></div>

            {/* Logo content */}
            <div className="relative w-full h-full flex items-center justify-center">
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-1 tracking-wider font-sans">
                  <span className="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-xl">
                    DH
                  </span>
                </div>
                <div className="text-xs text-white/70 font-medium tracking-[0.2em] uppercase">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </div>
              </div>
            </div>

            {/* Mobile rotating border */}
            <div className="absolute inset-0 rounded-2xl border border-gradient-to-r from-blue-400/50 via-purple-400/50 to-pink-400/50 animate-spin-slow"></div>

            {/* Mobile corner accents */}
            <div className="absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse shadow-lg"></div>
            <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-gradient-to-r from-pink-400 to-blue-400 rounded-full animate-pulse delay-500 shadow-md"></div>
          </div>
        </div>

        {/* Modern Brand Text - Mobile Optimized */}
        <div className={`space-y-4 mb-8 transition-all duration-1000 delay-400 ${currentPhase >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
          <div className="space-y-3">
            <p className="text-white/80 text-base font-light tracking-wide">
              {t.welcome}
            </p>
            <h1 className="text-white text-3xl font-bold tracking-tight leading-tight">
              <span className="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-xl">
                {t.brandName}
              </span>
            </h1>
            <div className="space-y-2">
              <p className="text-white/90 text-base font-medium tracking-wide">
                {t.tagline}
              </p>
              <p className="text-white/70 text-sm font-light tracking-wide">
                {t.excellence}
              </p>
            </div>
          </div>
        </div>

        {/* Modern Mobile Progress Section */}
        <div className={`space-y-5 transition-all duration-1000 delay-800 ${currentPhase >= 2 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
          {/* Modern Progress Bar for Mobile */}
          <div className="relative w-full max-w-xs mx-auto">
            <div className="w-full h-1.5 bg-white/15 rounded-full overflow-hidden backdrop-blur-sm border border-white/25 shadow-lg">
              <div
                className="h-full bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full transition-all duration-500 ease-out shadow-xl relative"
                style={{ width: `${progress}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full animate-pulse"></div>
              </div>
            </div>

            {/* Progress glow for mobile */}
            <div
              className="absolute top-0 h-1.5 bg-gradient-to-r from-blue-400/50 via-purple-400/50 to-pink-400/50 rounded-full blur-sm transition-all duration-500"
              style={{ width: `${progress}%` }}
            />
          </div>

          {/* Mobile Loading Animation */}
          <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse">
            <div className="flex space-x-1.5 rtl:space-x-reverse">
              <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce shadow-md"></div>
              <div className="w-1.5 h-1.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-bounce delay-100 shadow-md"></div>
              <div className="w-1.5 h-1.5 bg-gradient-to-r from-pink-400 to-blue-400 rounded-full animate-bounce delay-200 shadow-md"></div>
            </div>
            <span className="text-white/80 text-sm font-light tracking-wide ml-3 rtl:mr-3">
              {t.loading}
            </span>
          </div>

          {/* Mobile Progress Percentage */}
          <div className="text-center">
            <span className="text-white/60 text-xs font-light tracking-[0.15em]">
              {Math.round(progress)}%
            </span>
          </div>
        </div>
      </div>

      {/* Modern Mobile Bottom Safe Area */}
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-black/40 via-black/20 to-transparent safe-area-pb"></div>

      {/* Modern Mobile Top Safe Area */}
      <div className="absolute top-0 left-0 right-0 h-16 bg-gradient-to-b from-black/30 via-black/15 to-transparent safe-area-pt"></div>

      {/* Subtle grid pattern for mobile */}
      <div
        className="absolute inset-0 opacity-[0.02]"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px'
        }}
      />
    </div>
  );
};

export default MobileSplashScreen;
