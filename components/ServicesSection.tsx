'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Locale } from '../lib/i18n';

interface ServicesSectionProps {
  locale: Locale;
}

const ServicesSection: React.FC<ServicesSectionProps> = ({ locale }) => {
  const [whatsappNumber, setWhatsappNumber] = useState('+966501234567');
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredService, setHoveredService] = useState<number | null>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    // قراءة رقم الواتساب من الإعدادات
    const savedSettings = localStorage.getItem('siteSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        // استخدام فقط إعدادات التواصل الجديدة، تجاهل الرقم القديم
        if (settings.communicationSettings?.whatsapp?.businessNumber) {
          setWhatsappNumber(settings.communicationSettings.whatsapp.businessNumber);
        }
      } catch {
        console.log('Using default WhatsApp number');
      }
    }
  }, []);

  // تتبع ظهور القسم للأنيميشن
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);
  const services = [
    {
      icon: 'ri-file-list-3-line',
      title: locale === 'ar' ? 'عروض أسعار مخصصة' : 'Custom Quotations',
      description: locale === 'ar'
        ? 'نقدم عروض أسعار مفصلة ومخصصة لجميع احتياجات مشروعك الفندقي'
        : 'We provide detailed and customized quotations for all your hotel project needs',
      features: [
        locale === 'ar' ? 'تقييم شامل للمشروع' : 'Comprehensive project assessment',
        locale === 'ar' ? 'عروض أسعار تنافسية' : 'Competitive pricing',
        locale === 'ar' ? 'استشارة مجانية' : 'Free consultation'
      ],
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      hoverColor: 'hover:bg-blue-500'
    },
    {
      icon: 'ri-building-line',
      title: locale === 'ar' ? 'تجهيز المشاريع الفندقية' : 'Hotel Project Equipment',
      description: locale === 'ar'
        ? 'تجهيز شامل للفنادق والمنتجعات والمطاعم بأعلى معايير الجودة'
        : 'Comprehensive equipment for hotels, resorts and restaurants with highest quality standards',
      features: [
        locale === 'ar' ? 'تجهيز كامل للغرف' : 'Complete room setup',
        locale === 'ar' ? 'أثاث فندقي فاخر' : 'Luxury hotel furniture',
        locale === 'ar' ? 'معدات مطابخ احترافية' : 'Professional kitchen equipment'
      ],
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      hoverColor: 'hover:bg-green-500'
    },
    {
      icon: 'ri-customer-service-2-line',
      title: locale === 'ar' ? 'الاستشارة المجانية' : 'Free Consultation',
      description: locale === 'ar'
        ? 'فريق من الخبراء لتقديم الاستشارة المجانية وتصميم الحلول المناسبة'
        : 'Team of experts to provide free consultation and design suitable solutions',
      features: [
        locale === 'ar' ? 'استشارة مجانية' : 'Free consultation',
        locale === 'ar' ? 'تصميم مخصص' : 'Custom design',
        locale === 'ar' ? 'خبرة 15+ سنة' : '15+ years experience'
      ],
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      hoverColor: 'hover:bg-purple-500'
    },
    {
      icon: 'ri-team-line',
      title: locale === 'ar' ? 'خدمة العملاء المتخصصة' : 'Specialized Customer Service',
      description: locale === 'ar'
        ? 'فريق متخصص في مجال الضيافة لخدمة المستثمرين وشركات التصميم'
        : 'Specialized team in hospitality field serving investors and design companies',
      features: [
        locale === 'ar' ? 'استشارة متخصصة' : 'Specialized consultation',
        locale === 'ar' ? 'متابعة المشاريع' : 'Project follow-up',
        locale === 'ar' ? 'دعم مستمر' : 'Continuous support'
      ],
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      hoverColor: 'hover:bg-orange-500'
    }
  ];

  const content = {
    ar: {
      title: 'خدماتنا المتميزة',
      subtitle: 'نقدم مجموعة شاملة من الخدمات لضمان رضاكم التام',
      cta: 'اطلب خدمة',
      learnMore: 'اعرف المزيد'
    },
    en: {
      title: 'Our Distinguished Services',
      subtitle: 'We provide a comprehensive range of services to ensure your complete satisfaction',
      cta: 'Request Service',
      learnMore: 'Learn More'
    }
  };

  const currentContent = content[locale];

  return (
    <section
      ref={sectionRef}
      className="relative py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 overflow-hidden"
    >
      {/* خلفية متحركة */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-72 h-72 bg-primary rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div className="absolute top-10 right-10 w-72 h-72 bg-secondary rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-primary rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header المحسن */}
        <div className={`text-center mb-20 transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <div className="inline-flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <i className="ri-service-line"></i>
            <span>{locale === 'ar' ? 'خدماتنا المتميزة' : 'Our Services'}</span>
          </div>

          <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6 leading-tight">
            {currentContent.title}
          </h2>

          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {currentContent.subtitle}
          </p>

          {/* خط زخرفي */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-1 w-20 bg-gradient-to-r from-transparent to-primary rounded-full"></div>
            <div className="w-3 h-3 bg-primary rounded-full mx-4"></div>
            <div className="h-1 w-20 bg-gradient-to-l from-transparent to-primary rounded-full"></div>
          </div>
        </div>

        {/* Services Grid المحسن */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className={`group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 border border-gray-100 hover:border-transparent overflow-hidden ${isVisible ? 'animate-fadeInUp' : 'opacity-0'}`}
              style={{ animationDelay: `${index * 200}ms` }}
              onMouseEnter={() => setHoveredService(index)}
              onMouseLeave={() => setHoveredService(null)}
            >
              {/* خلفية متدرجة عند الهوفر */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

              {/* تأثير الضوء */}
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-primary to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Icon المحسن */}
              <div className={`relative w-20 h-20 ${service.bgColor} rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 ${service.hoverColor} transition-all duration-500 shadow-lg group-hover:shadow-xl`}>
                <i className={`${service.icon} text-4xl text-gray-700 group-hover:text-white transition-colors duration-500`}></i>

                {/* تأثير النبضة */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-20 animate-pulse`}></div>
              </div>

              {/* Content المحسن */}
              <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-gray-900 transition-colors duration-300 leading-tight">
                {service.title}
              </h3>

              <p className="text-gray-600 mb-8 leading-relaxed text-base group-hover:text-gray-700 transition-colors duration-300">
                {service.description}
              </p>

              {/* Features المحسنة */}
              <ul className="space-y-3 mb-8">
                {service.features.map((feature, featureIndex) => (
                  <li
                    key={featureIndex}
                    className={`flex items-center text-sm text-gray-600 group-hover:text-gray-700 transition-all duration-300 transform ${hoveredService === index ? 'translate-x-2' : ''}`}
                    style={{ transitionDelay: `${featureIndex * 100}ms` }}
                  >
                    <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors duration-300">
                      <i className="ri-check-line text-green-600 text-xs"></i>
                    </div>
                    <span className="font-medium">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button المحسن */}
              <a
                href={`/${locale}/contact`}
                className={`inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r ${service.color} text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105 group-hover:gap-4`}
              >
                <span>{currentContent.learnMore}</span>
                <i className="ri-arrow-right-line transition-transform duration-300 group-hover:translate-x-1"></i>
              </a>

              {/* رقم الخدمة */}
              <div className="absolute top-6 right-6 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-400 font-bold text-sm group-hover:bg-primary group-hover:text-white transition-all duration-300">
                {index + 1}
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA المحسن */}
        <div className={`text-center mt-20 transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <div className="relative bg-gradient-to-br from-primary via-primary to-secondary rounded-3xl p-12 text-white shadow-2xl overflow-hidden">
            {/* خلفية متحركة */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20">
              <div className="absolute top-0 left-0 w-full h-full opacity-10">
                <div className="w-4 h-4 bg-white rounded-full absolute top-10 left-10 animate-pulse"></div>
                <div className="w-2 h-2 bg-white rounded-full absolute top-20 right-20 animate-pulse animation-delay-1000"></div>
                <div className="w-3 h-3 bg-white rounded-full absolute bottom-20 left-20 animate-pulse animation-delay-2000"></div>
                <div className="w-2 h-2 bg-white rounded-full absolute bottom-10 right-10 animate-pulse animation-delay-3000"></div>
              </div>
            </div>

            <div className="relative z-10">
              {/* أيقونة مركزية */}
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-8 backdrop-blur-sm">
                <i className="ri-customer-service-2-line text-4xl text-white"></i>
              </div>

              <h3 className="text-4xl font-bold mb-6 leading-tight">
                {locale === 'ar'
                  ? 'هل تحتاج إلى خدمة مخصصة؟'
                  : 'Need a custom service?'
                }
              </h3>

              <p className="text-white/90 mb-10 max-w-3xl mx-auto text-lg leading-relaxed">
                {locale === 'ar'
                  ? 'تواصل معنا للحصول على حلول مخصصة تناسب احتياجاتك الخاصة. فريقنا من الخبراء جاهز لمساعدتك على مدار الساعة'
                  : 'Contact us to get custom solutions that suit your specific needs. Our team of experts is ready to help you around the clock'
                }
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a
                  href={`/${locale}/contact`}
                  className="group bg-white text-primary px-10 py-4 rounded-2xl font-bold hover:bg-gray-100 transition-all duration-300 inline-flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <i className="ri-phone-line text-xl group-hover:animate-pulse"></i>
                  <span>{currentContent.cta}</span>
                  <i className="ri-arrow-right-line transition-transform duration-300 group-hover:translate-x-1"></i>
                </a>

                <a
                  href={`https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن خدماتكم المتميزة' : 'Hello, I would like to inquire about your distinguished services')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group border-2 border-white text-white hover:bg-white hover:text-primary px-10 py-4 rounded-2xl font-bold transition-all duration-300 inline-flex items-center justify-center gap-3 backdrop-blur-sm hover:shadow-xl transform hover:scale-105"
                >
                  <i className="ri-whatsapp-line text-xl group-hover:animate-bounce"></i>
                  <span>{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
                  <i className="ri-external-link-line transition-transform duration-300 group-hover:translate-x-1"></i>
                </a>
              </div>

              {/* إحصائيات سريعة */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12 pt-8 border-t border-white/20">
                <div className="text-center">
                  <div className="text-3xl font-bold mb-2">500+</div>
                  <div className="text-white/80 text-sm">
                    {locale === 'ar' ? 'عميل راضٍ' : 'Happy Clients'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold mb-2">15+</div>
                  <div className="text-white/80 text-sm">
                    {locale === 'ar' ? 'سنة خبرة' : 'Years Experience'}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold mb-2">24/7</div>
                  <div className="text-white/80 text-sm">
                    {locale === 'ar' ? 'دعم فني' : 'Technical Support'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
