import Head from 'next/head'

interface FaviconMetaProps {
  title?: string
  description?: string
  url?: string
  image?: string
}

export default function FaviconMeta({ 
  title = "DROOB HAJER - معدات الضيافة",
  description = "موقع متخصص في معدات المطاعم والفنادق وأدوات الضيافة",
  url = "https://droobhajer.com",
  image = "https://droobhajer.com/android-icon-192x192.png"
}: FaviconMetaProps) {
  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta charSet="utf-8" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
      
      {/* Apple Touch Icons */}
      <link rel="apple-touch-icon" sizes="57x57" href="/apple-icon-57x57.png" />
      <link rel="apple-touch-icon" sizes="60x60" href="/apple-icon-60x60.png" />
      <link rel="apple-touch-icon" sizes="72x72" href="/apple-icon-72x72.png" />
      <link rel="apple-touch-icon" sizes="76x76" href="/apple-icon-76x76.png" />
      <link rel="apple-touch-icon" sizes="114x114" href="/apple-icon-114x114.png" />
      <link rel="apple-touch-icon" sizes="120x120" href="/apple-icon-120x120.png" />
      <link rel="apple-touch-icon" sizes="144x144" href="/apple-icon-144x144.png" />
      <link rel="apple-touch-icon" sizes="152x152" href="/apple-icon-152x152.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-icon-180x180.png" />
      <link rel="apple-touch-icon-precomposed" href="/apple-icon-precomposed.png" />
      
      {/* Android Chrome Icons */}
      <link rel="icon" type="image/png" sizes="36x36" href="/android-icon-36x36.png" />
      <link rel="icon" type="image/png" sizes="48x48" href="/android-icon-48x48.png" />
      <link rel="icon" type="image/png" sizes="72x72" href="/android-icon-72x72.png" />
      <link rel="icon" type="image/png" sizes="96x96" href="/android-icon-96x96.png" />
      <link rel="icon" type="image/png" sizes="144x144" href="/android-icon-144x144.png" />
      <link rel="icon" type="image/png" sizes="192x192" href="/android-icon-192x192.png" />
      
      {/* Microsoft Tiles */}
      <meta name="msapplication-TileColor" content="#3B82F6" />
      <meta name="msapplication-TileImage" content="/ms-icon-144x144.png" />
      <meta name="msapplication-config" content="/browserconfig.xml" />
      
      {/* Theme Colors */}
      <meta name="theme-color" content="#3B82F6" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-title" content="DROOB HAJER" />
      
      {/* Web App Manifest */}
      <link rel="manifest" href="/manifest.json" />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={image} />
      <meta property="og:image:width" content="192" />
      <meta property="og:image:height" content="192" />
      <meta property="og:site_name" content="DROOB HAJER" />
      <meta property="og:locale" content="ar_SA" />
      <meta property="og:locale:alternate" content="en_US" />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:site" content="@droobhajer" />
      <meta name="twitter:creator" content="@droobhajer" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <meta name="bingbot" content="index, follow" />
      <meta name="author" content="DROOB HAJER" />
      <meta name="publisher" content="DROOB HAJER" />
      <meta name="copyright" content="© 2025 DROOB HAJER. All rights reserved." />
      
      {/* Language and Direction */}
      <meta httpEquiv="content-language" content="ar" />
      <meta name="language" content="Arabic" />
      <meta name="geo.region" content="SA" />
      <meta name="geo.country" content="Saudi Arabia" />
      
      {/* Preconnect for Performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://cdn.jsdelivr.net" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />
      
      {/* Structured Data for Organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "DROOB HAJER",
            "alternateName": "دروب هاجر",
            "url": "https://droobhajer.com",
            "logo": "https://droobhajer.com/android-icon-192x192.png",
            "description": "موقع متخصص في معدات المطاعم والفنادق وأدوات الضيافة",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "SA",
              "addressRegion": "Saudi Arabia"
            },
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+966 *********",
              "contactType": "customer service",
              "availableLanguage": ["Arabic", "English"]
            },
            "sameAs": [
              "https://facebook.com/droobhajer",
              "https://instagram.com/droobhajer",
              "https://twitter.com/droobhajer",
              "https://linkedin.com/company/droobhajer",
              "https://youtube.com/@droobhajer"
            ]
          })
        }}
      />
    </Head>
  )
}
