import { Metadata } from 'next';
import { Locale } from '@/lib/i18n';
import { getCitySpecificKeywords, getSubcategoryKeywords } from '@/lib/main-keywords';

interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  category_id?: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  products_count?: number;
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
}

interface SubcategorySEOProps {
  subcategory: Subcategory;
  category?: Category;
  locale: Locale;
  city?: string;
}

// دالة لإنشاء كلمات مفتاحية محسنة للفئات الفرعية
function generateSubcategoryKeywords(
  subcategory: Subcategory,
  category: Category | undefined,
  locale: Locale
): string[] {
  const subcategoryName = locale === 'ar' ? subcategory.name_ar : subcategory.name;
  const categoryName = category ? (locale === 'ar' ? category.name_ar : category.name) : '';

  // استخدام الدالة المحسنة من ملف الكلمات المفتاحية
  return getSubcategoryKeywords(subcategoryName, categoryName, locale);
}

// دالة لإنشاء وصف محسن للفئة الفرعية
function generateSubcategoryDescription(
  subcategory: Subcategory,
  category: Category | undefined,
  locale: Locale
): string {
  const subcategoryName = locale === 'ar' ? subcategory.name_ar : subcategory.name;
  const categoryName = category ? (locale === 'ar' ? category.name_ar : category.name) : '';
  const subcategoryDescription = locale === 'ar' ? subcategory.description_ar : subcategory.description;
  
  // إذا كان هناك وصف موجود، نحسنه
  if (subcategoryDescription) {
    return locale === 'ar'
      ? `استكشف مجموعة ${subcategoryName} الشاملة ${categoryName ? 'من ' + categoryName : ''} في دروب هجر. ${subcategoryDescription} نوفر تجهيزات فندقية عالية الجودة ومعدات ضيافة احترافية للفنادق والمطاعم والمنتجعات. احصل على عروض أسعار مخصصة لمشروعك.`
      : `Explore our comprehensive ${subcategoryName} collection ${categoryName ? 'from ' + categoryName : ''} at DROOB HAJER. ${subcategoryDescription} We provide high-quality hotel equipment and professional hospitality supplies for hotels, restaurants, and resorts. Get custom quotes for your project.`;
  }
  
  // وصف افتراضي محسن للبورسلين
  if (subcategoryName.toLowerCase().includes('chinaware') || subcategoryName.includes('البورسلين')) {
    return locale === 'ar'
      ? `اكتشف مجموعة أدوات البورسلين الفاخرة والأنيقة في دروب هجر. نوفر أطباق بورسلين عالية الجودة، فناجين وصحون، أطباق تقديم، وأدوات مائدة بورسلين احترافية للفنادق والمطاعم الراقية. تجهيزات بورسلين متينة ومقاومة للحرارة مع تصاميم أنيقة تناسب جميع مناسبات الضيافة الفندقية.`
      : `Discover our luxury and elegant chinaware collection at DROOB HAJER. We provide high-quality porcelain plates, cups and saucers, serving platters, and professional porcelain tableware for luxury hotels and fine dining restaurants. Durable and heat-resistant porcelain equipment with elegant designs suitable for all hotel hospitality occasions.`;
  }
  
  // وصف افتراضي عام
  return locale === 'ar'
    ? `استكشف مجموعة ${subcategoryName} المتميزة ${categoryName ? 'من ' + categoryName : ''} في دروب هجر. نقدم تجهيزات فندقية عالية الجودة ومعدات ضيافة احترافية تلبي احتياجات الفنادق والمطاعم والمنتجعات. حلول متكاملة مع خدمة عملاء متخصصة وعروض أسعار مخصصة.`
    : `Explore our premium ${subcategoryName} collection ${categoryName ? 'from ' + categoryName : ''} at DROOB HAJER. We offer high-quality hotel equipment and professional hospitality supplies that meet the needs of hotels, restaurants, and resorts. Complete solutions with specialized customer service and custom quotations.`;
}

// دالة لإنشاء عنوان محسن للفئة الفرعية
function generateSubcategoryTitle(
  subcategory: Subcategory,
  category: Category | undefined,
  locale: Locale
): string {
  const subcategoryName = locale === 'ar' ? subcategory.name_ar : subcategory.name;
  const categoryName = category ? (locale === 'ar' ? category.name_ar : category.name) : '';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  // عنوان محسن للبورسلين
  if (subcategoryName.toLowerCase().includes('chinaware') || subcategoryName.includes('البورسلين')) {
    return locale === 'ar'
      ? `أدوات البورسلين الفاخرة - أطباق وفناجين بورسلين للفنادق | ${siteName}`
      : `Luxury Chinaware - Porcelain Plates & Cups for Hotels | ${siteName}`;
  }
  
  // عنوان عام محسن
  return locale === 'ar'
    ? `${subcategoryName} ${categoryName ? '- ' + categoryName : ''} - تجهيزات فندقية احترافية | ${siteName}`
    : `${subcategoryName} ${categoryName ? '- ' + categoryName : ''} - Professional Hotel Equipment | ${siteName}`;
}

export function generateSubcategoryMetadata(
  subcategory: Subcategory,
  category: Category | undefined,
  locale: Locale,
  city?: string
): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const title = generateSubcategoryTitle(subcategory, category, locale);
  const description = generateSubcategoryDescription(subcategory, category, locale);
  const keywords = generateSubcategoryKeywords(subcategory, category, locale);
  
  // إضافة كلمات مفتاحية خاصة بالمدينة إذا كانت محددة
  const cityKeywords = city ? getCitySpecificKeywords(city, locale) : [];
  const allKeywords = [...keywords, ...cityKeywords].join(', ');
  
  return {
    title,
    description,
    keywords: allKeywords,
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/subcategory/${subcategory.id}`,
      siteName,
      type: 'website',
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      images: subcategory.image_url ? [
        {
          url: subcategory.image_url,
          width: 800,
          height: 600,
          alt: locale === 'ar' ? subcategory.name_ar : subcategory.name,
          type: 'image/jpeg',
        },
      ] : [
        {
          url: `${baseUrl}/images/og-subcategory.jpg`,
          width: 1200,
          height: 630,
          alt: locale === 'ar' ? subcategory.name_ar : subcategory.name,
          type: 'image/jpeg',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/subcategory/${subcategory.id}`,
      languages: {
        'ar': `${baseUrl}/ar/subcategory/${subcategory.id}`,
        'en': `${baseUrl}/en/subcategory/${subcategory.id}`,
      },
    },
  };
}

// مكون لـ JSON-LD للفئة الفرعية
export function SubcategoryJsonLd({ subcategory, category, locale }: SubcategorySEOProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const subcategoryName = locale === 'ar' ? subcategory.name_ar : subcategory.name;
  const categoryName = category ? (locale === 'ar' ? category.name_ar : category.name) : '';
  const description = generateSubcategoryDescription(subcategory, category, locale);
  
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: subcategoryName,
    description: description,
    url: `${baseUrl}/${locale}/subcategory/${subcategory.id}`,
    isPartOf: {
      '@type': 'WebSite',
      name: siteName,
      url: baseUrl,
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: locale === 'ar' ? 'الرئيسية' : 'Home',
          item: `${baseUrl}/${locale}`,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: locale === 'ar' ? 'الفئات' : 'Categories',
          item: `${baseUrl}/${locale}/categories`,
        },
        ...(category ? [{
          '@type': 'ListItem',
          position: 3,
          name: categoryName,
          item: `${baseUrl}/${locale}/category/${category.id}`,
        }] : []),
        {
          '@type': 'ListItem',
          position: category ? 4 : 3,
          name: subcategoryName,
          item: `${baseUrl}/${locale}/subcategory/${subcategory.id}`,
        },
      ],
    },
    mainEntity: {
      '@type': 'ItemList',
      name: `${subcategoryName} Products`,
      description: description,
      numberOfItems: subcategory.products_count || 0,
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}
