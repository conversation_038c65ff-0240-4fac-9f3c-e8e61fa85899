import { Locale } from '@/lib/i18n';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface ProductData {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  category?: string;
  sku?: string;
}

// CategoryData interface removed as it's not used directly

interface StructuredDataProps {
  locale: Locale;
  type: 'website' | 'organization' | 'breadcrumb' | 'product' | 'category';
  data?: {
    breadcrumbs?: BreadcrumbItem[];
    name?: string;
    description?: string;
    price?: number;
    image?: string;
    category?: string;
    sku?: string;
    id?: string;
    url?: string;
    productsCount?: number;
    products?: ProductData[];
  };
}

export default function StructuredData({ locale, type, data }: StructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';

  const getStructuredData = () => {
    switch (type) {
      case 'website':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          name: siteName,
          url: baseUrl,
          description: locale === 'ar'
            ? 'منصة دروب هجر الإلكترونية المتخصصة في تجهيزات الفنادق والمنتجعات والمطاعم'
            : 'DROOB HAJER electronic platform specialized in hotel, resort and restaurant equipment',
          inLanguage: locale === 'ar' ? 'ar-SA' : 'en-US',
          publisher: {
            '@type': 'Organization',
            name: siteName,
            url: baseUrl,
            logo: {
              '@type': 'ImageObject',
              url: `${baseUrl}/images/logo.png`,
              width: 300,
              height: 100,
            },
          },
          potentialAction: {
            '@type': 'SearchAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: `${baseUrl}/${locale}/products?search={search_term_string}`,
            },
            'query-input': 'required name=search_term_string',
          },
        };

      case 'organization':
        return {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          name: siteName,
          url: baseUrl,
          logo: {
            '@type': 'ImageObject',
            url: `${baseUrl}/images/logo.png`,
            width: 300,
            height: 100,
          },
          description: locale === 'ar'
            ? 'منصة متخصصة في تجهيزات الفنادق والمنتجعات والمطاعم مع خدمة عروض الأسعار المخصصة'
            : 'Specialized platform for hotel, resort and restaurant equipment with custom quotation services',
          address: {
            '@type': 'PostalAddress',
            addressCountry: 'SA',
            addressLocality: locale === 'ar' ? 'الرياض' : 'Riyadh',
            addressRegion: locale === 'ar' ? 'منطقة الرياض' : 'Riyadh Region',
          },
          contactPoint: {
            '@type': 'ContactPoint',
            contactType: 'customer service',
            availableLanguage: ['Arabic', 'English'],
            areaServed: 'SA',
          },
          sameAs: [
            'https://twitter.com/droobhajer',
            'https://facebook.com/droobhajer',
            'https://instagram.com/droobhajer',
            'https://linkedin.com/company/droobhajer',
          ],
          foundingDate: '2024',
          numberOfEmployees: {
            '@type': 'QuantitativeValue',
            minValue: 10,
            maxValue: 50,
          },
          knowsAbout: [
            locale === 'ar' ? 'تجهيزات فندقية' : 'Hotel Equipment',
            locale === 'ar' ? 'أثاث فندقي' : 'Hotel Furniture',
            locale === 'ar' ? 'بياضات فنادق' : 'Hotel Linens',
            locale === 'ar' ? 'أدوات ضيافة' : 'Hospitality Supplies',
            locale === 'ar' ? 'معدات مطاعم' : 'Restaurant Equipment',
          ],
        };

      case 'breadcrumb':
        return {
          '@context': 'https://schema.org',
          '@type': 'BreadcrumbList',
          itemListElement: data?.breadcrumbs?.map((item: BreadcrumbItem, index: number) => ({
            '@type': 'ListItem',
            position: index + 1,
            name: item.name,
            item: item.url,
          })) || [],
        };

      case 'product':
        return {
          '@context': 'https://schema.org',
          '@type': 'Product',
          name: data?.name,
          description: data?.description,
          image: data?.image ? [data.image] : [],
          brand: {
            '@type': 'Brand',
            name: siteName,
          },
          manufacturer: {
            '@type': 'Organization',
            name: siteName,
          },
          category: data?.category,
          sku: data?.sku || data?.id,
          offers: {
            '@type': 'Offer',
            availability: 'https://schema.org/InStock',
            priceCurrency: 'SAR',
            price: data?.price || 0,
            priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            seller: {
              '@type': 'Organization',
              name: siteName,
            },
          },
        };

      case 'category':
        return {
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          name: data?.name,
          description: data?.description,
          url: data?.url,
          mainEntity: {
            '@type': 'ItemList',
            numberOfItems: data?.productsCount || 0,
            itemListElement: data?.products?.map((product: ProductData, index: number) => ({
              '@type': 'ListItem',
              position: index + 1,
              item: {
                '@type': 'Product',
                name: product.name,
                url: `${baseUrl}/${locale}/product/${product.id}`,
                image: product.image,
              },
            })) || [],
          },
        };

      default:
        return null;
    }
  };

  const structuredData = getStructuredData();

  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2),
      }}
    />
  );
}

// مكون مخصص للصفحة الرئيسية
export function HomeStructuredData({ locale }: { locale: Locale }) {
  return (
    <>
      <StructuredData locale={locale} type="website" />
      <StructuredData locale={locale} type="organization" />
    </>
  );
}

// مكون مخصص لصفحات المنتجات
export function ProductStructuredData({
  locale,
  product
}: {
  locale: Locale;
  product: ProductData;
}) {
  return (
    <StructuredData 
      locale={locale} 
      type="product" 
      data={product}
    />
  );
}

// مكون مخصص لصفحات الفئات
export function CategoryStructuredData({
  locale,
  category
}: {
  locale: Locale;
  category: {
    name: string;
    description?: string;
    url: string;
    productsCount?: number;
    products?: ProductData[];
  };
}) {
  return (
    <StructuredData 
      locale={locale} 
      type="category" 
      data={category}
    />
  );
}

// مكون مخصص للـ Breadcrumbs
export function BreadcrumbStructuredData({ 
  locale, 
  breadcrumbs 
}: { 
  locale: Locale; 
  breadcrumbs: Array<{ name: string; url: string }>;
}) {
  return (
    <StructuredData 
      locale={locale} 
      type="breadcrumb" 
      data={{ breadcrumbs }}
    />
  );
}
