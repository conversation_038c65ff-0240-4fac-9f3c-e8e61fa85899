import { BreadcrumbJsonLd, ProductJsonLd, WebPageJsonLd, OrganizationJsonLd } from 'next-seo';
import { Locale } from '@/lib/i18n';

interface OrganizationJsonLdProps {
  locale: Locale;
}

export function OrganizationJsonLdComponent({ locale }: OrganizationJsonLdProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  return (
    <OrganizationJsonLd
      type="Organization"
      id={`${baseUrl}/#organization`}
      name={siteName}
      url={baseUrl}
      logo={`${baseUrl}/images/logo.png`}
      sameAs={[
        'https://twitter.com/droobhajer',
        'https://facebook.com/droobhajer',
        'https://instagram.com/droobhajer',
      ]}
      contactPoints={[
        {
          telephone: '+966-XX-XXX-XXXX',
          contactType: 'customer service',
          areaServed: 'SA',
          availableLanguage: ['Arabic', 'English'],
        },
      ]}
      address={{
        streetAddress: locale === 'ar' ? 'مكة المكرمة المملكة العربية السعودية' : 'Mecca, Saudi Arabia',
        addressLocality: locale === 'ar' ? 'مكة' : 'Mecca',
        addressCountry: 'SA',
      }}
    />
  );
}

interface WebPageJsonLdProps {
  locale: Locale;
  title: string;
  description: string;
  url: string;
  breadcrumbs?: Array<{
    name: string;
    url: string;
  }>;
}

export function WebPageJsonLdComponent({
  locale,
  description,
  url,
  breadcrumbs
}: WebPageJsonLdProps) {
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  return (
    <>
      <WebPageJsonLd
        description={description}
        id={url}
        lastReviewed={new Date().toISOString()}
        reviewedBy={{
          type: 'Organization',
          name: siteName,
        }}
      />
      
      {breadcrumbs && breadcrumbs.length > 0 && (
        <BreadcrumbJsonLd
          itemListElements={breadcrumbs.map((item, index) => ({
            position: index + 1,
            name: item.name,
            item: item.url,
          }))}
        />
      )}
    </>
  );
}

interface ProductJsonLdProps {
  locale: Locale;
  product: {
    id: string;
    name: string;
    description: string;
    price: number;
    currency?: string;
    image?: string;
    brand?: string;
    category?: string;
    sku?: string;
    availability?: 'in stock' | 'out of stock' | 'preorder';
    condition?: 'new' | 'used' | 'refurbished';
    rating?: {
      value: number;
      count: number;
    };
  };
}

export function ProductJsonLdComponent({ locale, product }: ProductJsonLdProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  return (
    <ProductJsonLd
      productName={product.name}
      images={product.image ? [product.image] : []}
      description={product.description}
      brand={product.brand || siteName}
      manufacturerName={product.brand || siteName}
      manufacturerLogo={`${baseUrl}/images/logo.png`}
      
      offers={[
        {
          price: typeof product.price === 'number' ? product.price.toString() : String(product.price || 0),
          priceCurrency: product.currency || 'SAR',
          priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
          itemCondition: product.condition || 'new',
          availability: product.availability || 'in stock',
          url: `${baseUrl}/${locale}/products/${product.id}`,
          seller: {
            name: siteName,
          },
        },
      ]}
      
      mpn={product.sku || product.id}
      sku={product.sku || product.id}
      
      aggregateRating={product.rating ? {
        ratingValue: product.rating.value.toString(),
        reviewCount: product.rating.count.toString(),
      } : undefined}
      
      reviews={product.rating ? [
        {
          author: {
            type: 'Person',
            name: locale === 'ar' ? 'عميل راضي' : 'Satisfied Customer',
          },
          datePublished: new Date().toISOString(),
          reviewBody: locale === 'ar' 
            ? 'منتج ممتاز وجودة عالية، أنصح بالشراء'
            : 'Excellent product with high quality, highly recommend',
          name: locale === 'ar' ? 'تقييم ممتاز' : 'Excellent Review',
          reviewRating: {
            bestRating: '5',
            ratingValue: product.rating.value.toString(),
            worstRating: '1',
          },
        },
      ] : undefined}
    />
  );
}

interface CategoryJsonLdProps {
  locale: Locale;
  category: {
    id: string;
    name: string;
    description?: string;
    image?: string;
    productsCount?: number;
  };
}

export function CategoryJsonLdComponent({ locale, category }: CategoryJsonLdProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          name: category.name,
          description: category.description,
          url: `${baseUrl}/${locale}/categories/${category.id}`,
          image: category.image,
          mainEntity: {
            '@type': 'ItemList',
            numberOfItems: category.productsCount || 0,
            itemListElement: [], // يمكن إضافة المنتجات هنا
          },
          breadcrumb: {
            '@type': 'BreadcrumbList',
            itemListElement: [
              {
                '@type': 'ListItem',
                position: 1,
                name: locale === 'ar' ? 'الرئيسية' : 'Home',
                item: `${baseUrl}/${locale}`,
              },
              {
                '@type': 'ListItem',
                position: 2,
                name: locale === 'ar' ? 'الفئات' : 'Categories',
                item: `${baseUrl}/${locale}/categories`,
              },
              {
                '@type': 'ListItem',
                position: 3,
                name: category.name,
                item: `${baseUrl}/${locale}/categories/${category.id}`,
              },
            ],
          },
        }),
      }}
    />
  );
}

interface FAQJsonLdProps {
  locale: Locale;
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

export function FAQJsonLdComponent({ faqs }: FAQJsonLdProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          mainEntity: faqs.map((faq) => ({
            '@type': 'Question',
            name: faq.question,
            acceptedAnswer: {
              '@type': 'Answer',
              text: faq.answer,
            },
          })),
        }),
      }}
    />
  );
}
