import { NextSeo, NextSeoProps } from 'next-seo';
import { Locale } from '@/lib/i18n';
import { getPageSEO } from '@/lib/seo.config';

interface PageSEOProps {
  locale: Locale;
  page: 'home' | 'products' | 'categories' | 'about' | 'contact' | 'cart';
  customData?: {
    title?: string;
    description?: string;
    keywords?: string[];
    canonical?: string;
    noIndex?: boolean;
    ogImage?: string;
  };
  additionalProps?: Partial<NextSeoProps>;
}

export default function PageSEO({ 
  locale, 
  page, 
  customData, 
  additionalProps 
}: PageSEOProps) {
  const seoConfig = getPageSEO(locale, page, customData);
  
  // دمج الإعدادات مع أي إعدادات إضافية
  const finalConfig: NextSeoProps = {
    ...seoConfig,
    ...additionalProps,
    
    // دمج openGraph إذا كان موجود
    openGraph: {
      ...seoConfig.openGraph,
      ...additionalProps?.openGraph,
      
      // استخدام صورة مخصصة إذا تم توفيرها
      ...(customData?.ogImage && {
        images: [
          {
            url: customData.ogImage,
            width: 1200,
            height: 630,
            alt: customData.title || seoConfig.title,
            type: 'image/jpeg',
          },
        ],
      }),
    },
    
    // دمج additionalMetaTags
    additionalMetaTags: [
      ...(seoConfig.additionalMetaTags || []),
      ...(additionalProps?.additionalMetaTags || []),
    ],
  };

  return <NextSeo {...finalConfig} />;
}

// مكون مخصص للمنتجات
interface ProductSEOProps {
  locale: Locale;
  product: {
    id: string;
    name: string;
    description: string;
    price: number;
    image?: string;
    category?: string;
  };
}

export function ProductSEO({ locale, product }: ProductSEOProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const title = `${product.name} | ${siteName}`;
  const description = product.description.length > 160 
    ? `${product.description.substring(0, 157)}...`
    : product.description;
  
  const canonical = `${baseUrl}/${locale}/products/${product.id}`;
  
  return (
    <NextSeo
      title={title}
      description={description}
      canonical={canonical}
      
      openGraph={{
        type: 'product',
        title: title,
        description: description,
        url: canonical,
        siteName: siteName,
        images: product.image ? [
          {
            url: product.image,
            width: 800,
            height: 600,
            alt: product.name,
            type: 'image/jpeg',
          },
        ] : undefined,
        
        // بيانات إضافية للمنتج
        // product schema سيتم إضافته عبر ProductJsonLd
      }}
      
      additionalMetaTags={[
        {
          name: 'keywords',
          content: locale === 'ar'
            ? `${product.name}, ${product.category || ''}, دروب هاجر, تسوق, منتجات`
            : `${product.name}, ${product.category || ''}, DROOB HAJER, shopping, products`,
        },
        {
          property: 'product:price:amount',
          content: product.price.toString(),
        },
        {
          property: 'product:price:currency',
          content: 'SAR',
        },
      ]}
    />
  );
}

// مكون مخصص للفئات
interface CategorySEOProps {
  locale: Locale;
  category: {
    id: string;
    name: string;
    description?: string;
    image?: string;
    productsCount?: number;
  };
}

export function CategorySEO({ locale, category }: CategorySEOProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const title = locale === 'ar' 
    ? `${category.name} - تسوق أفضل المنتجات | ${siteName}`
    : `${category.name} - Shop Best Products | ${siteName}`;
    
  const description = category.description || (locale === 'ar'
    ? `تصفح مجموعة ${category.name} في دروب هاجر. اكتشف أفضل المنتجات بأسعار مميزة وجودة عالية.`
    : `Browse ${category.name} collection at DROOB HAJER. Discover the best products with great prices and high quality.`);
  
  const canonical = `${baseUrl}/${locale}/categories/${category.id}`;
  
  return (
    <NextSeo
      title={title}
      description={description}
      canonical={canonical}
      
      openGraph={{
        type: 'website',
        title: title,
        description: description,
        url: canonical,
        siteName: siteName,
        images: category.image ? [
          {
            url: category.image,
            width: 1200,
            height: 630,
            alt: category.name,
            type: 'image/jpeg',
          },
        ] : undefined,
      }}
      
      additionalMetaTags={[
        {
          name: 'keywords',
          content: locale === 'ar'
            ? `${category.name}, فئة, تصنيف, دروب هاجر, تسوق, منتجات`
            : `${category.name}, category, classification, DROOB HAJER, shopping, products`,
        },
        ...(category.productsCount ? [{
          name: 'product-count',
          content: category.productsCount.toString(),
        }] : []),
      ]}
    />
  );
}
