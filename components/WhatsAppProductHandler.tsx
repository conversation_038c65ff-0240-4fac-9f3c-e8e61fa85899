'use client';

import { useEffect, useState, useCallback } from 'react';
import { Locale } from '../lib/i18n';
import { ProductWithDetails } from '../types/mysql-database';

interface WhatsAppProductHandlerProps {
  locale: Locale;
  productId?: string;
}

export default function WhatsAppProductHandler({ locale, productId }: WhatsAppProductHandlerProps) {
  const [product, setProduct] = useState<ProductWithDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [whatsappSettings, setWhatsappSettings] = useState({
    businessNumber: '+966 599252259',
    welcomeMessage: 'Hello! How can we help you today?',
    welcomeMessageAr: 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
    enabled: true
  });

  // جلب إعدادات واتساب
  useEffect(() => {
    const savedSettings = localStorage.getItem('siteSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        if (settings.communicationSettings?.whatsapp) {
          setWhatsappSettings(prev => ({
            ...prev,
            ...settings.communicationSettings.whatsapp
          }));
        }
      } catch {
        console.log('Using default WhatsApp settings');
      }
    }
  }, []);

  const redirectToWhatsApp = useCallback((productData: ProductWithDetails) => {
    const productTitle = locale === 'ar' ? productData.title_ar : productData.title;
    const productDescription = locale === 'ar' ? productData.description_ar : productData.description;

    // إنشاء رسالة واتساب مختصرة وواضحة
    const message = locale === 'ar'
      ? `مرحباً، أريد الاستفسار عن المنتج:\n\n*${productTitle}*\n\nالرمز: ${productData.id}\nالسعر: ${productData.price} ريال\n\n${productDescription ? productDescription.substring(0, 100) + '...' : ''}`
      : `Hello, I want to inquire about the product:\n\n*${productTitle}*\n\nCode: ${productData.id}\nPrice: ${productData.price} SAR\n\n${productDescription ? productDescription.substring(0, 100) + '...' : ''}`;

    const encodedMessage = encodeURIComponent(message);
    const phoneNumber = whatsappSettings.businessNumber.replace(/[^0-9]/g, '');
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;

    // إعادة التوجيه إلى واتساب
    window.location.href = whatsappUrl;
  }, [locale, whatsappSettings]);

  const redirectToGeneralWhatsApp = useCallback(() => {
    const message = locale === 'ar' ? whatsappSettings.welcomeMessageAr : whatsappSettings.welcomeMessage;
    const encodedMessage = encodeURIComponent(message);
    const phoneNumber = whatsappSettings.businessNumber.replace(/[^0-9]/g, '');
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;

    window.location.href = whatsappUrl;
  }, [locale, whatsappSettings]);

  // جلب بيانات المنتج إذا كان محدد
  useEffect(() => {
    if (productId) {
      setLoading(true);
      fetch(`/api/products/${productId}`)
        .then(response => response.json())
        .then(result => {
          if (result.success && result.data) {
            setProduct(result.data);
            // إعادة التوجيه إلى واتساب تلقائياً
            redirectToWhatsApp(result.data);
          } else {
            console.error('Product not found');
            // إعادة التوجيه إلى واتساب العام
            redirectToGeneralWhatsApp();
          }
        })
        .catch(error => {
          console.error('Error fetching product:', error);
          redirectToGeneralWhatsApp();
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [productId, whatsappSettings, locale, redirectToWhatsApp, redirectToGeneralWhatsApp]);



  // إذا لم يكن هناك منتج محدد، إعادة التوجيه إلى واتساب العام
  useEffect(() => {
    if (!productId) {
      redirectToGeneralWhatsApp();
    }
  }, [productId, whatsappSettings, locale, redirectToGeneralWhatsApp]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">
            {locale === 'ar' ? 'جاري التحضير...' : 'Preparing...'}
          </h2>
          <p className="text-gray-500">
            {locale === 'ar' ? 'سيتم توجيهك إلى واتساب خلال لحظات' : 'You will be redirected to WhatsApp in a moment'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
          <i className="ri-whatsapp-line text-3xl text-green-600"></i>
        </div>
        
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          {locale === 'ar' ? 'توجيه إلى واتساب' : 'Redirecting to WhatsApp'}
        </h2>
        
        {product && (
          <div className="bg-white rounded-lg p-4 mb-6 shadow-sm">
            <h3 className="font-semibold text-gray-800 mb-2">
              {locale === 'ar' ? 'المنتج المحدد:' : 'Selected Product:'}
            </h3>
            <p className="text-gray-600">
              {locale === 'ar' ? product.title_ar : product.title}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              {locale === 'ar' ? 'الرمز:' : 'Code:'} {product.id}
            </p>
          </div>
        )}
        
        <p className="text-gray-600 mb-6">
          {locale === 'ar' 
            ? 'إذا لم يتم التوجيه تلقائياً، اضغط على الزر أدناه'
            : 'If you are not redirected automatically, click the button below'
          }
        </p>
        
        <button
          onClick={() => product ? redirectToWhatsApp(product) : redirectToGeneralWhatsApp()}
          className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center gap-2"
        >
          <i className="ri-whatsapp-line"></i>
          {locale === 'ar' ? 'فتح واتساب' : 'Open WhatsApp'}
        </button>
        
        <div className="mt-6 pt-6 border-t border-gray-200">
          <a
            href={`/${locale}/contact`}
            className="text-gray-500 hover:text-gray-700 transition-colors inline-flex items-center gap-2"
          >
            <i className="ri-arrow-left-line"></i>
            {locale === 'ar' ? 'العودة إلى صفحة التواصل' : 'Back to Contact Page'}
          </a>
        </div>
      </div>
    </div>
  );
}
