'use client';

import React from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { ProductWithDetails } from '../types/mysql-database';
import { useApiCache } from '../hooks/useApiCache';
import ModernProductCard from './ModernProductCard';

interface FeaturedProductsProps {
  locale: Locale;
  products?: ProductWithDetails[];
}

const FeaturedProducts: React.FC<FeaturedProductsProps> = ({ locale, products: initialProducts }) => {
  // استخدام الـ Cache Hook دائماً، ولكن تجاهل النتيجة إذا تم تمرير البيانات
  const { data: products, loading, error } = useApiCache<ProductWithDetails[]>(
    '/api/products?featured=true',
    {
      ttl: 15 * 60 * 1000, // 15 دقيقة
      staleWhileRevalidate: 30 * 60 * 1000, // 30 دقيقة
      revalidateOnFocus: true,
      revalidateOnReconnect: true
    }
  );

  // استخدام البيانات المُمررة أو المُحملة من الـ API
  const allProducts = initialProducts || products;
  // أخذ أول 6 منتجات مميزة
  const displayProducts = allProducts ? allProducts.slice(0, 6) : [];

  // إظهار التحميل فقط إذا لم يتم تمرير البيانات ولا يزال يحمل
  const isLoading = !initialProducts && loading;

  if (isLoading) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري تحميل المنتجات المميزة...' : 'Loading featured products...'}
            </p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4 text-center">
          <p className="text-red-600">خطأ في تحميل المنتجات المميزة</p>
        </div>
      </section>
    );
  }

  if (displayProducts.length === 0) {
    return null;
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {locale === 'ar' 
              ? 'اكتشف أفضل منتجاتنا المختارة بعناية لتلبية احتياجاتك'
              : 'Discover our best products carefully selected to meet your needs'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {displayProducts.map((product) => (
            <ModernProductCard
              key={product.id}
              id={product.id}
              image={product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
              title={locale === 'ar' ? product.title_ar : product.title}
              description={locale === 'ar' ? (product.description_ar || '') : (product.description || '')}
              price={product.price}
              available={product.is_available}
              locale={locale}
              variant="featured"
            />
          ))}
        </div>

        <div className="text-center">
          <Link
            href={`/${locale}/products`}
            prefetch={true}
            className="inline-flex items-center gap-2 bg-primary hover:bg-primary/90 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
          >
            {locale === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}
            <i className="ri-arrow-right-line"></i>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
