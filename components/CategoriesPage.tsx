'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';

interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  product_count: number;
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  subcategories: Subcategory[];
}

interface CategoriesPageProps {
  locale: Locale;
}

const CategoriesPage: React.FC<CategoriesPageProps> = ({ locale }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  const t = (key: string) => getTranslation(locale, key as keyof typeof import('../lib/translations').translations.ar);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/navbar/categories');
        const result = await response.json();
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error('خطأ في جلب الفئات:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            <p className="mt-4 text-gray-600">{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            {t('categories')}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {locale === 'ar' 
              ? 'تصفح جميع فئات المنتجات واكتشف ما نقدمه من منتجات متنوعة وعالية الجودة'
              : 'Browse all product categories and discover our diverse range of high-quality products'
            }
          </p>
        </div>

        {/* الفئات */}
        {categories.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {categories.map((category) => (
              <div key={category.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                {/* صورة الفئة */}
                {category.image_url && (
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={category.image_url}
                      alt={locale === 'ar' ? category.name_ar : category.name}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                )}

                <div className="p-6">
                  {/* اسم الفئة */}
                  <h2 className="text-xl font-bold text-gray-800 mb-2">
                    {locale === 'ar' ? category.name_ar : category.name}
                  </h2>

                  {/* وصف الفئة */}
                  {category.description && (
                    <p className="text-gray-600 mb-4 text-sm">
                      {locale === 'ar' ? category.description_ar : category.description}
                    </p>
                  )}

                  {/* الفئات الفرعية */}
                  {category.subcategories && category.subcategories.length > 0 && (
                    <div className="mb-4">
                      <h3 className="text-sm font-semibold text-gray-700 mb-2">
                        {locale === 'ar' ? 'الفئات الفرعية:' : 'Subcategories:'}
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {category.subcategories.slice(0, 3).map((subcategory) => (
                          <Link
                            key={subcategory.id}
                            href={`/${locale}/subcategory/${subcategory.id}`}
                            className="inline-block px-3 py-1 bg-gray-100 text-gray-700 text-xs rounded-full hover:bg-primary hover:text-white transition-colors duration-200"
                          >
                            {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                          </Link>
                        ))}
                        {category.subcategories.length > 3 && (
                          <span className="inline-block px-3 py-1 bg-gray-200 text-gray-600 text-xs rounded-full">
                            +{category.subcategories.length - 3} {locale === 'ar' ? 'المزيد' : 'more'}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* رابط عرض المنتجات */}
                  <Link
                    href={`/${locale}/category/${category.id}`}
                    className="inline-flex items-center gap-2 bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                  >
                    <span>
                      {locale === 'ar' ? 'عرض المنتجات' : 'View Products'}
                    </span>
                    <i className="ri-arrow-right-line"></i>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl text-gray-300 mb-4">
              <i className="ri-folder-open-line"></i>
            </div>
            <h2 className="text-2xl font-bold text-gray-600 mb-2">
              {locale === 'ar' ? 'لا توجد فئات حالياً' : 'No Categories Available'}
            </h2>
            <p className="text-gray-500">
              {locale === 'ar' 
                ? 'سيتم إضافة الفئات قريباً. تابعونا للحصول على آخر التحديثات.'
                : 'Categories will be added soon. Stay tuned for updates.'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoriesPage;
