'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Locale } from '../lib/i18n';
import { ProductWithDetails, Subcategory, Category } from '../types/mysql-database';
import MobileSubcategoryPage from './mobile/MobileSubcategoryPage';
import ModernProductCard from './ModernProductCard';
import Navbar from './Navbar';
import Footer from './Footer';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null
});

interface ResponsiveSubcategoryPageProps {
  locale: Locale;
  subcategoryId: string;
  initialProducts?: ProductWithDetails[];
  initialSubcategory?: Subcategory;
  initialCategory?: Category;
}

const ResponsiveSubcategoryPage: React.FC<ResponsiveSubcategoryPageProps> = ({
  locale,
  subcategoryId,
  initialProducts,
  initialSubcategory,
  initialCategory
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState<ProductWithDetails[]>(initialProducts || []);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
      const isSmallScreen = window.innerWidth <= 768;

      setIsMobile(isMobileDevice || isSmallScreen);
      setIsLoading(false);
    };

    checkDevice();

    const handleResize = () => {
      const isSmallScreen = window.innerWidth <= 768;
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

      setIsMobile(isMobileDevice || isSmallScreen);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تحديث المنتجات عند تغيير initialProducts
  useEffect(() => {
    if (initialProducts) {
      setProducts(initialProducts);
      // إذا كان عدد المنتجات الأولية أقل من 12، فلا توجد منتجات أخرى
      setHasMoreProducts(initialProducts.length >= 12);
      setCurrentPage(1);
    }
  }, [initialProducts]);

  // دالة تحميل المزيد من المنتجات
  const loadMoreProducts = useCallback(async () => {
    if (!hasMoreProducts || loadingMore) return;

    try {
      setLoadingMore(true);

      const response = await fetch(`/api/products?subcategoryId=${subcategoryId}&page=${currentPage + 1}&limit=12`, {
        headers: {
          'Cache-Control': 'max-age=300'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setProducts(prev => [...prev, ...result.data]);
          setCurrentPage(prev => prev + 1);
          setHasMoreProducts(result.pagination?.hasMore || false);
        }
      }
    } catch (error) {
      console.error('Error loading more products:', error);
    } finally {
      setLoadingMore(false);
    }
  }, [subcategoryId, currentPage, hasMoreProducts, loadingMore]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">
            {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (isMobile) {
    return (
      <MobileSubcategoryPage
        locale={locale}
        subcategoryId={subcategoryId}
        initialProducts={initialProducts}
        initialSubcategory={initialSubcategory}
        initialCategory={initialCategory}
      />
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main>
        {/* Page Header */}
        <section className="bg-primary py-12">
          <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <nav className="mb-6 text-center">
              <ol className="inline-flex items-center space-x-2 rtl:space-x-reverse text-sm text-white/80">
                <li>
                  <a href={`/${locale}`} className="hover:text-white transition-colors">
                    {locale === 'ar' ? 'الرئيسية' : 'Home'}
                  </a>
                </li>
                <i className="ri-arrow-right-s-line mx-2"></i>
                <li>
                  <a href={`/${locale}/categories`} className="hover:text-white transition-colors">
                    {locale === 'ar' ? 'الفئات' : 'Categories'}
                  </a>
                </li>
                {initialCategory && (
                  <>
                    <i className="ri-arrow-right-s-line mx-2"></i>
                    <li>
                      <a href={`/${locale}/category/${initialCategory.id}`} className="hover:text-white transition-colors">
                        {locale === 'ar' ? initialCategory.name_ar : initialCategory.name}
                      </a>
                    </li>
                  </>
                )}
                <i className="ri-arrow-right-s-line mx-2"></i>
                <li className="text-white font-medium">
                  {initialSubcategory ? (locale === 'ar' ? initialSubcategory.name_ar : initialSubcategory.name) : ''}
                </li>
              </ol>
            </nav>

            {/* Page Title */}
            <h1 className="text-4xl font-bold text-white text-center mb-4">
              {initialSubcategory ? (locale === 'ar' ? initialSubcategory.name_ar : initialSubcategory.name) : 'منتجات الفئة الفرعية'}
            </h1>
            <p className="text-white/80 text-center mt-4 max-w-2xl mx-auto">
              {initialSubcategory && locale === 'ar' ? initialSubcategory.description_ar :
               initialSubcategory && locale === 'en' ? initialSubcategory.description :
               locale === 'ar'
                ? 'اكتشف مجموعتنا المتنوعة من المنتجات عالية الجودة'
                : 'Discover our diverse range of high-quality products'
              }
            </p>
            {products && products.length > 0 && (
              <div className="text-center mt-4 text-white/60 text-sm">
                {products.length} {locale === 'ar' ? 'منتج متاح' : 'products available'}
              </div>
            )}
          </div>
        </section>

        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            {products && products.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {products.map((product) => (
                    <ModernProductCard
                      key={product.id}
                      id={product.id}
                      image={product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
                      title={locale === 'ar' ? product.title_ar : product.title}
                      description={locale === 'ar' ? (product.description_ar || '') : (product.description || '')}
                      price={product.price}
                      available={product.is_available}
                      locale={locale}
                      variant="default"
                    />
                  ))}
                </div>

                {/* Load More Button */}
                {hasMoreProducts && (
                  <div className="text-center mt-12">
                    <button
                      onClick={loadMoreProducts}
                      disabled={loadingMore}
                      className="inline-flex items-center gap-3 bg-primary text-white px-8 py-4 rounded-lg font-medium hover:bg-primary/90 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105"
                    >
                      {loadingMore ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</span>
                        </>
                      ) : (
                        <>
                          <i className="ri-add-line text-lg"></i>
                          <span>{locale === 'ar' ? 'عرض المزيد من المنتجات' : 'Load More Products'}</span>
                        </>
                      )}
                    </button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-16">
                <i className="ri-shopping-bag-line text-6xl text-gray-400 mb-4"></i>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {locale === 'ar'
                    ? 'لم يتم العثور على منتجات في هذه الفئة الفرعية'
                    : 'No products found in this subcategory'
                  }
                </p>
                <a
                  href={`/${locale}/products`}
                  className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
                >
                  <i className="ri-shopping-bag-line"></i>
                  <span>{locale === 'ar' ? 'تصفح جميع المنتجات' : 'Browse All Products'}</span>
                </a>
              </div>
            )}
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
};

export default ResponsiveSubcategoryPage;
