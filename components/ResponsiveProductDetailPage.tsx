'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '../lib/i18n';
import { ProductWithDetails, Category, Subcategory } from '../types/mysql-database';
import MobileProductDetailPage from './mobile/MobileProductDetailPage';
import ProductPageClient from '../app/[locale]/product/[id]/ProductPageClient';
import Navbar from './Navbar';
import Footer from './Footer';

interface ResponsiveProductDetailPageProps {
  locale: Locale;
  initialProduct?: ProductWithDetails | null;
  initialCategory?: Category | null;
  initialSubcategory?: Subcategory | null;
  productId: string;
}

const ResponsiveProductDetailPage: React.FC<ResponsiveProductDetailPageProps> = ({
  locale,
  initialProduct,
  initialCategory,
  initialSubcategory,
  productId
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      try {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isSmallScreen = window.innerWidth <= 768;

        setIsMobile(isMobileDevice || isSmallScreen);
      } catch (error) {
        console.error('Error checking device:', error);
        // في حالة الخطأ، افتراض أنه ليس موبايل
        setIsMobile(false);
      }
    };

    // فحص فوري بدون تأخير
    checkDevice();

    const handleResize = () => {
      try {
        const isSmallScreen = window.innerWidth <= 768;
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

        setIsMobile(isMobileDevice || isSmallScreen);
      } catch (error) {
        console.error('Error in resize handler:', error);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // إزالة شاشة التحميل لتجنب عرضها في SSR
  // المحتوى سيظهر مباشرة لأن البيانات متوفرة من الخادم

  if (isMobile) {
    return (
      <MobileProductDetailPage
        locale={locale}
        initialProduct={initialProduct}
        initialCategory={initialCategory}
        initialSubcategory={initialSubcategory}
        productId={productId}
      />
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <ProductPageClient
        initialProduct={initialProduct || null}
        initialCategory={initialCategory || null}
        initialSubcategory={initialSubcategory || null}
        locale={locale}
        productId={productId}
      />
      <Footer locale={locale} />
    </>
  );
};

export default ResponsiveProductDetailPage;
