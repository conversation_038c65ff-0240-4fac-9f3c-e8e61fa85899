import React, { useState, useRef } from 'react';
import Image from 'next/image';

interface ImageUploadProps {
  onImagesUploaded: (images: string[]) => void;
  multiple?: boolean;
  maxFiles?: number;
  currentImages?: string[];
  label?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImagesUploaded,
  multiple = false,
  maxFiles = 1,
  currentImages = [],
  label = 'رفع الصور'
}) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const filesToUpload = Array.from(files);
    
    // التحقق من عدد الملفات
    if (multiple && currentImages.length + filesToUpload.length > maxFiles) {
      alert(`يمكنك رفع ${maxFiles} صور كحد أقصى`);
      return;
    }

    if (!multiple && filesToUpload.length > 1) {
      alert('يمكنك رفع صورة واحدة فقط');
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      filesToUpload.forEach((file) => {
        formData.append('files', file);
      });

      const response = await fetch('/api/upload-simple', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('فشل في رفع الصور');
      }

      const result = await response.json();
      
      if (result.success && result.files) {
        if (multiple) {
          // إضافة الصور الجديدة إلى الصور الحالية
          const newImages = [...currentImages, ...result.files];
          onImagesUploaded(newImages);
        } else {
          // استبدال الصورة الحالية
          onImagesUploaded(result.files);
        }
      } else {
        throw new Error(result.error || 'فشل في رفع الصور');
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      alert('حدث خطأ أثناء رفع الصور');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeImage = (index: number) => {
    const newImages = currentImages.filter((_, i) => i !== index);
    onImagesUploaded(newImages);
  };

  const canAddMore = multiple ? currentImages.length < maxFiles : currentImages.length === 0;

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>

      {/* منطقة رفع الصور */}
      {canAddMore && (
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragOver
              ? 'border-blue-400 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple={multiple}
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
          />

          {uploading ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
              <p className="text-sm text-gray-600">جاري رفع الصور...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center">
              <i className="ri-upload-cloud-2-line text-4xl text-gray-400 mb-2"></i>
              <p className="text-sm text-gray-600 mb-2">
                اسحب الصور هنا أو{' '}
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  اختر الملفات
                </button>
              </p>
              <p className="text-xs text-gray-500">
                {multiple 
                  ? `يمكنك رفع ${maxFiles - currentImages.length} صور إضافية`
                  : 'صورة واحدة فقط'
                }
              </p>
            </div>
          )}
        </div>
      )}

      {/* عرض الصور الحالية */}
      {currentImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {currentImages.map((image, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden border border-gray-200">
                <Image
                  src={image || '/api/placeholder?width=200&height=200&text=صورة'}
                  alt={`صورة ${index + 1}`}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/api/placeholder?width=200&height=200&text=خطأ في الصورة';
                  }}
                />
              </div>
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
                title="حذف الصورة"
              >
                <i className="ri-close-line text-sm"></i>
              </button>
            </div>
          ))}
        </div>
      )}

      {/* معلومات إضافية */}
      {multiple && (
        <p className="text-xs text-gray-500">
          {currentImages.length} من {maxFiles} صور
        </p>
      )}
    </div>
  );
};

export default ImageUpload;
