'use client';

import React from 'react';

interface ProductSkeletonProps {
  count?: number;
  isMobile?: boolean;
}

const ProductSkeleton: React.FC<ProductSkeletonProps> = ({ 
  count = 6, 
  isMobile = false 
}) => {
  if (isMobile) {
    return (
      <div className="space-y-4">
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className="flex bg-white rounded-xl overflow-hidden shadow-sm animate-pulse">
            {/* Image Skeleton */}
            <div className="w-24 h-24 bg-gray-200 flex-shrink-0"></div>
            
            {/* Content Skeleton */}
            <div className="flex-1 p-3 space-y-2">
              {/* Title */}
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              
              {/* Description */}
              <div className="space-y-1">
                <div className="h-3 bg-gray-200 rounded w-full"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
              
              {/* Bottom row */}
              <div className="flex items-center justify-between pt-1">
                <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                <div className="h-6 bg-gray-200 rounded-full w-8"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Desktop grid skeleton
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white rounded-xl overflow-hidden shadow-sm animate-pulse">
          {/* Image Skeleton */}
          <div className="aspect-square bg-gray-200"></div>
          
          {/* Content Skeleton */}
          <div className="p-4 space-y-3">
            {/* Title */}
            <div className="h-5 bg-gray-200 rounded w-3/4"></div>
            
            {/* Description */}
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded w-full"></div>
              <div className="h-3 bg-gray-200 rounded w-2/3"></div>
            </div>
            
            {/* Price and button */}
            <div className="flex items-center justify-between pt-2">
              <div className="h-6 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductSkeleton;
