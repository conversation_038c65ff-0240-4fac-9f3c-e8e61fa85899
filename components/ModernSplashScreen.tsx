'use client';

import React, { useState, useEffect } from 'react';

interface ModernSplashScreenProps {
  onComplete?: () => void;
  duration?: number;
  showProgress?: boolean;
  locale?: 'ar' | 'en';
}

const ModernSplashScreen: React.FC<ModernSplashScreenProps> = ({
  onComplete,
  duration = 4000,
  showProgress = true,
  locale = 'ar'
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [currentPhase, setCurrentPhase] = useState(0); // 0: logo, 1: text, 2: complete

  const texts = {
    ar: {
      welcome: 'مرحباً بكم في',
      brandName: 'دروب هجر',
      tagline: 'التجهيزات الفندقية الاحترافية',
      subtitle: 'حلول الضيافة المتطورة',
      loading: 'جاري التحميل',
      excellence: 'التميز في كل التفاصيل'
    },
    en: {
      welcome: 'Welcome to',
      brandName: 'DROOB HAJER',
      tagline: 'Professional Hotel Equipment',
      subtitle: 'Advanced Hospitality Solutions',
      loading: 'Loading',
      excellence: 'Excellence in Every Detail'
    }
  };

  const t = texts[locale];

  useEffect(() => {
    // Phase transitions
    const phaseTimer1 = setTimeout(() => setCurrentPhase(1), 800);
    const phaseTimer2 = setTimeout(() => setCurrentPhase(2), 1600);

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
              onComplete?.();
            }, 800);
          }, 600);
          return 100;
        }
        return prev + 1.5;
      });
    }, duration / 67);

    return () => {
      clearInterval(interval);
      clearTimeout(phaseTimer1);
      clearTimeout(phaseTimer2);
    };
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center overflow-hidden">
      {/* Ultra Modern Gradient Background */}
      <div 
        className="absolute inset-0 transition-all duration-1000"
        style={{
          background: `
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.3) 0%, transparent 50%),
            linear-gradient(135deg, #0c0a1e 0%, #1a1b3a 25%, #2d1b69 50%, #4c1d95 75%, #7c3aed 100%)
          `
        }}
      />

      {/* Animated Geometric Shapes */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Modern floating elements */}
        <div className="absolute top-1/4 left-1/4 w-40 h-40 border border-white/10 rounded-3xl rotate-45 animate-float opacity-20" style={{animationDelay: '0s'}} />
        <div className="absolute top-1/3 right-1/4 w-32 h-32 border-2 border-blue-300/30 rounded-full animate-float opacity-40" style={{animationDelay: '1s'}} />
        <div className="absolute bottom-1/4 left-1/3 w-24 h-24 border border-purple-300/25 rounded-2xl animate-float opacity-30" style={{animationDelay: '2s'}} />
        <div className="absolute bottom-1/3 right-1/3 w-20 h-20 border-2 border-pink-300/30 rounded-full animate-float opacity-25" style={{animationDelay: '0.5s'}} />
        
        {/* Premium gradient orbs */}
        <div className="absolute top-20 right-20 w-60 h-60 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 left-20 w-48 h-48 bg-gradient-to-r from-pink-500/30 to-blue-500/30 rounded-full blur-2xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse delay-500" />
      </div>

      {/* Professional Equipment Icons */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Glassmorphism equipment icons */}
        <div className="absolute top-16 left-16 w-14 h-14 bg-white/15 backdrop-blur-xl rounded-2xl flex items-center justify-center text-white/50 text-2xl animate-float border border-white/30 shadow-2xl" style={{animationDelay: '0s'}}>
          🍽️
        </div>
        <div className="absolute top-24 right-20 w-12 h-12 bg-white/15 backdrop-blur-xl rounded-xl flex items-center justify-center text-white/50 text-xl animate-float border border-white/30 shadow-xl" style={{animationDelay: '1.2s'}}>
          🏨
        </div>
        <div className="absolute bottom-24 left-20 w-13 h-13 bg-white/15 backdrop-blur-xl rounded-2xl flex items-center justify-center text-white/50 text-xl animate-float border border-white/30 shadow-xl" style={{animationDelay: '2.1s'}}>
          ⚡
        </div>
        <div className="absolute bottom-16 right-16 w-14 h-14 bg-white/15 backdrop-blur-xl rounded-xl flex items-center justify-center text-white/50 text-2xl animate-float border border-white/30 shadow-2xl" style={{animationDelay: '0.7s'}}>
          ✨
        </div>

        {/* Premium floating particles */}
        {[...Array(25)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1.5 h-1.5 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full opacity-40 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${5 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-8 max-w-xl mx-auto">
        {/* Ultra Modern Logo Container */}
        <div className={`relative mb-16 transition-all duration-1000 ${currentPhase >= 0 ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-12 scale-95'}`}>
          {/* Multi-layer glow effects */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-blue-500/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-xl animate-pulse delay-500"></div>
          
          {/* Premium Logo */}
          <div className="relative w-48 h-48 mx-auto">
            {/* Ultra glassmorphism background */}
            <div className="absolute inset-0 bg-white/20 backdrop-blur-2xl rounded-[2rem] border-2 border-white/30 shadow-2xl"></div>
            
            {/* Logo content */}
            <div className="relative w-full h-full flex items-center justify-center">
              <div className="text-center">
                <div className="text-6xl font-bold text-white mb-3 tracking-wider font-sans">
                  <span className="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-2xl">
                    DH
                  </span>
                </div>
                <div className="text-sm text-white/70 font-medium tracking-[0.3em] uppercase">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </div>
              </div>
            </div>

            {/* Premium rotating border */}
            <div className="absolute inset-0 rounded-[2rem] border-2 border-gradient-to-r from-blue-400/60 via-purple-400/60 to-pink-400/60 animate-spin-slow"></div>
            
            {/* Premium corner accents */}
            <div className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse shadow-xl"></div>
            <div className="absolute -bottom-3 -left-3 w-6 h-6 bg-gradient-to-r from-pink-400 to-blue-400 rounded-full animate-pulse delay-500 shadow-lg"></div>
            <div className="absolute -top-3 -left-3 w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse delay-1000 shadow-md"></div>
          </div>
        </div>

        {/* Premium Brand Text */}
        <div className={`space-y-8 mb-16 transition-all duration-1000 delay-500 ${currentPhase >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="space-y-6">
            <p className="text-white/80 text-xl font-light tracking-wide">
              {t.welcome}
            </p>
            <h1 className="text-white text-5xl md:text-6xl font-bold tracking-tight leading-tight">
              <span className="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-2xl">
                {t.brandName}
              </span>
            </h1>
            <div className="space-y-3">
              <p className="text-white/90 text-xl md:text-2xl font-medium tracking-wide">
                {t.tagline}
              </p>
              <p className="text-white/70 text-lg font-light tracking-wide">
                {t.excellence}
              </p>
            </div>
          </div>
        </div>

        {/* Ultra Modern Progress Section */}
        {showProgress && (
          <div className={`space-y-8 transition-all duration-1000 delay-1000 ${currentPhase >= 2 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {/* Premium Progress Bar */}
            <div className="relative w-full max-w-sm mx-auto">
              <div className="w-full h-2 bg-white/15 rounded-full overflow-hidden backdrop-blur-sm border border-white/30 shadow-xl">
                <div 
                  className="h-full bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full transition-all duration-700 ease-out shadow-2xl relative overflow-hidden"
                  style={{ width: `${progress}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full animate-pulse"></div>
                  <div className="absolute inset-0 bg-white/30 rounded-full animate-pulse delay-300"></div>
                </div>
              </div>
              
              {/* Premium progress glow */}
              <div 
                className="absolute top-0 h-2 bg-gradient-to-r from-blue-400/60 via-purple-400/60 to-pink-400/60 rounded-full blur-md transition-all duration-700"
                style={{ width: `${progress}%` }}
              />
            </div>
            
            {/* Premium Loading Animation */}
            <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse">
              <div className="flex space-x-2 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce shadow-lg"></div>
                <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-bounce delay-100 shadow-lg"></div>
                <div className="w-2 h-2 bg-gradient-to-r from-pink-400 to-blue-400 rounded-full animate-bounce delay-200 shadow-lg"></div>
              </div>
              <span className="text-white/80 text-base font-light tracking-wide ml-4 rtl:mr-4">
                {t.loading}
              </span>
            </div>
            
            {/* Premium Progress Percentage */}
            <div className="text-center">
              <span className="text-white/60 text-sm font-light tracking-[0.2em]">
                {Math.round(progress)}%
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Premium Bottom Decoration */}
      <div className="absolute bottom-0 left-0 right-0 h-48 bg-gradient-to-t from-black/40 via-black/20 to-transparent"></div>
      
      {/* Ultra subtle grid pattern */}
      <div 
        className="absolute inset-0 opacity-[0.03]"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px'
        }}
      />
    </div>
  );
};

export default ModernSplashScreen;
