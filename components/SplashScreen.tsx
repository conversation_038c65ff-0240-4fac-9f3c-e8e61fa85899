'use client';

import React, { useState, useEffect } from 'react';

interface SplashScreenProps {
  onComplete?: () => void;
  duration?: number;
  showProgress?: boolean;
  locale?: 'ar' | 'en';
}

const SplashScreen: React.FC<SplashScreenProps> = ({
  onComplete,
  duration = 4000,
  showProgress = true,
  locale = 'ar'
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [currentPhase, setCurrentPhase] = useState(0); // 0: logo, 1: text, 2: complete

  const texts = {
    ar: {
      welcome: 'مرحباً بكم في',
      brandName: 'دروب هجر',
      tagline: 'التجهيزات الفندقية الاحترافية',
      subtitle: 'حلول الضيافة المتطورة',
      loading: 'جاري التحميل',
      excellence: 'التميز في كل التفاصيل'
    },
    en: {
      welcome: 'Welcome to',
      brandName: 'DROOB HAJER',
      tagline: 'Professional Hotel Equipment',
      subtitle: 'Advanced Hospitality Solutions',
      loading: 'Loading',
      excellence: 'Excellence in Every Detail'
    }
  };

  const t = texts[locale];

  useEffect(() => {
    // Phase transitions
    const phaseTimer1 = setTimeout(() => setCurrentPhase(1), 800);
    const phaseTimer2 = setTimeout(() => setCurrentPhase(2), 1600);

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
              onComplete?.();
            }, 800);
          }, 600);
          return 100;
        }
        return prev + 1.5;
      });
    }, duration / 67);

    return () => {
      clearInterval(interval);
      clearTimeout(phaseTimer1);
      clearTimeout(phaseTimer2);
    };
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center overflow-hidden">
      {/* Modern Gradient Background */}
      <div
        className="absolute inset-0 transition-all duration-1000"
        style={{
          background: `
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
            linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%)
          `
        }}
      />

      {/* Animated Geometric Shapes */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating geometric elements */}
        <div className="absolute top-1/4 left-1/4 w-32 h-32 border border-white/10 rounded-lg rotate-45 animate-float opacity-20" style={{animationDelay: '0s'}} />
        <div className="absolute top-1/3 right-1/4 w-24 h-24 border border-blue-300/20 rounded-full animate-float opacity-30" style={{animationDelay: '1s'}} />
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 border border-purple-300/20 rounded-lg animate-float opacity-25" style={{animationDelay: '2s'}} />
        <div className="absolute bottom-1/3 right-1/3 w-16 h-16 border border-pink-300/20 rounded-full animate-float opacity-20" style={{animationDelay: '0.5s'}} />

        {/* Gradient orbs */}
        <div className="absolute top-20 right-20 w-40 h-40 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse" />
        <div className="absolute bottom-20 left-20 w-32 h-32 bg-gradient-to-r from-pink-400/20 to-blue-400/20 rounded-full blur-xl animate-pulse delay-1000" />
      </div>

      {/* Professional Equipment Icons */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Modern Equipment Icons with glassmorphism effect */}
        <div className="absolute top-16 left-16 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-white/40 text-xl animate-float border border-white/20" style={{animationDelay: '0s'}}>
          🍽️
        </div>
        <div className="absolute top-24 right-20 w-10 h-10 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center text-white/40 text-lg animate-float border border-white/20" style={{animationDelay: '1.2s'}}>
          🏨
        </div>
        <div className="absolute bottom-24 left-20 w-11 h-11 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-white/40 text-lg animate-float border border-white/20" style={{animationDelay: '2.1s'}}>
          ⚡
        </div>
        <div className="absolute bottom-16 right-16 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center text-white/40 text-xl animate-float border border-white/20" style={{animationDelay: '0.7s'}}>
          ✨
        </div>

        {/* Floating particles with modern design */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-30 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 4}s`,
              animationDuration: `${4 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-8 max-w-lg mx-auto">
        {/* Modern Logo Container */}
        <div className={`relative mb-12 transition-all duration-1000 ${currentPhase >= 0 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          {/* Glow Effects */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-pink-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>

          {/* Main Logo */}
          <div className="relative w-40 h-40 mx-auto">
            {/* Glassmorphism background */}
            <div className="absolute inset-0 bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl"></div>

            {/* Logo content */}
            <div className="relative w-full h-full flex items-center justify-center">
              <div className="text-center">
                <div className="text-5xl font-bold text-white mb-2 tracking-wider font-sans">
                  <span className="bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                    DH
                  </span>
                </div>
                <div className="text-xs text-white/60 font-medium tracking-widest uppercase">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </div>
              </div>
            </div>

            {/* Rotating border */}
            <div className="absolute inset-0 rounded-3xl border-2 border-gradient-to-r from-blue-400/50 to-purple-400/50 animate-spin-slow"></div>

            {/* Corner accents */}
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
            <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-gradient-to-r from-pink-400 to-blue-400 rounded-full animate-pulse delay-500"></div>
          </div>
        </div>

        {/* Brand Text */}
        <div className={`space-y-6 mb-12 transition-all duration-1000 delay-500 ${currentPhase >= 1 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="space-y-4">
            <p className="text-white/70 text-lg font-light tracking-wide">
              {t.welcome}
            </p>
            <h1 className="text-white text-4xl md:text-5xl font-bold tracking-tight leading-tight">
              <span className="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                {t.brandName}
              </span>
            </h1>
            <div className="space-y-2">
              <p className="text-white/90 text-lg md:text-xl font-medium tracking-wide">
                {t.tagline}
              </p>
              <p className="text-white/60 text-base font-light tracking-wide">
                {t.excellence}
              </p>
            </div>
          </div>
        </div>

        {/* Modern Progress Section */}
        {showProgress && (
          <div className={`space-y-6 transition-all duration-1000 delay-1000 ${currentPhase >= 2 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {/* Modern Progress Bar */}
            <div className="relative w-full max-w-xs mx-auto">
              <div className="w-full h-1 bg-white/10 rounded-full overflow-hidden backdrop-blur-sm border border-white/20">
                <div
                  className="h-full bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full transition-all duration-500 ease-out shadow-lg relative"
                  style={{ width: `${progress}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full animate-pulse"></div>
                </div>
              </div>

              {/* Progress glow */}
              <div
                className="absolute top-0 h-1 bg-gradient-to-r from-blue-400/50 via-purple-400/50 to-pink-400/50 rounded-full blur-sm transition-all duration-500"
                style={{ width: `${progress}%` }}
              />
            </div>

            {/* Loading Animation */}
            <div className="flex items-center justify-center space-x-3 rtl:space-x-reverse">
              <div className="flex space-x-1 rtl:space-x-reverse">
                <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-bounce"></div>
                <div className="w-1.5 h-1.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-bounce delay-100"></div>
                <div className="w-1.5 h-1.5 bg-gradient-to-r from-pink-400 to-blue-400 rounded-full animate-bounce delay-200"></div>
              </div>
              <span className="text-white/70 text-sm font-light tracking-wide ml-3 rtl:mr-3">
                {t.loading}
              </span>
            </div>

            {/* Progress Percentage with modern styling */}
            <div className="text-center">
              <span className="text-white/50 text-xs font-light tracking-widest">
                {Math.round(progress)}%
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Modern Bottom Decoration */}
      <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-black/30 via-black/10 to-transparent"></div>

      {/* Subtle grid pattern */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      />
    </div>
  );
};

export default SplashScreen;
