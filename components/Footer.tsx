'use client';

import React from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';
import { useFooterSettings, useSocialLinks, useContactInfo, useSiteSettings, useFooterCategories } from '../hooks/useSiteSettings';

interface FooterProps {
  locale: Locale;
}

const Footer: React.FC<FooterProps> = ({ locale }) => {
  const t = (key: string) => getTranslation(locale, key as keyof typeof import('../lib/translations').translations.ar);
  const { footerSettings } = useFooterSettings();
  const { socialLinks } = useSocialLinks();
  const { contactInfo } = useContactInfo();
  const { settings } = useSiteSettings();
  const { categories, loading: categoriesLoading } = useFooterCategories();

  return (
    <footer className="bg-gradient-to-r from-gray-900 to-gray-800 text-white">
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8">
          {/* Company Info - Enhanced */}
          <div className="lg:col-span-2 space-y-6">
            <div>
              <h3 className="text-2xl font-bold text-primary mb-4">
                {locale === 'ar' ? (settings?.siteNameAr || 'دروب هجر') : (settings?.siteName || 'DROOB HAJER')}
              </h3>
              <p className="text-gray-300 leading-relaxed mb-4">
                {locale === 'ar'
                  ? (footerSettings?.companyInfo?.descriptionAr || 'نحن مورد رائد لمعدات المطاعم والفنادق عالية الجودة، نقدم حلولاً شاملة لصناعة الضيافة.')
                  : (footerSettings?.companyInfo?.description || 'We are a leading supplier of high-quality restaurant and hotel equipment, providing comprehensive solutions for the hospitality industry.')
                }
              </p>

              {/* Key Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-300">
                  <i className="ri-check-line text-green-400 text-lg font-bold"></i>
                  <span>{locale === 'ar' ? 'منتجات عالية الجودة' : 'High Quality Products'}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-300">
                  <i className="ri-check-line text-green-400 text-lg font-bold"></i>
                  <span>{locale === 'ar' ? 'خدمة عملاء متميزة' : 'Excellent Customer Service'}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-300">
                  <i className="ri-check-line text-green-400 text-lg font-bold"></i>
                  <span>{locale === 'ar' ? 'توصيل سريع' : 'Fast Delivery'}</span>
                </div>
                <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-300">
                  <i className="ri-check-line text-green-400 text-lg font-bold"></i>
                  <span>{locale === 'ar' ? 'أسعار تنافسية' : 'Competitive Prices'}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Product Categories - New Section */}
          {footerSettings?.categoriesSection?.showCategories && !categoriesLoading && categories.length > 0 && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white border-b border-primary/30 pb-2">
                <i className="ri-menu-2-line text-primary-light mr-2 text-xl"></i>
                {locale === 'ar'
                  ? (footerSettings.categoriesSection.sectionTitleAr || 'فئات المنتجات')
                  : (footerSettings.categoriesSection.sectionTitle || 'Product Categories')
                }
              </h4>
              <ul className="space-y-2">
                {categories.slice(0, footerSettings.categoriesSection.maxCategories || 6).map((category) => (
                  <li key={category.id}>
                    <Link
                      href={`/${locale}/categories/${category.id}`}
                      className="text-gray-300 hover:text-primary transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group"
                    >
                      <i className="ri-arrow-right-s-line text-primary-light group-hover:translate-x-1 transition-transform duration-200 text-lg"></i>
                      <span>{locale === 'ar' ? category.name_ar : category.name}</span>
                    </Link>
                  </li>
                ))}
              </ul>
              <Link
                href={`/${locale}/categories`}
                className="inline-flex items-center text-primary hover:text-primary-light transition-colors duration-200 text-sm font-medium"
              >
                <span>{locale === 'ar' ? 'عرض جميع الفئات' : 'View All Categories'}</span>
                <i className={`ri-arrow-${locale === 'ar' ? 'left' : 'right'}-line ml-1`}></i>
              </Link>
            </div>
          )}

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white border-b border-primary/30 pb-2">
              <i className="ri-links-line text-primary-light mr-2 text-xl"></i>
              {locale === 'ar' ? 'روابط سريعة' : 'Quick Links'}
            </h4>
            <ul className="space-y-2">
              {footerSettings?.quickLinks?.filter(link => link.isActive).map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.url.startsWith('/') ? `/${locale}${link.url === '/' ? '' : link.url}` : link.url}
                    className="text-gray-300 hover:text-primary transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group"
                  >
                    <i className="ri-arrow-right-s-line text-primary group-hover:translate-x-1 transition-transform duration-200"></i>
                    <span>{locale === 'ar' ? link.nameAr : link.nameEn}</span>
                  </Link>
                </li>
              )) || (
                <>
                  <li>
                    <Link href={`/${locale}`} className="text-gray-300 hover:text-primary transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group">
                      <i className="ri-home-4-line text-primary-light text-lg"></i>
                      <span>{t('home')}</span>
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/products`} className="text-gray-300 hover:text-primary transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group">
                      <i className="ri-shopping-bag-3-line text-primary-light text-lg"></i>
                      <span>{t('products')}</span>
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/categories`} className="text-gray-300 hover:text-primary transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group">
                      <i className="ri-menu-2-line text-primary-light text-lg"></i>
                      <span>{t('categories')}</span>
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/about`} className="text-gray-300 hover:text-primary transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group">
                      <i className="ri-information-line text-primary-light text-lg"></i>
                      <span>{t('about')}</span>
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/contact`} className="text-gray-300 hover:text-primary transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse group">
                      <i className="ri-customer-service-2-line text-primary-light text-lg"></i>
                      <span>{t('contact')}</span>
                    </Link>
                  </li>
                </>
              )}
            </ul>
          </div>

          {/* Contact Info - Enhanced */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white border-b border-primary/30 pb-2">
              <i className="ri-customer-service-2-line text-primary-light mr-2 text-xl"></i>
              {locale === 'ar' ? 'معلومات التواصل' : 'Contact Info'}
            </h4>
            <ul className="space-y-3">
              {footerSettings?.companyInfo?.showPhone && contactInfo?.phone && (
                <li className="flex items-center space-x-3 space-x-reverse">
                  <i className="ri-phone-line text-primary-light text-lg"></i>
                  <a
                    href={`tel:${contactInfo.phone}`}
                    className="text-gray-300 hover:text-primary transition-colors duration-200 phone-number"
                  >
                    {contactInfo.phone}
                  </a>
                </li>
              )}
              {footerSettings?.companyInfo?.showEmail && contactInfo?.email && (
                <li className="flex items-center space-x-3 space-x-reverse">
                  <i className="ri-mail-line text-primary-light text-lg"></i>
                  <a href={`mailto:${contactInfo.email}`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                    {contactInfo.email}
                  </a>
                </li>
              )}
              {footerSettings?.companyInfo?.showAddress && contactInfo?.address && (
                <li className="flex items-center space-x-3 space-x-reverse">
                  <i className="ri-map-pin-line text-primary-light text-lg"></i>
                  <span className="text-gray-300">
                    {locale === 'ar' ? contactInfo.addressAr : contactInfo.address}
                  </span>
                </li>
              )}
            </ul>
          </div>

          {/* Social Media - Enhanced */}
          {footerSettings?.showSocialLinks && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white border-b border-primary/30 pb-2">
                <i className="ri-share-line text-primary-light mr-2 text-xl"></i>
                {locale === 'ar' ? 'تابعنا' : 'Follow Us'}
              </h4>
              <div className="flex flex-wrap gap-3">
                {socialLinks?.facebook && (
                  <a
                    href={socialLinks.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-700 hover:bg-blue-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 group"
                    aria-label="Facebook"
                  >
                    <i className="ri-facebook-fill text-white group-hover:text-white"></i>
                  </a>
                )}
                {socialLinks?.instagram && (
                  <a
                    href={socialLinks.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-700 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 group"
                    aria-label="Instagram"
                  >
                    <i className="ri-instagram-line text-white group-hover:text-white"></i>
                  </a>
                )}
                {socialLinks?.twitter && (
                  <a
                    href={socialLinks.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-700 hover:bg-blue-400 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 group"
                    aria-label="Twitter"
                  >
                    <i className="ri-twitter-line text-white group-hover:text-white"></i>
                  </a>
                )}
                {socialLinks?.linkedin && (
                  <a
                    href={socialLinks.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-700 hover:bg-blue-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 group"
                    aria-label="LinkedIn"
                  >
                    <i className="ri-linkedin-fill text-white group-hover:text-white"></i>
                  </a>
                )}
                {socialLinks?.youtube && (
                  <a
                    href={socialLinks.youtube}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-gray-700 hover:bg-red-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 group"
                    aria-label="YouTube"
                  >
                    <i className="ri-youtube-fill text-white group-hover:text-white"></i>
                  </a>
                )}
              </div>

              {/* WhatsApp Contact */}
              {contactInfo?.whatsapp && (
                <div className="pt-2">
                  <a
                    href={`https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 text-sm font-medium"
                  >
                    <i className="ri-whatsapp-line"></i>
                    <span>{locale === 'ar' ? 'تواصل عبر واتساب' : 'Contact via WhatsApp'}</span>
                  </a>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Enhanced Bottom Section */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-center md:text-left">
              <p className="text-gray-400 text-sm">
                © 2025 {locale === 'ar' ? (settings?.siteNameAr || 'دروب هجر') : (settings?.siteName || 'DROOB HAJER')}. {' '}
                {locale === 'ar'
                  ? (footerSettings?.copyrightTextAr || 'جميع الحقوق محفوظة.')
                  : (footerSettings?.copyrightText || 'All rights reserved.')
                }
              </p>
              <p className="text-gray-500 text-xs mt-1">
                {locale === 'ar'
                  ? 'مورد رائد لمعدات الفنادق والمطاعم في المملكة العربية السعودية'
                  : 'Leading supplier of hotel and restaurant equipment in Saudi Arabia'
                }
              </p>
            </div>

            {/* Additional Links */}
            <div className="flex items-center space-x-6 rtl:space-x-reverse text-sm">
              <Link
                href={`/${locale}/privacy`}
                className="text-gray-400 hover:text-primary transition-colors duration-200"
              >
                {locale === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy'}
              </Link>
              <Link
                href={`/${locale}/terms`}
                className="text-gray-400 hover:text-primary transition-colors duration-200"
              >
                {locale === 'ar' ? 'شروط الاستخدام' : 'Terms of Use'}
              </Link>
              <Link
                href={`/${locale}/sitemap`}
                className="text-gray-400 hover:text-primary transition-colors duration-200"
              >
                {locale === 'ar' ? 'خريطة الموقع' : 'Sitemap'}
              </Link>
            </div>
          </div>

          {/* SEO Schema for Organization */}
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                "@context": "https://schema.org",
                "@type": "Organization",
                "name": locale === 'ar' ? (settings?.siteNameAr || 'دروب هجر') : (settings?.siteName || 'DROOB HAJER'),
                "url": `https://droobhajer.com/${locale}`,
                "logo": "https://droobhajer.com/logo.png",
                "description": locale === 'ar'
                  ? (footerSettings?.companyInfo?.descriptionAr || 'مورد رائد لمعدات المطاعم والفنادق')
                  : (footerSettings?.companyInfo?.description || 'Leading supplier of restaurant and hotel equipment'),
                "address": {
                  "@type": "PostalAddress",
                  "addressCountry": "SA",
                  "addressLocality": locale === 'ar' ? 'الرياض' : 'Riyadh'
                },
                "contactPoint": {
                  "@type": "ContactPoint",
                  "telephone": contactInfo?.phone || '+966 11 234 5678',
                  "contactType": "customer service",
                  "availableLanguage": ["ar", "en"]
                },
                "sameAs": [
                  socialLinks?.facebook,
                  socialLinks?.instagram,
                  socialLinks?.twitter,
                  socialLinks?.linkedin,
                  socialLinks?.youtube
                ].filter(Boolean)
              })
            }}
          />
        </div>
      </div>
    </footer>
  );
};

export default Footer;
