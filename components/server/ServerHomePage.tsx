import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Locale } from '../../lib/i18n';
import { ProductWithDetails, Category } from '../../types/mysql-database';
import { generateProductUrl } from '../../utils/generateSlug';
import ServerNavbar from './ServerNavbar';
import ServerFooter from './ServerFooter';

interface ServerHomePageProps {
  locale: Locale;
  categories: Category[];
  featuredProducts: ProductWithDetails[];
}

const ServerHomePage: React.FC<ServerHomePageProps> = ({
  locale,
  categories,
  featuredProducts
}) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <ServerNavbar locale={locale} />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary via-blue-600 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
          </h1>
          <p className="text-xl mb-8 opacity-90">
            {locale === 'ar' 
              ? 'التجهيزات الفندقية الاحترافية وحلول البوفيه المتطورة'
              : 'Professional Hotel Equipment & Advanced Buffet Solutions'
            }
          </p>
          <Link
            href={`/${locale}/products`}
            className="inline-block bg-white text-primary px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors"
          >
            {locale === 'ar' ? 'تصفح المنتجات' : 'Browse Products'}
          </Link>
        </div>
      </section>

      {/* Categories Section */}
      {categories.length > 0 && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {locale === 'ar' ? 'فئات المنتجات' : 'Product Categories'}
              </h2>
              <p className="text-gray-600 text-lg">
                {locale === 'ar' 
                  ? 'اكتشف مجموعتنا الواسعة من معدات الفنادق والبوفيه'
                  : 'Discover our wide range of hotel and buffet equipment'
                }
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {categories.slice(0, 8).map((category) => (
                <Link
                  key={category.id}
                  href={`/${locale}/products?category=${category.id}`}
                  className="bg-gray-50 rounded-xl p-6 text-center hover:shadow-lg transition-all hover:bg-white group"
                >
                  {category.image_url && (
                    <div className="w-20 h-20 mx-auto mb-4 rounded-lg overflow-hidden">
                      <Image
                        src={category.image_url}
                        alt={locale === 'ar' ? category.name_ar : category.name}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform"
                        loading="lazy"
                      />
                    </div>
                  )}
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {locale === 'ar' ? category.name_ar : category.name}
                  </h3>
                  {category.description && (
                    <p className="text-sm text-gray-600">
                      {locale === 'ar' ? category.description_ar : category.description}
                    </p>
                  )}
                </Link>
              ))}
            </div>
            
            {categories.length > 8 && (
              <div className="text-center mt-8">
                <Link
                  href={`/${locale}/products`}
                  className="inline-block bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-dark transition-colors"
                >
                  {locale === 'ar' ? 'عرض جميع الفئات' : 'View All Categories'}
                </Link>
              </div>
            )}
          </div>
        </section>
      )}

      {/* Featured Products Section */}
      {featuredProducts.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
              </h2>
              <p className="text-gray-600 text-lg">
                {locale === 'ar' 
                  ? 'أحدث وأفضل منتجاتنا عالية الجودة'
                  : 'Our latest and best high-quality products'
                }
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredProducts.map((product) => {
                const productTitle = locale === 'ar' ? product.title_ar : product.title;
                const productUrl = generateProductUrl(product, locale);
                
                return (
                  <Link
                    key={product.id}
                    href={productUrl}
                    className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all group"
                  >
                    {/* Product Image */}
                    <div className="aspect-square bg-gray-100 overflow-hidden">
                      {product.images && product.images.length > 0 ? (
                        <Image
                          src={product.images[0].image_url}
                          alt={productTitle}
                          width={400}
                          height={400}
                          className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                          loading="lazy"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          <div className="text-center">
                            <div className="text-6xl mb-2">📷</div>
                            <p>{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="p-6">
                      <h3 className="font-bold text-gray-900 mb-2 text-lg line-clamp-2">
                        {productTitle}
                      </h3>
                      
                      {product.description && (
                        <p className="text-gray-600 mb-4 line-clamp-2">
                          {locale === 'ar' ? product.description_ar : product.description}
                        </p>
                      )}

                      {/* Price */}
                      {product.price && (
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-2">
                            <span className="text-xl font-bold text-primary">
                              {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                            </span>
                            {product.original_price && product.original_price > product.price && (
                              <span className="text-sm text-gray-400 line-through">
                                {product.original_price}
                              </span>
                            )}
                          </div>
                          
                          {/* Availability */}
                          <div className={`text-xs px-3 py-1 rounded-full ${
                            product.is_available 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {product.is_available 
                              ? (locale === 'ar' ? 'متوفر' : 'Available')
                              : (locale === 'ar' ? 'غير متوفر' : 'Out of Stock')
                            }
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex space-x-3">
                        <div className="flex-1 bg-primary text-white text-center py-3 rounded-lg font-semibold">
                          {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                        </div>
                        <a
                          href={`https://wa.me/966599252259?text=${encodeURIComponent(`${locale === 'ar' ? 'مرحباً، أريد الاستفسار عن هذا المنتج:' : 'Hello, I want to inquire about this product:'} ${productTitle}`)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-14 h-12 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <span className="text-xl">📱</span>
                        </a>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
            
            <div className="text-center mt-12">
              <Link
                href={`/${locale}/products`}
                className="inline-block bg-primary text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-primary-dark transition-colors"
              >
                {locale === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Contact Section */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">
            {locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}
          </h2>
          <p className="text-xl mb-8 opacity-90">
            {locale === 'ar' 
              ? 'نحن هنا لمساعدتك في اختيار أفضل المعدات لمشروعك'
              : 'We are here to help you choose the best equipment for your project'
            }
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://wa.me/966599252259"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center bg-green-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-600 transition-colors"
            >
              <span className="mr-2">📱</span>
              {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
            </a>
            <a
              href="tel:+966599252259"
              className="inline-flex items-center justify-center bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              <span className="mr-2">📞</span>
              {locale === 'ar' ? 'اتصال' : 'Call'}
            </a>
          </div>
        </div>
      </section>

      <ServerFooter locale={locale} />
    </div>
  );
};

export default ServerHomePage;
