import React from 'react';
import Link from 'next/link';
import { Locale } from '../../lib/i18n';

interface ServerNavbarProps {
  locale: Locale;
}

const ServerNavbar: React.FC<ServerNavbarProps> = ({ locale }) => {
  return (
    <nav className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">DH</span>
            </div>
            <div className="hidden sm:block">
              <div className="font-bold text-gray-900 text-lg">
                {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
              </div>
              <div className="text-xs text-gray-600">
                {locale === 'ar' ? 'معدات فندقية' : 'Hotel Equipment'}
              </div>
            </div>
          </Link>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href={`/${locale}`}
              className="text-gray-700 hover:text-primary font-medium transition-colors"
            >
              {locale === 'ar' ? 'الرئيسية' : 'Home'}
            </Link>
            <Link
              href={`/${locale}/products`}
              className="text-gray-700 hover:text-primary font-medium transition-colors"
            >
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </Link>
            <Link
              href={`/${locale}/about`}
              className="text-gray-700 hover:text-primary font-medium transition-colors"
            >
              {locale === 'ar' ? 'من نحن' : 'About'}
            </Link>
            <Link
              href={`/${locale}/contact`}
              className="text-gray-700 hover:text-primary font-medium transition-colors"
            >
              {locale === 'ar' ? 'تواصل معنا' : 'Contact'}
            </Link>
          </div>

          {/* Language Switcher & Contact */}
          <div className="flex items-center space-x-4">
            {/* Language Switcher */}
            <div className="flex items-center space-x-2">
              <Link
                href={locale === 'ar' ? '/en' : '/ar'}
                className="text-sm text-gray-600 hover:text-primary transition-colors"
              >
                {locale === 'ar' ? 'EN' : 'العربية'}
              </Link>
            </div>

            {/* Contact Buttons */}
            <div className="flex items-center space-x-2">
              <a
                href="https://wa.me/966599252259"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors"
                title={locale === 'ar' ? 'واتساب' : 'WhatsApp'}
              >
                <span className="text-lg">📱</span>
              </a>
              <a
                href="tel:+966599252259"
                className="w-10 h-10 bg-primary text-white rounded-lg flex items-center justify-center hover:bg-primary-dark transition-colors"
                title={locale === 'ar' ? 'اتصال' : 'Call'}
              >
                <span className="text-lg">📞</span>
              </a>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <div className="w-8 h-8 flex flex-col justify-center items-center space-y-1 cursor-pointer">
                <div className="w-5 h-0.5 bg-gray-600"></div>
                <div className="w-5 h-0.5 bg-gray-600"></div>
                <div className="w-5 h-0.5 bg-gray-600"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <div className="md:hidden border-t border-gray-200 py-4">
          <div className="flex flex-col space-y-3">
            <Link
              href={`/${locale}`}
              className="text-gray-700 hover:text-primary font-medium transition-colors py-2"
            >
              {locale === 'ar' ? 'الرئيسية' : 'Home'}
            </Link>
            <Link
              href={`/${locale}/products`}
              className="text-gray-700 hover:text-primary font-medium transition-colors py-2"
            >
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </Link>
            <Link
              href={`/${locale}/about`}
              className="text-gray-700 hover:text-primary font-medium transition-colors py-2"
            >
              {locale === 'ar' ? 'من نحن' : 'About'}
            </Link>
            <Link
              href={`/${locale}/contact`}
              className="text-gray-700 hover:text-primary font-medium transition-colors py-2"
            >
              {locale === 'ar' ? 'تواصل معنا' : 'Contact'}
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default ServerNavbar;
