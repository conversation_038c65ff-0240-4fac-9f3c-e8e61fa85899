import React from 'react';
import Link from 'next/link';
import { Locale } from '../../lib/i18n';

interface ServerFooterProps {
  locale: Locale;
}

const ServerFooter: React.FC<ServerFooterProps> = ({ locale }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">DH</span>
              </div>
              <div>
                <div className="font-bold text-xl">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </div>
                <div className="text-sm text-gray-400">
                  {locale === 'ar' ? 'معدات فندقية احترافية' : 'Professional Hotel Equipment'}
                </div>
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {locale === 'ar' 
                ? 'نحن متخصصون في توفير أفضل معدات الفنادق والبوفيه عالية الجودة. نقدم حلول شاملة لجميع احتياجات الضيافة والمطاعم.'
                : 'We specialize in providing the best high-quality hotel and buffet equipment. We offer comprehensive solutions for all hospitality and restaurant needs.'
              }
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <span className="text-primary">📞</span>
                <a href="tel:+966599252259" className="text-gray-300 hover:text-white transition-colors">
                  +966 599 252 259
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-primary">📱</span>
                <a 
                  href="https://wa.me/966599252259" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-primary">📍</span>
                <span className="text-gray-300">
                  {locale === 'ar' ? 'الرياض، المملكة العربية السعودية' : 'Riyadh, Saudi Arabia'}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-bold text-lg mb-4">
              {locale === 'ar' ? 'روابط سريعة' : 'Quick Links'}
            </h3>
            <ul className="space-y-3">
              <li>
                <Link href={`/${locale}`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'الرئيسية' : 'Home'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/products`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'المنتجات' : 'Products'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/about`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'من نحن' : 'About Us'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/contact`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}
                </Link>
              </li>
            </ul>
          </div>

          {/* Product Categories */}
          <div>
            <h3 className="font-bold text-lg mb-4">
              {locale === 'ar' ? 'فئات المنتجات' : 'Product Categories'}
            </h3>
            <ul className="space-y-3">
              <li>
                <Link href={`/${locale}/products?category=buffetware`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'أدوات البوفيه' : 'Buffetware'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/products?category=kitchen`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'معدات المطبخ' : 'Kitchen Equipment'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/products?category=serving`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'أدوات التقديم' : 'Serving Equipment'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/products?category=storage`} className="text-gray-300 hover:text-white transition-colors">
                  {locale === 'ar' ? 'حلول التخزين' : 'Storage Solutions'}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}. 
              {locale === 'ar' ? ' جميع الحقوق محفوظة.' : ' All rights reserved.'}
            </div>
            
            <div className="flex items-center space-x-6">
              <Link href={`/${locale}/privacy`} className="text-gray-400 hover:text-white text-sm transition-colors">
                {locale === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy'}
              </Link>
              <Link href={`/${locale}/terms`} className="text-gray-400 hover:text-white text-sm transition-colors">
                {locale === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions'}
              </Link>
              <div className="flex items-center space-x-2">
                <Link
                  href={locale === 'ar' ? '/en' : '/ar'}
                  className="text-gray-400 hover:text-white text-sm transition-colors"
                >
                  {locale === 'ar' ? 'English' : 'العربية'}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default ServerFooter;
