'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import Footer from './Footer';
import WhatsAppButton from './WhatsAppButton';
import { useSiteSettings } from '../hooks/useSiteSettings';

interface ContactContentProps {
  locale: Locale;
}

export default function ContactContent({ locale }: ContactContentProps) {
  const { settings, loading } = useSiteSettings();
  


  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getContactContent = () => {
    if (!settings) {
      return {
        title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
        subtitle: locale === 'ar' ? 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك' : 'We are here to help you with all your inquiries and needs',
        formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
        name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
        email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
        phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
        subject: locale === 'ar' ? 'الموضوع' : 'Subject',
        message: locale === 'ar' ? 'الرسالة' : 'Message',
        send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
        sending: locale === 'ar' ? 'جاري الإرسال...' : 'Sending...',
        successMessage: locale === 'ar' ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.' : 'Your message has been sent successfully! We will contact you soon.',
        errorMessage: locale === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.',
        contactInfo: locale === 'ar' ? 'معلومات التواصل' : 'Contact Information',
        address: locale === 'ar' ? 'العنوان' : 'Address',
        phone_label: locale === 'ar' ? 'الهاتف' : 'Phone',
        email_label: locale === 'ar' ? 'البريد الإلكتروني' : 'Email',
        hours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours'
      };
    }

    // استخدام الإعدادات الافتراضية
    return {
      title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
      subtitle: locale === 'ar'
        ? 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك'
        : 'We are here to help you with all your inquiries and needs',
      formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
      name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
      email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
      phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
      subject: locale === 'ar' ? 'الموضوع' : 'Subject',
      message: locale === 'ar' ? 'الرسالة' : 'Message',
      send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
      sending: locale === 'ar' ? 'جاري الإرسال...' : 'Sending...',
      successMessage: locale === 'ar' ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.' : 'Your message has been sent successfully! We will contact you soon.',
      errorMessage: locale === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.',
      contactInfo: locale === 'ar' ? 'معلومات التواصل' : 'Contact Information',
      address: locale === 'ar' ? 'العنوان' : 'Address',
      phone_label: locale === 'ar' ? 'الهاتف' : 'Phone',
      email_label: locale === 'ar' ? 'البريد الإلكتروني' : 'Email',
      hours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours'
    };
  };

  const currentContent = getContactContent();



  if (loading) {
    return (
      <>
        <main className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <i className="ri-loader-4-line text-4xl text-primary animate-spin mb-4"></i>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </p>
          </div>
        </main>
        <Footer locale={locale} />
      </>
    );
  }

  return (
    <>
      <main>
        {/* Hero Section */}
        <section className="bg-primary py-16">
          <div className="container mx-auto px-4">
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {currentContent.title}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form & Info */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Google Maps Location */}
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div className="p-6 border-b border-gray-100">
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">
                    {locale === 'ar' ? 'موقعنا على الخريطة' : 'Our Location'}
                  </h2>
                  <p className="text-gray-600">
                    {locale === 'ar'
                      ? 'يمكنك العثور علينا في هذا الموقع'
                      : 'You can find us at this location'
                    }
                  </p>
                </div>

                {/* Google Maps Embed */}
                <div className="relative h-96">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.4!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjTCsDQyJzQ5LjAiTiA0NsKwNDAnMzEuMSJF!5e0!3m2!1sen!2ssa!4v1234567890"
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title={locale === 'ar' ? 'موقع دروب هاجر' : 'DROOB HAJER Location'}
                  ></iframe>
                </div>

                {/* Map Actions */}
                <div className="p-6 bg-gray-50">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <a
                      href="https://maps.google.com/?q=24.7136,46.6753"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-primary text-white px-4 py-3 rounded-lg font-medium text-center hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
                    >
                      <i className="ri-map-pin-line"></i>
                      {locale === 'ar' ? 'فتح في خرائط جوجل' : 'Open in Google Maps'}
                    </a>
                    <a
                      href="https://www.google.com/maps/dir/?api=1&destination=24.7136,46.6753"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-blue-600 text-white px-4 py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                    >
                      <i className="ri-navigation-line"></i>
                      {locale === 'ar' ? 'الحصول على الاتجاهات' : 'Get Directions'}
                    </a>
                  </div>
                </div>
              </div>


              {/* Contact Information */}
              <div className="space-y-8">
                <div className="bg-gray-50 rounded-2xl p-8">
                  <h3 className="text-2xl font-bold text-gray-800 mb-6">
                    {currentContent.contactInfo}
                  </h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-map-pin-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.address}</h4>
                        <p className="text-gray-600">
                          {locale === 'ar'
                            ? (settings?.addressAr || 'الرياض، المملكة العربية السعودية')
                            : (settings?.address || 'Riyadh, Saudi Arabia')
                          }
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-phone-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.phone_label}</h4>
                        <p className="text-gray-600 phone-number">
                          {settings?.phone || '+966 XX XXX XXXX'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-mail-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.email_label}</h4>
                        <p className="text-gray-600">
                          {settings?.contactEmail || '<EMAIL>'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-time-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">{currentContent.hours}</h4>
                        <p className="text-gray-600">
                          {locale === 'ar'
                            ? (settings?.workingHoursAr || 'السبت - الخميس: 9:00 ص - 6:00 م')
                            : (settings?.workingHours || 'Saturday - Thursday: 9:00 AM - 6:00 PM')
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
