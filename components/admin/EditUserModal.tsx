import React, { useState, useEffect } from 'react';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  lastLogin: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: AdminUser | null;
  onUserUpdated: () => void;
}

interface FormData {
  username: string;
  email: string;
  newPassword: string;
  confirmPassword: string;
}

interface FormErrors {
  username?: string;
  email?: string;
  newPassword?: string;
  confirmPassword?: string;
  general?: string;
}

const EditUserModal: React.FC<EditUserModalProps> = ({ isOpen, onClose, user, onUserUpdated }) => {
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswordSection, setShowPasswordSection] = useState(false);

  // تحديث البيانات عند تغيير المستخدم
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username,
        email: user.email,
        newPassword: '',
        confirmPassword: ''
      });
      setErrors({});
      setShowPasswordSection(false);
    }
  }, [user]);

  // إعادة تعيين النموذج
  const resetForm = () => {
    if (user) {
      setFormData({
        username: user.username,
        email: user.email,
        newPassword: '',
        confirmPassword: ''
      });
    }
    setErrors({});
    setShowPasswordSection(false);
  };

  // التحقق من صحة البيانات
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // التحقق من اسم المستخدم
    if (!formData.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    } else if (formData.username.length < 3) {
      newErrors.username = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
    }

    // التحقق من البريد الإلكتروني
    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح';
    }

    // التحقق من كلمة المرور إذا كان قسم كلمة المرور مفعل
    if (showPasswordSection) {
      if (!formData.newPassword.trim()) {
        newErrors.newPassword = 'كلمة المرور الجديدة مطلوبة';
      } else if (formData.newPassword.length < 6) {
        newErrors.newPassword = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      if (!formData.confirmPassword.trim()) {
        newErrors.confirmPassword = 'تأكيد كلمة المرور مطلوب';
      } else if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = 'كلمة المرور وتأكيدها غير متطابقتين';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !user) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const token = localStorage.getItem('authToken') ||
                   document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

      // تحديث البيانات الأساسية
      const updateResponse = await fetch('/api/admin/users/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include',
        body: JSON.stringify({
          userId: user.id,
          username: formData.username,
          email: formData.email
        })
      });

      const updateData = await updateResponse.json();

      if (!updateData.success) {
        setErrors({ general: updateData.message || 'حدث خطأ أثناء تحديث المستخدم' });
        return;
      }

      // تغيير كلمة المرور إذا كان مطلوباً
      if (showPasswordSection && formData.newPassword) {
        const passwordResponse = await fetch('/api/admin/users/reset-password', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          credentials: 'include',
          body: JSON.stringify({
            userId: user.id,
            newPassword: formData.newPassword
          })
        });

        const passwordData = await passwordResponse.json();

        if (!passwordData.success) {
          setErrors({ general: passwordData.message || 'حدث خطأ أثناء تغيير كلمة المرور' });
          return;
        }
      }

      onUserUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating user:', error);
      setErrors({ general: 'حدث خطأ في الاتصال بالخادم' });
    } finally {
      setIsLoading(false);
    }
  };

  // إغلاق المودال
  const handleClose = () => {
    if (!isLoading) {
      resetForm();
      onClose();
    }
  };

  if (!isOpen || !user) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            <i className="ri-edit-line text-2xl ml-3 text-primary"></i>
            تعديل المستخدم
          </h2>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <i className="ri-close-line text-2xl"></i>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* خطأ عام */}
          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
              <i className="ri-error-warning-line text-lg ml-2"></i>
              {errors.general}
            </div>
          )}

          {/* معلومات المستخدم الحالي */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">المستخدم الحالي:</h3>
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <i className="ri-user-line text-primary"></i>
              </div>
              <div>
                <p className="font-medium text-gray-800">{user.username}</p>
                <p className="text-sm text-gray-600">{user.email}</p>
              </div>
            </div>
          </div>

          {/* اسم المستخدم */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم المستخدم *
            </label>
            <input
              type="text"
              value={formData.username}
              onChange={(e) => setFormData({ ...formData, username: e.target.value })}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors ${
                errors.username ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="أدخل اسم المستخدم"
              disabled={isLoading}
            />
            {errors.username && (
              <p className="text-red-600 text-sm mt-1">{errors.username}</p>
            )}
          </div>

          {/* البريد الإلكتروني */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              البريد الإلكتروني *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors ${
                errors.email ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="أدخل البريد الإلكتروني"
              disabled={isLoading}
            />
            {errors.email && (
              <p className="text-red-600 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          {/* قسم تغيير كلمة المرور */}
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-700">تغيير كلمة المرور</h3>
              <button
                type="button"
                onClick={() => {
                  setShowPasswordSection(!showPasswordSection);
                  if (showPasswordSection) {
                    setFormData({ ...formData, newPassword: '', confirmPassword: '' });
                    setErrors({ ...errors, newPassword: undefined, confirmPassword: undefined });
                  }
                }}
                className="text-sm text-primary hover:text-primary/80 transition-colors"
              >
                {showPasswordSection ? 'إلغاء' : 'تغيير كلمة المرور'}
              </button>
            </div>

            {showPasswordSection && (
              <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
                {/* كلمة المرور الجديدة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الجديدة *
                  </label>
                  <input
                    type="password"
                    value={formData.newPassword}
                    onChange={(e) => setFormData({ ...formData, newPassword: e.target.value })}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors ${
                      errors.newPassword ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="أدخل كلمة المرور الجديدة"
                    disabled={isLoading}
                  />
                  {errors.newPassword && (
                    <p className="text-red-600 text-sm mt-1">{errors.newPassword}</p>
                  )}
                </div>

                {/* تأكيد كلمة المرور */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تأكيد كلمة المرور *
                  </label>
                  <input
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors ${
                      errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="أعد إدخال كلمة المرور"
                    disabled={isLoading}
                  />
                  {errors.confirmPassword && (
                    <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>
                  )}
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <p className="text-xs text-yellow-800">
                    ⚠️ سيتم تغيير كلمة مرور المستخدم فوراً بدون الحاجة لكلمة المرور السابقة
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* أزرار التحكم */}
          <div className="flex space-x-3 space-x-reverse pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري التحديث...
                </>
              ) : (
                <>
                  <i className="ri-save-line text-lg ml-2"></i>
                  حفظ التغييرات
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditUserModal;
