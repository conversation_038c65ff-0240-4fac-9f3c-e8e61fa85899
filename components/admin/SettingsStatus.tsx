import React, { useState, useEffect } from 'react';

interface SettingsStatusProps {
  onRefresh?: () => void;
}

const SettingsStatus: React.FC<SettingsStatusProps> = ({ onRefresh }) => {
  const [apiStatus, setApiStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [localStorageStatus, setLocalStorageStatus] = useState<'available' | 'unavailable'>('unavailable');
  const [fileSystemStatus, setFileSystemStatus] = useState<'unknown' | 'working' | 'error'>('unknown');
  const [settingsSource, setSettingsSource] = useState<'file' | 'localStorage' | 'default'>('default');
  const [testResults, setTestResults] = useState<{
    success: boolean;
    message: string;
    results?: {
      filePermissions: boolean;
      fileRead: boolean;
      fileWrite: boolean;
      localStorageRead: boolean;
    };
    recommendations?: string[];
  } | null>(null);
  const [isRunningTest, setIsRunningTest] = useState(false);

  const checkApiStatus = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setApiStatus('success');
        // تحديد مصدر البيانات
        if (data.lastUpdated) {
          setSettingsSource('file');
          setFileSystemStatus('working');
        } else if (data.siteName) {
          setSettingsSource('localStorage');
        } else {
          setSettingsSource('default');
        }
      } else {
        setApiStatus('error');
        setFileSystemStatus('error');
      }
    } catch {
      setApiStatus('error');
      setFileSystemStatus('error');
    }
  };

  const checkLocalStorage = () => {
    try {
      const settings = localStorage.getItem('siteSettings');
      setLocalStorageStatus(settings ? 'available' : 'unavailable');
    } catch {
      setLocalStorageStatus('unavailable');
    }
  };

  const handleRefresh = () => {
    setApiStatus('loading');
    checkApiStatus();
    checkLocalStorage();
    if (onRefresh) {
      onRefresh();
    }
  };

  const runSystemTest = async () => {
    setIsRunningTest(true);
    setTestResults(null);

    try {
      const response = await fetch('/api/test-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults(data);
      } else {
        setTestResults({
          success: false,
          message: 'فشل في تشغيل الاختبار'
        });
      }
    } catch {
      setTestResults({
        success: false,
        message: 'خطأ في الاتصال بالخادم'
      });
    } finally {
      setIsRunningTest(false);
    }
  };

  useEffect(() => {
    checkApiStatus();
    checkLocalStorage();
  }, []);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
          <i className="ri-settings-3-line text-xl ml-2 text-primary"></i>
          حالة نظام الإعدادات
        </h3>
        <div className="flex gap-2">
          <button
            onClick={handleRefresh}
            className="px-3 py-1 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-sm flex items-center"
          >
            <i className="ri-refresh-line text-sm ml-1"></i>
            تحديث
          </button>
          <button
            onClick={runSystemTest}
            disabled={isRunningTest}
            className="px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors text-sm flex items-center"
          >
            {isRunningTest ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white ml-1"></div>
                اختبار...
              </>
            ) : (
              <>
                <i className="ri-bug-line text-sm ml-1"></i>
                اختبار النظام
              </>
            )}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* API Status */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className={`w-3 h-3 rounded-full ${
            apiStatus === 'loading' ? 'bg-yellow-500 animate-pulse' :
            apiStatus === 'success' ? 'bg-green-500' : 'bg-red-500'
          }`}></div>
          <div>
            <p className="text-sm font-medium text-gray-700">API الإعدادات</p>
            <p className="text-xs text-gray-500">
              {apiStatus === 'loading' ? 'جاري التحقق...' :
               apiStatus === 'success' ? 'يعمل بشكل طبيعي' : 'خطأ في الاتصال'}
            </p>
          </div>
        </div>

        {/* Local Storage Status */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className={`w-3 h-3 rounded-full ${
            localStorageStatus === 'available' ? 'bg-green-500' : 'bg-red-500'
          }`}></div>
          <div>
            <p className="text-sm font-medium text-gray-700">التخزين المحلي</p>
            <p className="text-xs text-gray-500">
              {localStorageStatus === 'available' ? 'متوفر' : 'غير متوفر'}
            </p>
          </div>
        </div>

        {/* File System Status */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className={`w-3 h-3 rounded-full ${
            fileSystemStatus === 'unknown' ? 'bg-gray-500' :
            fileSystemStatus === 'working' ? 'bg-green-500' : 'bg-red-500'
          }`}></div>
          <div>
            <p className="text-sm font-medium text-gray-700">نظام الملفات</p>
            <p className="text-xs text-gray-500">
              {fileSystemStatus === 'unknown' ? 'غير معروف' :
               fileSystemStatus === 'working' ? 'يعمل' : 'خطأ'}
            </p>
          </div>
        </div>

        {/* Settings Source */}
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className={`w-3 h-3 rounded-full ${
            settingsSource === 'file' ? 'bg-green-500' :
            settingsSource === 'localStorage' ? 'bg-yellow-500' : 'bg-gray-500'
          }`}></div>
          <div>
            <p className="text-sm font-medium text-gray-700">مصدر البيانات</p>
            <p className="text-xs text-gray-500">
              {settingsSource === 'file' ? 'ملف' :
               settingsSource === 'localStorage' ? 'تخزين محلي' : 'افتراضي'}
            </p>
          </div>
        </div>
      </div>

      {/* Status Messages */}
      {apiStatus === 'error' && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center">
            <i className="ri-error-warning-line text-red-600 text-lg ml-2"></i>
            <div>
              <p className="text-red-800 font-medium text-sm">خطأ في API الإعدادات</p>
              <p className="text-red-700 text-xs mt-1">
                تعذر الاتصال بـ API الإعدادات. تحقق من أن الخادم يعمل بشكل صحيح.
              </p>
            </div>
          </div>
        </div>
      )}

      {localStorageStatus === 'unavailable' && (
        <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center">
            <i className="ri-alert-line text-yellow-600 text-lg ml-2"></i>
            <div>
              <p className="text-yellow-800 font-medium text-sm">تحذير: التخزين المحلي غير متوفر</p>
              <p className="text-yellow-700 text-xs mt-1">
                قد لا تنعكس التغييرات على الموقع فوراً. تأكد من تمكين JavaScript والتخزين المحلي.
              </p>
            </div>
          </div>
        </div>
      )}

      {apiStatus === 'success' && localStorageStatus === 'available' && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center">
            <i className="ri-check-line text-green-600 text-lg ml-2"></i>
            <div>
              <p className="text-green-800 font-medium text-sm">النظام يعمل بشكل طبيعي</p>
              <p className="text-green-700 text-xs mt-1">
                جميع الأنظمة تعمل بشكل صحيح. التغييرات ستنعكس على الموقع فوراً.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults && (
        <div className={`mt-4 border rounded-lg p-4 ${
          testResults.success ? 'bg-blue-50 border-blue-200' : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-center mb-3">
            <i className={`${testResults.success ? 'ri-check-line text-blue-600' : 'ri-error-warning-line text-red-600'} text-lg ml-2`}></i>
            <h4 className="font-medium text-gray-800">نتائج اختبار النظام</h4>
          </div>

          {testResults.results && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
              <div className={`text-center p-2 rounded ${testResults.results.filePermissions ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div className="text-xs font-medium">صلاحيات الملف</div>
                <div className="text-lg">{testResults.results.filePermissions ? '✅' : '❌'}</div>
              </div>
              <div className={`text-center p-2 rounded ${testResults.results.fileRead ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div className="text-xs font-medium">قراءة الملف</div>
                <div className="text-lg">{testResults.results.fileRead ? '✅' : '❌'}</div>
              </div>
              <div className={`text-center p-2 rounded ${testResults.results.fileWrite ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div className="text-xs font-medium">كتابة الملف</div>
                <div className="text-lg">{testResults.results.fileWrite ? '✅' : '❌'}</div>
              </div>
              <div className={`text-center p-2 rounded ${testResults.results.localStorageRead ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div className="text-xs font-medium">التخزين المحلي</div>
                <div className="text-lg">{testResults.results.localStorageRead ? '✅' : '❌'}</div>
              </div>
            </div>
          )}

          {testResults.recommendations && testResults.recommendations.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-700 mb-2">التوصيات:</h5>
              <ul className="text-sm text-gray-600 space-y-1">
                {testResults.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-500 ml-2">•</span>
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SettingsStatus;
