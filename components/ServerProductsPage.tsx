import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { ProductWithDetails, Category } from '../types/mysql-database';
import { generateProductUrl } from '../utils/generateSlug';
import ServerNavbar from './server/ServerNavbar';
import ServerFooter from './server/ServerFooter';

interface ServerProductsPageProps {
  locale: Locale;
  products: ProductWithDetails[];
  categories: Category[];
}

const ServerProductsPage: React.FC<ServerProductsPageProps> = ({
  locale,
  products,
  categories
}) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <ServerNavbar locale={locale} />
      
      {/* Static Content for SEO and No-JS users */}
      <div className="bg-white static-content">
        {/* Hero Section */}
        <div className="bg-primary py-12">
          <div className="container mx-auto px-4">
            <div className="text-center text-white">
              <h1 className="text-4xl font-bold mb-4">
                {locale === 'ar' ? 'منتجاتنا' : 'Our Products'}
              </h1>
              <p className="text-xl opacity-90">
                {locale === 'ar' 
                  ? 'اكتشف مجموعتنا الواسعة من معدات الفنادق والبوفيه عالية الجودة'
                  : 'Discover our wide range of high-quality hotel and buffet equipment'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Categories Section */}
        {categories.length > 0 && (
          <div className="container mx-auto px-4 py-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              {locale === 'ar' ? 'الفئات' : 'Categories'}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/${locale}/products?category=${category.id}`}
                  className="bg-white rounded-lg border border-gray-200 p-4 text-center hover:shadow-md transition-shadow"
                >
                  {category.image_url && (
                    <div className="w-16 h-16 mx-auto mb-3 rounded-lg overflow-hidden">
                      <Image
                        src={category.image_url}
                        alt={locale === 'ar' ? category.name_ar : category.name}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>
                  )}
                  <h3 className="font-semibold text-gray-900">
                    {locale === 'ar' ? category.name_ar : category.name}
                  </h3>
                  {category.description && (
                    <p className="text-sm text-gray-600 mt-1">
                      {locale === 'ar' ? category.description_ar : category.description}
                    </p>
                  )}
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Products Grid */}
        <div className="container mx-auto px-4 pb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
          </h2>
          
          {products.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => {
                const productTitle = locale === 'ar' ? product.title_ar : product.title;
                const productUrl = generateProductUrl(product, locale);
                
                return (
                  <Link
                    key={product.id}
                    href={productUrl}
                    className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow group"
                  >
                    {/* Product Image */}
                    <div className="aspect-square bg-gray-100 overflow-hidden">
                      {product.images && product.images.length > 0 ? (
                        <Image
                          src={product.images[0].image_url}
                          alt={productTitle}
                          width={300}
                          height={300}
                          className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                          loading="lazy"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          <div className="text-center">
                            <div className="text-4xl mb-2">📷</div>
                            <p className="text-sm">{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                        {productTitle}
                      </h3>
                      
                      {product.description && (
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                          {locale === 'ar' ? product.description_ar : product.description}
                        </p>
                      )}

                      {/* Price */}
                      {product.price && (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold text-primary">
                              {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                            </span>
                            {product.original_price && product.original_price > product.price && (
                              <span className="text-sm text-gray-400 line-through">
                                {product.original_price}
                              </span>
                            )}
                          </div>
                          
                          {/* Availability */}
                          <div className={`text-xs px-2 py-1 rounded-full ${
                            product.is_available 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {product.is_available 
                              ? (locale === 'ar' ? 'متوفر' : 'Available')
                              : (locale === 'ar' ? 'غير متوفر' : 'Out of Stock')
                            }
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="mt-4 flex space-x-2">
                        <div className="flex-1 bg-primary text-white text-center py-2 rounded-lg text-sm font-semibold">
                          {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                        </div>
                        <a
                          href={`https://wa.me/966599252259?text=${encodeURIComponent(`${locale === 'ar' ? 'مرحباً، أريد الاستفسار عن هذا المنتج:' : 'Hello, I want to inquire about this product:'} ${productTitle}`)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-12 h-10 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <span className="text-lg">📱</span>
                        </a>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📦</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {locale === 'ar' ? 'لا توجد منتجات' : 'No Products Found'}
              </h3>
              <p className="text-gray-600">
                {locale === 'ar' 
                  ? 'لم يتم العثور على منتجات في الوقت الحالي'
                  : 'No products found at the moment'
                }
              </p>
            </div>
          )}

          {/* Load More Button */}
          {products.length >= 12 && (
            <div className="text-center mt-8">
              <div className="inline-block bg-primary text-white px-6 py-3 rounded-lg font-semibold">
                {locale === 'ar' ? 'عرض المزيد' : 'Load More'}
              </div>
            </div>
          )}
        </div>
      </div>



      <ServerFooter locale={locale} />
    </div>
  );
};

export default ServerProductsPage;
