import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { generateProductUrl } from '../utils/generateSlug';

interface ProductCardProps {
  id: string;
  image: string;
  title: string;
  description: string;
  price?: number;
  available?: boolean;
  alt?: string;
  locale: 'ar' | 'en';
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  image,
  title,
  price = 0,
  available = true,
  alt,
  locale
}) => {
  const [showToast, setShowToast] = useState(false);

  // إنشاء رابط المنتج بالشكل الجديد
  const productUrl = generateProductUrl({ id, title, title_ar: title }, locale);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showToast) {
      timer = setTimeout(() => setShowToast(false), 3000);
    }
    return () => clearTimeout(timer);
  }, [showToast]);



  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!available) return;

    const cartItem = {
      id: id,
      title: title,
      image: image,
      price: price,
      quantity: 1
    };

    // جلب السلة الحالية من localStorage
    const existingCart = localStorage.getItem('cart');
    const cart = existingCart ? JSON.parse(existingCart) : [];

    // البحث عن المنتج في السلة
    const existingItemIndex = cart.findIndex((item: { id: string }) => item.id === id);

    if (existingItemIndex > -1) {
      // إذا كان المنتج موجود، زيادة الكمية
      cart[existingItemIndex].quantity += 1;
    } else {
      // إذا لم يكن موجود، إضافته للسلة
      cart.push(cartItem);
    }

    // حفظ السلة المحدثة
    localStorage.setItem('cart', JSON.stringify(cart));

    // إرسال حدث لتحديث عداد السلة
    window.dispatchEvent(new Event('cartUpdated'));

    setShowToast(true);
  };

  return (
    <>
      {showToast && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300">
          <div className="flex items-center gap-2">
            <i className="ri-check-line"></i>
            {locale === 'ar' ? `تم إضافة ${title} إلى السلة` : `${title} added to cart`}
          </div>
        </div>
      )}
      <div className="product-card bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
        <div className="relative">
          <Link href={`/${locale}${productUrl}`} prefetch={true} className="block">
            <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
              <Image
                src={image}
                alt={alt || title}
                width={300}
                height={200}
                className="w-full h-40 object-cover transition-transform duration-500 group-hover:scale-110"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/api/placeholder?width=300&height=200&text=لا توجد صورة';
                }}
              />

              {!available && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                  <span className="bg-red-500 text-white px-3 py-1 rounded-lg font-bold text-sm">
                    {locale === 'ar' ? 'غير متوفر' : 'Unavailable'}
                  </span>
                </div>
              )}
            </div>
          </Link>


        </div>
        <div className="p-3 bg-gradient-to-t from-white to-gray-50">
          <h3 className="text-sm font-bold text-gray-900 mb-3 group-hover:text-primary transition-colors duration-300 line-clamp-2 leading-tight text-center">{title}</h3>

          {/* Availability Badge */}
          <div className="flex justify-center mb-3">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              available
                ? 'bg-green-100 text-green-700 border border-green-200'
                : 'bg-red-100 text-red-700 border border-red-200'
            }`}>
              {available ? (locale === 'ar' ? 'متوفر' : 'Available') : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')}
            </span>
          </div>

          {/* Action Buttons - Enhanced Design */}
          <div className="flex justify-center gap-2">
            {/* WhatsApp Button */}
            <a
              href={`https://wa.me/966599252259?text=${encodeURIComponent(
                locale === 'ar'
                  ? `مرحباً، أريد الاستفسار عن هذا المنتج: ${title}`
                  : `Hello, I would like to inquire about this product: ${title}`
              )}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 py-2 px-3 rounded-lg bg-green-500 text-white flex items-center justify-center hover:bg-green-600 transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg"
              title={locale === 'ar' ? 'استفسار عبر الواتساب' : 'Inquire via WhatsApp'}
            >
              <i className="ri-whatsapp-line text-sm"></i>
            </a>

            {/* Add to Cart Button */}
            <button
              onClick={handleAddToCart}
              disabled={!available}
              className={`flex-1 py-2 px-3 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg ${
                available
                  ? 'bg-primary text-white hover:bg-primary/90'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              title={available
                ? (locale === 'ar' ? 'إضافة للسلة' : 'Add to Cart')
                : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')
              }
            >
              <i className="ri-shopping-cart-2-line text-sm"></i>
            </button>

            {/* Quick View Button */}
            <Link
              href={`/${locale}${productUrl}`}
              prefetch={true}
              className="flex-1 py-2 px-3 rounded-lg bg-blue-500 text-white flex items-center justify-center hover:bg-blue-600 transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg"
              title={locale === 'ar' ? 'عرض سريع' : 'Quick View'}
            >
              <i className="ri-eye-line text-sm"></i>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductCard;
