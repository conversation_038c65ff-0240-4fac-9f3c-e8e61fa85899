import React from 'react';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import { ProductWithDetails, Category } from '../types/mysql-database';
import ServerNavbar from './server/ServerNavbar';
import ServerFooter from './server/ServerFooter';

interface ServerProductDetailPageProps {
  locale: Locale;
  product: ProductWithDetails;
  category: Category | null;
}

const ServerProductDetailPage: React.FC<ServerProductDetailPageProps> = ({
  locale,
  product,
  category
}) => {
  // Static HTML content that will be visible even without JavaScript
  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;

  return (
    <div className="min-h-screen bg-gray-50">
      <ServerNavbar locale={locale} />
      
      {/* Static Content for SEO and No-JS users */}
      <div className="bg-white static-content">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* Product Images - Static */}
            <div className="space-y-4">
              {product.images && product.images.length > 0 ? (
                <div className="aspect-square bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <Image
                    src={product.images[0].image_url}
                    alt={`${productTitle} - ${category ? (locale === 'ar' ? category.name_ar : category.name) : ''}`}
                    width={600}
                    height={600}
                    className="w-full h-full object-contain"
                    priority
                  />
                </div>
              ) : (
                <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <div className="text-6xl mb-2">📷</div>
                    <p>{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                  </div>
                </div>
              )}
              
              {/* Thumbnail Images */}
              {product.images && product.images.length > 1 && (
                <div className="flex space-x-2 overflow-x-auto">
                  {product.images.slice(1, 5).map((image, index) => (
                    <div key={index} className="flex-shrink-0 w-20 h-20 bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <Image
                        src={image.image_url}
                        alt={`${productTitle} - ${locale === 'ar' ? 'صورة' : 'Image'} ${index + 2}`}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Product Information - Static */}
            <div className="space-y-6">
              {/* Title */}
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {productTitle}
                </h1>
                {category && (
                  <h2 className="text-lg font-semibold text-gray-700">
                    {locale === 'ar'
                      ? `${category.name_ar} - معدات فنادق احترافية عالية الجودة`
                      : `${category.name} - Professional High Quality Hotel Equipment`
                    }
                  </h2>
                )}
              </div>

              {/* Price */}
              {product.price && (
                <div className="border-b border-gray-200 pb-4">
                  <div className="flex items-center space-x-4">
                    <span className="text-4xl font-bold text-primary">
                      {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                    </span>
                    {product.original_price && product.original_price > product.price && (
                      <>
                        <span className="text-xl text-gray-400 line-through">
                          {product.original_price} {locale === 'ar' ? 'ريال' : 'SAR'}
                        </span>
                        <span className="bg-red-500 text-white text-sm font-bold px-3 py-1 rounded-full">
                          {Math.round(((product.original_price - product.price) / product.original_price) * 100)}% {locale === 'ar' ? 'خصم' : 'OFF'}
                        </span>
                      </>
                    )}
                  </div>
                  <div className="text-sm text-gray-500 mt-2">
                    {locale === 'ar' ? 'شامل ضريبة القيمة المضافة' : 'VAT included'}
                  </div>
                </div>
              )}

              {/* Description */}
              {productDescription && (
                <div className="border-b border-gray-200 pb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-primary/20">
                    {locale === 'ar' ? 'وصف المنتج' : 'Product Description'}
                  </h2>
                  <p className="text-gray-700 leading-relaxed text-lg">
                    {productDescription}
                  </p>
                </div>
              )}

              {/* Features */}
              {product.features && product.features.length > 0 && (
                <div className="border-b border-gray-200 pb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-green-500/20">
                    {locale === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
                  </h2>
                  <ul className="space-y-4">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                          <span className="text-white text-sm">✓</span>
                        </div>
                        <span className="text-gray-700 text-lg leading-relaxed">
                          {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Specifications */}
              {product.specifications && product.specifications.length > 0 && (
                <div className="border-b border-gray-200 pb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-blue-500/20">
                    {locale === 'ar' ? 'المواصفات الأساسية' : 'Key Specifications'}
                  </h2>
                  <div className="space-y-4">
                    {product.specifications.map((spec, index) => (
                      <div key={index} className="flex justify-between items-center py-3 border-b border-gray-200 last:border-b-0">
                        <span className="font-semibold text-gray-800 text-lg">
                          {locale === 'ar' ? spec.spec_key_ar : spec.spec_key}:
                        </span>
                        <span className="text-gray-600 text-lg">
                          {locale === 'ar' ? spec.spec_value_ar : spec.spec_value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Static Action Buttons */}
              <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <div className="text-center space-y-4">
                  <p className="text-gray-600">
                    {locale === 'ar' ? 'للطلب والاستفسار:' : 'For orders and inquiries:'}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <a
                      href={`https://wa.me/966599252259?text=${encodeURIComponent(`${locale === 'ar' ? 'مرحباً، أريد الاستفسار عن هذا المنتج:' : 'Hello, I want to inquire about this product:'} ${productTitle}`)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-green-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-600 transition-colors flex items-center justify-center space-x-2"
                    >
                      <span>📱</span>
                      <span>{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
                    </a>
                    <a
                      href={`tel:+966599252259`}
                      className="flex-1 bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-dark transition-colors flex items-center justify-center space-x-2"
                    >
                      <span>📞</span>
                      <span>{locale === 'ar' ? 'اتصال' : 'Call'}</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>



      <ServerFooter locale={locale} />
    </div>
  );
};

export default ServerProductDetailPage;
