'use client';

import { useSearchParams } from 'next/navigation';
import { Locale } from '../lib/i18n';
import WhatsAppProductHandler from './WhatsAppProductHandler';

interface WhatsAppProductHandlerWithParamsProps {
  locale: Locale;
}

export default function WhatsAppProductHandlerWithParams({ locale }: WhatsAppProductHandlerWithParamsProps) {
  const searchParams = useSearchParams();
  const productId = searchParams?.get('product');

  return <WhatsAppProductHandler locale={locale} productId={productId || undefined} />;
}
