'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface ModernProductCardProps {
  id: string;
  image: string;
  title: string;
  description: string;
  price?: number;
  available?: boolean;
  alt?: string;
  locale: 'ar' | 'en';
  variant?: 'default' | 'featured' | 'compact';
}

const ModernProductCard: React.FC<ModernProductCardProps> = ({
  id,
  image,
  title,
  description,
  price = 0,
  available = true,
  alt,
  locale,
  variant = 'default'
}) => {
  const [showToast, setShowToast] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // إنشاء رابط المنتج بسيط
  const productUrl = `/product/${id}`;

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showToast) {
      timer = setTimeout(() => setShowToast(false), 3000);
    }
    return () => clearTimeout(timer);
  }, [showToast]);

  const addToCart = () => {
    if (!available || typeof window === 'undefined') return;

    try {
      const cartItem = {
        id,
        title,
        image,
        price: price || 0,
        quantity: 1
      };

      const existingCart = localStorage.getItem('cart');
      const cart = existingCart ? JSON.parse(existingCart) : [];

      const existingItemIndex = cart.findIndex((item: { id: string; quantity: number }) => item.id === id);

      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += 1;
      } else {
        cart.push(cartItem);
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      setShowToast(true);

      // إرسال event للتحديث
      window.dispatchEvent(new Event('cartUpdated'));
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  const getCardClasses = () => {
    const baseClasses = "group relative bg-white rounded-2xl overflow-hidden transition-all duration-500 ease-out";
    
    switch (variant) {
      case 'featured':
        return `${baseClasses} shadow-lg hover:shadow-2xl transform hover:-translate-y-3 border border-gray-100/50 hover:border-primary/20`;
      case 'compact':
        return `${baseClasses} shadow-md hover:shadow-xl transform hover:-translate-y-2 border border-gray-100/50`;
      default:
        return `${baseClasses} shadow-lg hover:shadow-2xl transform hover:-translate-y-2 border border-gray-100/50 hover:border-primary/20`;
    }
  };

  const getImageHeight = () => {
    switch (variant) {
      case 'featured':
        return 'h-64';
      case 'compact':
        return 'h-48';
      default:
        return 'h-56';
    }
  };

  return (
    <>
      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-xl shadow-lg animate-slide-in-right">
          <div className="flex items-center gap-2">
            <i className="ri-check-line text-lg"></i>
            <span className="font-medium">
              {locale === 'ar' ? 'تم إضافة المنتج للسلة' : 'Product added to cart'}
            </span>
          </div>
        </div>
      )}

      <div 
        className={getCardClasses()}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image Container */}
        <div className="relative overflow-hidden">
          <Link href={`/${locale}${productUrl}`} prefetch={true} className="block">
            <div className={`relative ${getImageHeight()} bg-gradient-to-br from-gray-50 to-gray-100`}>
              {/* Loading Skeleton */}
              {!imageLoaded && (
                <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse">
                  <div className="flex items-center justify-center h-full">
                    <i className="ri-image-line text-gray-400 text-3xl"></i>
                  </div>
                </div>
              )}
              
              <Image
                src={image || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
                alt={alt || title || 'Product image'}
                fill
                className={`object-cover transition-all duration-700 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                } ${isHovered ? 'scale-110' : 'scale-100'}`}
                onLoad={() => setImageLoaded(true)}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  if (target.src !== '/api/placeholder?width=400&height=300&text=لا توجد صورة') {
                    target.src = '/api/placeholder?width=400&height=300&text=لا توجد صورة';
                  }
                  setImageLoaded(true);
                }}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority={variant === 'featured'}
              />

              {/* Gradient Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent transition-opacity duration-300 ${
                isHovered ? 'opacity-100' : 'opacity-0'
              }`} />

              {/* Status Badge */}
              <div className="absolute top-3 left-3">
                <span className={`px-3 py-1 rounded-full text-xs font-bold backdrop-blur-sm border transition-all duration-300 ${
                  available
                    ? 'bg-green-500/90 text-white border-green-400/50 shadow-lg'
                    : 'bg-red-500/90 text-white border-red-400/50 shadow-lg'
                }`}>
                  {available 
                    ? (locale === 'ar' ? 'متوفر' : 'Available')
                    : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')
                  }
                </span>
              </div>

              {/* Quick Actions Overlay */}
              <div className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
                isHovered ? 'opacity-100' : 'opacity-0'
              }`}>
                <div className="flex gap-2">
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      addToCart();
                    }}
                    disabled={!available}
                    className={`p-3 rounded-full backdrop-blur-md transition-all duration-300 transform hover:scale-110 ${
                      available
                        ? 'bg-primary/90 text-white hover:bg-primary shadow-lg border border-primary/30'
                        : 'bg-gray-500/50 text-gray-300 cursor-not-allowed'
                    }`}
                    title={locale === 'ar' ? 'إضافة للسلة' : 'Add to Cart'}
                  >
                    <i className="ri-shopping-cart-line text-lg"></i>
                  </button>
                  
                  <Link
                    href={`/${locale}${productUrl}`}
                    prefetch={true}
                    className="p-3 rounded-full bg-white/90 text-gray-800 hover:bg-white transition-all duration-300 transform hover:scale-110 shadow-lg border border-white/30"
                    title={locale === 'ar' ? 'عرض سريع' : 'Quick View'}
                  >
                    <i className="ri-eye-line text-lg"></i>
                  </Link>
                </div>
              </div>
            </div>
          </Link>
        </div>

        {/* Content */}
        <div className="p-5">
          <Link href={`/${locale}${productUrl}`} prefetch={true} className="block">
            <h3 className={`font-bold text-gray-900 mb-2 line-clamp-2 transition-colors duration-300 group-hover:text-primary ${
              variant === 'featured' ? 'text-lg' : variant === 'compact' ? 'text-sm' : 'text-base'
            }`}>
              {title}
            </h3>
            
            <p className={`text-gray-600 mb-4 line-clamp-2 leading-relaxed ${
              variant === 'compact' ? 'text-xs' : 'text-sm'
            }`}>
              {description}
            </p>
          </Link>

          {/* Price & Action */}
          <div className="flex items-center justify-between">
            {price && price > 0 && (
              <div className="flex flex-col">
                <span className={`font-bold text-primary ${
                  variant === 'featured' ? 'text-xl' : variant === 'compact' ? 'text-base' : 'text-lg'
                }`}>
                  {Number(price).toFixed(2)} {locale === 'ar' ? 'ر.س' : 'SAR'}
                </span>
                <span className="text-xs text-gray-500">
                  {locale === 'ar' ? 'شامل الضريبة' : 'Tax included'}
                </span>
              </div>
            )}
            
            <button
              onClick={addToCart}
              disabled={!available}
              className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 ${
                available
                  ? 'bg-primary text-white hover:bg-primary/90 shadow-md hover:shadow-lg'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              } ${variant === 'compact' ? 'text-xs px-3 py-1.5' : 'text-sm'}`}
            >
              <i className="ri-add-line mr-1"></i>
              {locale === 'ar' ? 'إضافة' : 'Add'}
            </button>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-secondary/10 to-transparent rounded-tr-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      </div>
    </>
  );
};

export default ModernProductCard;
