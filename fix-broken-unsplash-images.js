const mysql = require('mysql2/promise');

async function fixBrokenUnsplashImages() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: '',
      database: 'droobhajer_db'
    });
    
    console.log('🔧 إصلاح روابط Unsplash المعطلة...');
    
    // روابط Unsplash موثوقة ومختبرة
    const workingImages = {
      'Kitchen Equipment': 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&auto=format',
      'Bakeryware': 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&auto=format',
      'Cutlery': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format',
      'Glassware': 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop&auto=format',
      'Chinaware': 'https://images.unsplash.com/photo-1578749556568-bc2c40e68b61?w=400&h=300&fit=crop&auto=format',
      'Cleaning Supplies': 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop&auto=format',
      'Banquet Supplies': 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop&auto=format',
      'Buffetware': 'https://images.unsplash.com/photo-1555244162-803834f70033?w=400&h=300&fit=crop&auto=format',
      'Table Accessories': 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&auto=format',
      'Table Linen': 'https://images.unsplash.com/photo-1519225421980-715cb0215aed?w=400&h=300&fit=crop&auto=format',
      'Room Service Equipment': 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop&auto=format',
      'Housekeeping': 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&auto=format',
      'Maintenance Tools': 'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=400&h=300&fit=crop&auto=format'
    };
    
    // جلب جميع الفئات الحالية
    const [categories] = await connection.execute(`
      SELECT id, name, name_ar, image_url 
      FROM categories 
      WHERE deleted_at IS NULL
    `);
    
    console.log('🔄 تحديث الروابط المعطلة...');
    
    for (const category of categories) {
      const workingImage = workingImages[category.name];
      
      if (workingImage && category.image_url !== workingImage) {
        try {
          await connection.execute(`
            UPDATE categories 
            SET image_url = ? 
            WHERE id = ?
          `, [workingImage, category.id]);
          
          console.log(`✅ تم تحديث صورة: ${category.name_ar} (${category.name})`);
        } catch (error) {
          console.error(`❌ خطأ في تحديث ${category.name}:`, error.message);
        }
      }
    }
    
    // إضافة روابط احتياطية للفئات التي لا تحتوي على صور مناسبة
    const fallbackImages = [
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=300&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1578749556568-bc2c40e68b61?w=400&h=300&fit=crop&auto=format',
      'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop&auto=format'
    ];
    
    // تحديث أي فئات لا تحتوي على روابط محددة
    let fallbackIndex = 0;
    for (const category of categories) {
      if (!workingImages[category.name]) {
        const fallbackImage = fallbackImages[fallbackIndex % fallbackImages.length];
        
        try {
          await connection.execute(`
            UPDATE categories 
            SET image_url = ? 
            WHERE id = ?
          `, [fallbackImage, category.id]);
          
          console.log(`✅ تم تحديث صورة احتياطية: ${category.name_ar} (${category.name})`);
          fallbackIndex++;
        } catch (error) {
          console.error(`❌ خطأ في تحديث ${category.name}:`, error.message);
        }
      }
    }
    
    console.log('\n✅ تم إصلاح جميع روابط الصور');
    
    // عرض النتائج النهائية
    const [updatedCategories] = await connection.execute(`
      SELECT id, name, name_ar, image_url 
      FROM categories 
      WHERE deleted_at IS NULL 
      ORDER BY name_ar
    `);
    
    console.log('\n📋 الفئات بعد الإصلاح:');
    updatedCategories.forEach(cat => {
      console.log(`- ${cat.name_ar} (${cat.name})`);
      console.log(`  الصورة: ${cat.image_url}`);
      console.log('');
    });
    
    await connection.end();
    console.log('\n🎉 تم إصلاح جميع مشاكل الصور!');
    console.log('💡 أعد تحميل الصفحة لرؤية التحسينات');
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  }
}

fixBrokenUnsplashImages();
