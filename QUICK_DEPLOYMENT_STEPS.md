# 🚀 خطوات النشر السريع على Hostinger

## 📋 قائمة المراجعة السريعة

### ✅ قبل النشر:
- [ ] تحديث `.env.production` بالقيم الصحيحة
- [ ] تحديث `ecosystem.config.js` بمسار السيرفر
- [ ] تحديث `deploy.sh` بمعلومات السيرفر
- [ ] إنشاء قاعدة البيانات MySQL في Hostinger
- [ ] إنشاء حسابات الإيميل المطلوبة

### 🔧 الإعدادات المطلوبة:

#### 1. معلومات السيرفر:
```bash
SERVER_IP="your-server-ip-address"
DOMAIN="your-domain.com"
USERNAME="your-hostinger-username"
```

#### 2. قاعدة البيانات:
```bash
DB_HOST="localhost"
DB_USER="your-db-username"
DB_PASSWORD="your-db-password"
DB_NAME="your-database-name"
```

#### 3. الإيميل:
```bash
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-email-password"
ADMIN_EMAIL="<EMAIL>"
```

## 🚀 خطوات النشر:

### الطريقة 1: النشر التلقائي
```bash
# 1. تحديث الإعدادات
cp .env.production.example .env.production
# قم بتحديث القيم في .env.production

# 2. تشغيل سكريبت النشر
chmod +x deploy.sh
./deploy.sh
```

### الطريقة 2: النشر اليدوي

#### الخطوة 1: إعداد قاعدة البيانات
```sql
-- في phpMyAdmin أو MySQL Command Line
-- قم بتشغيل: database-setup-production.sql
```

#### الخطوة 2: بناء المشروع
```bash
npm install
npm run build:production
```

#### الخطوة 3: رفع الملفات
```bash
# ضغط الملفات
tar -czf project.tar.gz .next public package.json package-lock.json next.config.js .env.production ecosystem.config.js .htaccess

# رفع عبر FTP أو File Manager
# ارفع إلى: /public_html/
```

#### الخطوة 4: تثبيت على السيرفر
```bash
# عبر SSH
ssh username@your-server-ip

cd /home/<USER>/public_html/
tar -xzf project.tar.gz
npm install --production
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 🧪 اختبار النشر:

### 1. اختبار قاعدة البيانات:
```
https://your-domain.com/api/test-db-connection
```

### 2. اختبار الإيميل:
```
https://your-domain.com/api/admin/test-email
```

### 3. اختبار تسجيل الدخول:
```
https://your-domain.com/admin/login
Username: admin
Password: [كلمة المرور من .env.production]
```

### 4. اختبار الموقع:
```
https://your-domain.com
```

## 🔧 إعدادات Hostinger المطلوبة:

### 1. Node.js:
- تفعيل Node.js من لوحة التحكم
- اختيار إصدار 18+ أو 20+
- تحديد مجلد التطبيق: `/public_html/`

### 2. قاعدة البيانات:
- إنشاء قاعدة بيانات MySQL
- إنشاء مستخدم قاعدة بيانات
- تشغيل ملف `database-setup-production.sql`

### 3. الإيميل:
- إنشاء حسابات إيميل من لوحة التحكم
- تفعيل SMTP
- استخدام الإعدادات:
  - SMTP Host: `smtp.hostinger.com`
  - Port: `465` (SSL)

### 4. SSL:
- تفعيل شهادة SSL المجانية
- إعادة توجيه HTTP إلى HTTPS

## 🚨 استكشاف الأخطاء:

### مشكلة: التطبيق لا يعمل
```bash
# تحقق من حالة PM2
pm2 status
pm2 logs droobhajer

# إعادة تشغيل
pm2 restart droobhajer
```

### مشكلة: خطأ في قاعدة البيانات
```bash
# تحقق من الاتصال
mysql -u username -p -h localhost database_name

# تحقق من الجداول
SHOW TABLES;
```

### مشكلة: خطأ في الإيميل
```bash
# اختبار SMTP
telnet smtp.hostinger.com 465
```

## 📞 الدعم:

### ملفات السجلات:
- PM2 Logs: `pm2 logs`
- Application Logs: `/home/<USER>/public_html/logs/`
- Nginx Logs: `/var/log/nginx/`

### أوامر مفيدة:
```bash
# مراقبة التطبيق
pm2 monit

# إعادة تشغيل
pm2 restart droobhajer

# إيقاف
pm2 stop droobhajer

# حذف من PM2
pm2 delete droobhajer

# تحديث التطبيق
pm2 reload droobhajer
```

## 🎉 بعد النشر الناجح:

1. ✅ تغيير كلمة مرور المدير الافتراضية
2. ✅ إعداد النسخ الاحتياطية التلقائية
3. ✅ مراقبة الأداء والأخطاء
4. ✅ تحديث معلومات الاتصال
5. ✅ اختبار جميع الوظائف

---

**💡 نصيحة:** احتفظ بنسخة احتياطية من قاعدة البيانات قبل أي تحديث!
