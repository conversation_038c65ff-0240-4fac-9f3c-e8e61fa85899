# دليل استكشاف الأخطاء - نظام طلبات التسعير

## 🔧 المشاكل الشائعة وحلولها

### 1. خطأ "cannot save file" عند إنشاء ملف Excel

#### الأعراض:
```
Error: cannot save file D:\path\to\excel\file.xlsx
```

#### الأسباب المحتملة:
- المجلد غير موجود
- عدم وجود صلاحيات كتابة
- مشكلة في مكتبة XLSX
- مساحة القرص ممتلئة

#### الحلول:

**1. تشغيل سكريبت إعداد المجلدات:**
```bash
npm run setup-dirs
```

**2. التحقق من صلاحيات المجلد يدوياً:**
```bash
# Windows
icacls "public\uploads\excel" /grant Users:F

# Linux/Mac
chmod 755 public/uploads/excel
```

**3. إنشاء المجلدات يدوياً:**
```bash
mkdir -p public/uploads/excel
mkdir -p public/uploads/images
mkdir -p temp-uploads
mkdir -p secure-uploads
```

### 2. خطأ في إرسال الإيميل

#### الأعراض:
```
❌ خطأ في إرسال الإيميل عبر Hostinger SMTP
Error: Invalid login
```

#### الحلول:

**1. اختبار إعدادات الإيميل:**
```bash
npm run test-email
```

**2. التحقق من متغيرات البيئة:**
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-correct-password
ADMIN_EMAIL=<EMAIL>
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=465
```

**3. التحقق من إعدادات Hostinger:**
- تأكد من أن الإيميل مُفعَّل في لوحة تحكم Hostinger
- تأكد من صحة كلمة المرور
- جرب المنفذ 587 بدلاً من 465

### 3. خطأ في قاعدة البيانات

#### الأعراض:
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```

#### الحلول:

**1. التحقق من تشغيل MySQL:**
```bash
# Windows (XAMPP)
# تأكد من تشغيل Apache و MySQL من لوحة تحكم XAMPP

# Linux
sudo systemctl start mysql

# Mac
brew services start mysql
```

**2. التحقق من إعدادات قاعدة البيانات:**
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=droobhajer_db
```

**3. إنشاء قاعدة البيانات:**
```sql
CREATE DATABASE droobhajer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. مشاكل رفع الملفات

#### الأعراض:
- الصور لا تظهر
- ملفات Excel لا تُحفظ

#### الحلول:

**1. التحقق من حجم الملف:**
```javascript
// في next.config.js
module.exports = {
  experimental: {
    serverComponentsExternalPackages: ['xlsx']
  },
  api: {
    bodyParser: {
      sizeLimit: '10mb'
    }
  }
}
```

**2. التحقق من صلاحيات المجلدات:**
```bash
npm run setup-dirs
```

### 5. مشاكل الترميز (العربية)

#### الأعراض:
- النصوص العربية تظهر كرموز غريبة
- مشاكل في ملف Excel

#### الحلول:

**1. التأكد من ترميز UTF-8:**
```javascript
// عند كتابة ملف CSV
fs.writeFileSync(csvFilePath, fullCsvData, 'utf8');
```

**2. إعدادات قاعدة البيانات:**
```sql
ALTER DATABASE droobhajer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 🔍 أدوات التشخيص

### 1. فحص النظام الكامل
```bash
# تشغيل جميع الفحوصات
npm run setup-dirs
npm run test-email
npm run dev
```

### 2. فحص المجلدات
```bash
ls -la public/uploads/
ls -la public/uploads/excel/
```

### 3. فحص قاعدة البيانات
```sql
SHOW DATABASES;
USE droobhajer_db;
SHOW TABLES;
SELECT COUNT(*) FROM quote_requests;
```

### 4. فحص السجلات
```bash
# في terminal أثناء تشغيل النظام
# ابحث عن هذه الرسائل:
# ✅ تم إنشاء ملف Excel بنجاح
# ✅ تم إرسال الإيميل بنجاح
# ❌ خطأ في...
```

## 📞 الحصول على المساعدة

### معلومات مفيدة عند طلب المساعدة:

1. **نظام التشغيل:** Windows/Linux/Mac
2. **إصدار Node.js:** `node --version`
3. **رسالة الخطأ الكاملة**
4. **خطوات إعادة إنتاج المشكلة**
5. **محتوى ملف .env.local (بدون كلمات المرور)**

### سجلات مفيدة:
```bash
# تشغيل النظام مع سجلات مفصلة
npm run dev

# في نافذة أخرى، اختبار الإيميل
npm run test-email

# فحص المجلدات
npm run setup-dirs
```

## ✅ قائمة التحقق السريع

- [ ] تم تشغيل `npm run setup-dirs`
- [ ] تم تشغيل `npm run test-email` بنجاح
- [ ] قاعدة البيانات MySQL تعمل
- [ ] متغيرات البيئة محدثة بشكل صحيح
- [ ] المجلدات موجودة وقابلة للكتابة
- [ ] إعدادات Hostinger صحيحة
- [ ] النظام يعمل على `npm run dev`

---

**آخر تحديث:** يونيو 2025  
**الإصدار:** 1.0
