import { Category, Subcategory, AdminProduct, SiteSettings } from '../types/admin';

// دالة عامة للتعامل مع API
const apiCall = async (url: string, options: RequestInit = {}) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.message || 'API call failed');
  }

  return data.data;
};

// دوال الفئات الرئيسية
export const categoriesAPI = {
  getAll: (): Promise<Category[]> => apiCall('/api/admin/categories'),
  
  create: (category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<Category> =>
    apiCall('/api/admin/categories', {
      method: 'POST',
      body: JSON.stringify(category),
    }),
  
  update: (id: string, updates: Partial<Category>): Promise<Category> =>
    apiCall(`/api/admin/categories?id=${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    }),
  
  delete: (id: string): Promise<void> =>
    apiCall(`/api/admin/categories?id=${id}`, {
      method: 'DELETE',
    }),
};

// دوال الفئات الفرعية
export const subcategoriesAPI = {
  getAll: (): Promise<Subcategory[]> => apiCall('/api/admin/subcategories'),
  
  getByCategory: (categoryId: string): Promise<Subcategory[]> =>
    apiCall(`/api/admin/subcategories?categoryId=${categoryId}`),
  
  create: (subcategory: Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<Subcategory> =>
    apiCall('/api/admin/subcategories', {
      method: 'POST',
      body: JSON.stringify(subcategory),
    }),
  
  update: (id: string, updates: Partial<Subcategory>): Promise<Subcategory> =>
    apiCall(`/api/admin/subcategories?id=${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    }),
  
  delete: (id: string): Promise<void> =>
    apiCall(`/api/admin/subcategories?id=${id}`, {
      method: 'DELETE',
    }),
};

// دوال المنتجات
export const productsAPI = {
  getAll: (): Promise<AdminProduct[]> => apiCall('/api/admin/products'),
  
  getByCategory: (categoryId: string): Promise<AdminProduct[]> =>
    apiCall(`/api/admin/products?categoryId=${categoryId}`),
  
  getBySubcategory: (subcategoryId: string): Promise<AdminProduct[]> =>
    apiCall(`/api/admin/products?subcategoryId=${subcategoryId}`),
  
  create: (product: Omit<AdminProduct, 'id' | 'createdAt' | 'updatedAt'>): Promise<AdminProduct> =>
    apiCall('/api/admin/products', {
      method: 'POST',
      body: JSON.stringify(product),
    }),
  
  update: (id: string, updates: Partial<AdminProduct>): Promise<AdminProduct> =>
    apiCall(`/api/admin/products?id=${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    }),
  
  delete: (id: string): Promise<void> =>
    apiCall(`/api/admin/products?id=${id}`, {
      method: 'DELETE',
    }),
};

// دوال الإعدادات
export const settingsAPI = {
  get: (): Promise<SiteSettings> => apiCall('/api/admin/settings'),
  
  update: (settings: Partial<SiteSettings>): Promise<SiteSettings> =>
    apiCall('/api/admin/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    }),
};

// دوال الإحصائيات
export const statsAPI = {
  get: (): Promise<{
    totalProducts: number;
    totalCategories: number;
    totalSubcategories: number;
    activeProducts: number;
    inactiveProducts: number;
    featuredProducts: number;
    availableProducts: number;
    unavailableProducts: number;
  }> => apiCall('/api/admin/stats'),
};

// دالة لمعالجة الأخطاء
export const handleAPIError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return 'حدث خطأ غير متوقع';
};

// دالة لعرض رسائل النجاح
export const showSuccessMessage = (message: string) => {
  // يمكن استبدالها بنظام إشعارات أكثر تطوراً
  console.log('Success:', message);
};

// دالة لعرض رسائل الخطأ
export const showErrorMessage = (message: string) => {
  // يمكن استبدالها بنظام إشعارات أكثر تطوراً
  console.error('Error:', message);
};
