/**
 * دالة لتحويل نص إلى slug صديق للسيو
 * @param text النص المراد تحويله
 * @returns slug منسق للاستخدام في الروابط
 */
export function generateSlug(text: string): string {
  if (!text) return '';
  
  return text
    // تحويل إلى أحرف صغيرة
    .toLowerCase()
    // إزالة الرموز الخاصة والاحتفاظ بالأحرف والأرقام والفراغات والشرطات
    .replace(/[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF-]/g, '')
    // استبدال الفراغات المتعددة بفراغ واحد
    .replace(/\s+/g, ' ')
    // إزالة الفراغات من البداية والنهاية
    .trim()
    // استبدال الفراغات بشرطات
    .replace(/\s/g, '-')
    // إزالة الشرطات المتعددة
    .replace(/-+/g, '-')
    // إزالة الشرطات من البداية والنهاية
    .replace(/^-+|-+$/g, '');
}

/**
 * دالة لإنشاء رابط منتج بالشكل الجديد
 * @param product المنتج
 * @param locale اللغة
 * @returns رابط المنتج بالشكل /products/[slug]-[id]
 */
export function generateProductUrl(product: { id: string; title: string; title_ar: string }, locale: 'ar' | 'en'): string {
  const title = locale === 'ar' ? product.title_ar : product.title;
  const slug = generateSlug(title);
  return `/products/${slug}-${product.id}`;
}

/**
 * دالة لاستخراج ID من رابط المنتج
 * @param slugWithId النص الذي يحتوي على slug-id
 * @returns معرف المنتج أو null إذا لم يتم العثور عليه
 */
export function extractProductIdFromSlug(slugWithId: string): string | null {
  if (!slugWithId) return null;
  
  // البحث عن آخر شرطة في النص
  const lastDashIndex = slugWithId.lastIndexOf('-');
  
  if (lastDashIndex === -1) {
    // إذا لم توجد شرطة، قد يكون المعرف فقط
    return slugWithId;
  }
  
  // استخراج الجزء بعد آخر شرطة
  const potentialId = slugWithId.substring(lastDashIndex + 1);
  
  // التحقق من أن المعرف ليس فارغاً
  if (potentialId.trim() === '') {
    return null;
  }
  
  return potentialId;
}

/**
 * دالة للتحقق من صحة تنسيق رابط المنتج
 * @param slugWithId النص المراد التحقق منه
 * @returns true إذا كان التنسيق صحيحاً
 */
export function isValidProductSlug(slugWithId: string): boolean {
  if (!slugWithId) return false;
  
  const productId = extractProductIdFromSlug(slugWithId);
  return productId !== null && productId.length > 0;
}
