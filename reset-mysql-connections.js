// إعادة تعيين اتصالات MySQL
const mysql = require('mysql2/promise');

async function resetConnections() {
  let connection = null;
  try {
    console.log('🔄 محاولة إعادة تعيين اتصالات MySQL...');
    
    // محاولة الاتصال كـ root لإدارة الاتصالات
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || ''
    });

    console.log('✅ تم الاتصال بـ MySQL بنجاح');

    // عرض الاتصالات الحالية
    const [processes] = await connection.execute('SHOW PROCESSLIST');
    console.log(`📊 عدد الاتصالات الحالية: ${processes.length}`);

    // قتل الاتصالات الخاملة (إذا كان لديك صلاحيات)
    let killedConnections = 0;
    for (const process of processes) {
      // قتل الاتصالات الخاملة التي تستغرق أكثر من 60 ثانية
      if (process.Command === 'Sleep' && process.Time > 60 && process.Id !== connection.threadId) {
        try {
          await connection.execute(`KILL ${process.Id}`);
          killedConnections++;
        } catch (killError) {
          // تجاهل أخطاء القتل
        }
      }
    }

    if (killedConnections > 0) {
      console.log(`🗑️ تم إنهاء ${killedConnections} اتصال خامل`);
    }

    // عرض الاتصالات بعد التنظيف
    const [newProcesses] = await connection.execute('SHOW PROCESSLIST');
    console.log(`📊 عدد الاتصالات بعد التنظيف: ${newProcesses.length}`);

    // عرض متغيرات الاتصال
    const [variables] = await connection.execute("SHOW VARIABLES LIKE 'max_connections'");
    if (variables.length > 0) {
      console.log(`⚙️ الحد الأقصى للاتصالات: ${variables[0].Value}`);
    }

    console.log('✅ تم إعادة تعيين الاتصالات بنجاح');

  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين الاتصالات:', error.message);
    
    if (error.message.includes('Too many connections')) {
      console.log('\n🔧 حلول مقترحة:');
      console.log('1. إعادة تشغيل خادم MySQL:');
      console.log('   - في XAMPP: إيقاف وتشغيل MySQL');
      console.log('   - في الخادم: sudo systemctl restart mysql');
      console.log('2. زيادة max_connections في إعدادات MySQL');
      console.log('3. التأكد من إغلاق الاتصالات في التطبيق');
    }
  } finally {
    if (connection) {
      try {
        await connection.end();
        console.log('✅ تم إغلاق اتصال الإدارة');
      } catch (closeError) {
        console.log('⚠️ تحذير: مشكلة في إغلاق اتصال الإدارة');
      }
    }
  }
}

// دالة لاختبار الاتصال البسيط
async function testSimpleConnection() {
  let connection = null;
  try {
    console.log('\n🧪 اختبار اتصال بسيط...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'droobhajer_db'
    });

    await connection.ping();
    console.log('✅ الاتصال البسيط يعمل بنجاح');

    // اختبار استعلام بسيط
    const [result] = await connection.execute('SELECT 1 as test');
    console.log('✅ الاستعلامات تعمل بنجاح');

    return true;
  } catch (error) {
    console.error('❌ فشل الاتصال البسيط:', error.message);
    return false;
  } finally {
    if (connection) {
      try {
        await connection.end();
      } catch (closeError) {
        // تجاهل أخطاء الإغلاق
      }
    }
  }
}

async function main() {
  console.log('🚀 بدء إعادة تعيين اتصالات MySQL\n');
  
  // محاولة إعادة تعيين الاتصالات
  await resetConnections();
  
  // اختبار الاتصال
  const connectionWorks = await testSimpleConnection();
  
  if (connectionWorks) {
    console.log('\n🎉 MySQL جاهز للاستخدام!');
    console.log('يمكنك الآن تشغيل:');
    console.log('- node test-db-connection.js');
    console.log('- npm run dev');
  } else {
    console.log('\n❌ ما زالت هناك مشاكل في الاتصال');
    console.log('يرجى إعادة تشغيل خادم MySQL يدوياً');
  }
}

main();
