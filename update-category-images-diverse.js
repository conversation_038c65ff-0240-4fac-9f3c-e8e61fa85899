const mysql = require('mysql2/promise');

async function updateCategoryImagesWithDiverseImages() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: '',
      database: 'droobhajer_db'
    });
    
    console.log('🎨 تحديث صور الفئات بصور متنوعة ومناسبة...');
    
    // تحديث كل فئة بصورة مناسبة لها
    const categoryUpdates = [
      {
        name: 'Kitchen Equipment',
        nameAr: 'معدات المطبخ',
        image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
      },
      {
        name: 'Bakeryware',
        nameAr: 'أدوات المخبز',
        image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop'
      },
      {
        name: 'Cutlery',
        nameAr: 'أدوات المائدة',
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop'
      },
      {
        name: 'Glassware',
        nameAr: 'كاسات زجاجية',
        image: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=300&fit=crop'
      },
      {
        name: 'Chinaware',
        nameAr: 'أدوات البورسلين',
        image: 'https://images.unsplash.com/photo-1578749556568-bc2c40e68b61?w=400&h=300&fit=crop'
      },
      {
        name: 'Cleaning Supplies',
        nameAr: 'أدوات التنظيف',
        image: 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop'
      },
      {
        name: 'Banquet Supplies',
        nameAr: 'تجهيزات الولائم',
        image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop'
      },
      {
        name: 'Buffetware',
        nameAr: 'مستلزمات البوفيه',
        image: 'https://images.unsplash.com/photo-1555244162-803834f70033?w=400&h=300&fit=crop'
      },
      {
        name: 'Table Accessories',
        nameAr: 'إكسسوارات الطاولة',
        image: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop'
      },
      {
        name: 'Table Linen',
        nameAr: 'مفارش الطاولة',
        image: 'https://images.unsplash.com/photo-1519225421980-715cb0215aed?w=400&h=300&fit=crop'
      },
      {
        name: 'Room Service Equipment',
        nameAr: 'خدمات الغرف',
        image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop'
      },
      {
        name: 'Housekeeping',
        nameAr: 'التدبير الفندقي',
        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop'
      },
      {
        name: 'Maintenance Tools',
        nameAr: 'الصيانة',
        image: 'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=400&h=300&fit=crop'
      }
    ];
    
    console.log('🔄 تحديث صور الفئات...');
    
    for (const category of categoryUpdates) {
      try {
        // تحديث بناءً على الاسم الإنجليزي أو العربي
        const result = await connection.execute(`
          UPDATE categories 
          SET image_url = ? 
          WHERE (name = ? OR name_ar = ?) AND deleted_at IS NULL
        `, [category.image, category.name, category.nameAr]);
        
        if (result[0].affectedRows > 0) {
          console.log(`✅ تم تحديث صورة فئة: ${category.nameAr} (${category.name})`);
        } else {
          console.log(`⚠️  لم يتم العثور على فئة: ${category.nameAr} (${category.name})`);
        }
      } catch (error) {
        console.error(`❌ خطأ في تحديث فئة ${category.nameAr}:`, error.message);
      }
    }
    
    console.log('\n✅ تم الانتهاء من تحديث جميع صور الفئات');
    
    // عرض النتائج النهائية
    const [updatedCategories] = await connection.execute(`
      SELECT id, name, name_ar, image_url 
      FROM categories 
      WHERE deleted_at IS NULL 
      ORDER BY name_ar
    `);
    
    console.log('\n📋 الفئات بعد التحديث:');
    updatedCategories.forEach(cat => {
      console.log(`- ${cat.name_ar} (${cat.name})`);
      console.log(`  الصورة: ${cat.image_url}`);
      console.log('');
    });
    
    await connection.end();
    console.log('\n🎉 تم إصلاح جميع مشاكل الصور بنجاح!');
    console.log('💡 يمكنك الآن إعادة تشغيل الخادم لرؤية التحسينات');
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  }
}

updateCategoryImagesWithDiverseImages();
