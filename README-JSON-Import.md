# دليل استخدام ميزة إضافة المنتجات عبر JSON

## 📁 الملفات المرجعية

### 1. `categories-reference.txt`
ملف نصي شامل يحتوي على:
- قائمة بجميع الفئات الرئيسية والفرعية
- معرفات (IDs) الفئات بالأسماء العربية والإنجليزية
- هيكل JSON المطلوب لإضافة المنتجات
- أمثلة عملية لمنتجات مختلفة
- ملاحظات ونصائح مهمة

### 2. `categories-data.json`
ملف JSON منظم يحتوي على:
- بيانات الفئات والفئات الفرعية بتنسيق JSON
- قالب (template) لإضافة المنتجات
- أمثلة جاهزة للاستخدام

### 3. `example-product.json`
مثال كامل لمنتج جاهز للاستخدام المباشر

## 🚀 كيفية الاستخدام

### الخطوة 1: الوصول إلى صفحة إدارة المنتجات
1. اذهب إلى `/admin/products`
2. ستجد زرين في أعلى الصفحة:
   - **"إضافة منتج جديد"** (الأزرق) - للإضافة التقليدية
   - **"إضافة JSON"** (الأخضر) - للإضافة عبر JSON

### الخطوة 2: استخدام ميزة JSON
1. اضغط على زر **"إضافة JSON"** الأخضر
2. ستفتح نافذة تحتوي على:
   - تعليمات الاستخدام
   - مثال على التنسيق الصحيح
   - منطقة إدخال JSON

### الخطوة 3: إعداد بيانات JSON
1. افتح ملف `categories-reference.txt` للحصول على معرفات الفئات
2. استخدم القالب الموجود في الملف أو انسخ من `example-product.json`
3. تأكد من استخدام `categoryId` و `subcategoryId` صحيحين

### الخطوة 4: إضافة المنتج
1. الصق بيانات JSON في المنطقة المخصصة
2. اضغط **"إضافة المنتج"**
3. سيتم التحقق من البيانات وإضافة المنتج

## ✅ الحقول المطلوبة

```json
{
  "title": "مطلوب - اسم المنتج بالإنجليزية",
  "titleAr": "مطلوب - اسم المنتج بالعربية", 
  "description": "مطلوب - وصف المنتج بالإنجليزية",
  "descriptionAr": "مطلوب - وصف المنتج بالعربية",
  "price": "مطلوب - السعر (رقم)",
  "categoryId": "مطلوب - معرف الفئة الرئيسية",
  "subcategoryId": "مطلوب - معرف الفئة الفرعية"
}
```

## 🔧 الحقول الاختيارية

```json
{
  "id": "معرف مخصص للمنتج (سيتم إنشاؤه تلقائياً إذا لم يتم توفيره)",
  "originalPrice": "السعر الأصلي قبل التخفيض",
  "available": "متوفر للبيع (true/false)",
  "features": ["قائمة المميزات بالإنجليزية"],
  "featuresAr": ["قائمة المميزات بالعربية"],
  "specifications": "قائمة المواصفات التقنية",
  "isActive": "المنتج نشط (true/false)",
  "isFeatured": "منتج مميز (true/false)"
}
```

## ⚠️ ملاحظات مهمة

1. **الصور**: سيتم تجاهل حقل `images` في هذا الإصدار ويمكن إضافة الصور لاحقاً عبر تعديل المنتج
2. **معرف المنتج**: يجب أن يكون فريداً ولا يحتوي على مسافات
3. **الفئات**: تأكد من استخدام معرفات صحيحة من ملف `categories-reference.txt`
4. **الأسعار**: استخدم أسعار بالريال السعودي

## 🎯 أمثلة سريعة

### منتج بسيط:
```json
{
  "title": "Stainless Steel Plate",
  "titleAr": "صحن ستانلس ستيل",
  "description": "High quality stainless steel plate",
  "descriptionAr": "صحن ستانلس ستيل عالي الجودة",
  "price": 45.00,
  "categoryId": "Food-Beverage",
  "subcategoryId": "Chinaware"
}
```

### منتج متكامل:
انظر ملف `example-product.json` للحصول على مثال كامل

## 🆘 استكشاف الأخطاء

### خطأ "الحقول المطلوبة مفقودة"
تأكد من وجود جميع الحقول المطلوبة المذكورة أعلاه

### خطأ "معرف المنتج غير صحيح"
استخدم أحرف وأرقام وشرطات فقط (بدون مسافات)

### خطأ "فئة غير موجودة"
تحقق من معرفات الفئات في ملف `categories-reference.txt`

### خطأ "تنسيق JSON غير صحيح"
تأكد من صحة تنسيق JSON باستخدام أدوات التحقق عبر الإنترنت

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، تواصل مع فريق التطوير.
