import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { getTranslation } from '../../../lib/translations';
import ResponsiveCategoriesPage from '../../../components/ResponsiveCategoriesPage';

// أنواع البيانات
interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  product_count: number;
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  subcategories: Subcategory[];
}

interface PageProps {
  params: Promise<{
    locale: Locale;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = (key: string) => getTranslation(locale, key as keyof typeof import('../../../lib/translations').translations.ar);

  return {
    title: `${t('categories')} - DROOB HAJER`,
    description: t('categories_description') || 'تصفح جميع فئات المنتجات في متجر دروب هاجر',
  };
}

export default async function Categories({ params }: PageProps) {
  const { locale } = await params;

  // جلب البيانات للفئات
  let categories: Category[] = [];

  try {
    const categoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/navbar/categories`, {
      next: { revalidate: 900 } // 15 دقيقة
    });

    if (categoriesResponse.ok) {
      const categoriesResult = await categoriesResponse.json();
      if (categoriesResult.success && Array.isArray(categoriesResult.data)) {
        categories = categoriesResult.data.filter((cat: Category) => cat.is_active);
      }
    }
  } catch (error) {
    console.error('Error fetching categories data:', error);
    // في حالة الخطأ، سيتم استخدام البيانات الفارغة والمكون سيجلب البيانات من جانب العميل
  }

  return <ResponsiveCategoriesPage locale={locale} categories={categories} />;
}
