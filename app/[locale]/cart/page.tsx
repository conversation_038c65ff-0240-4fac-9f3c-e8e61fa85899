import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { generateCartMetadata } from '../../../lib/metadata';
import ResponsiveCartPage from '../../../components/ResponsiveCartPage';

// إنشاء metadata لصفحة السلة
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;
  return generateCartMetadata(locale);
}

export default async function CartPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return (
    <ResponsiveCartPage locale={locale} />
  );
}
