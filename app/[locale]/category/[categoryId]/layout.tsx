import { Metadata } from 'next';
import { Locale } from '../../../../lib/i18n';

interface CategoryLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string; categoryId: string }>;
}

// دالة لجلب بيانات الفئة للـ SEO
async function getCategoryForSEO(categoryId: string) {
  try {
    const baseUrl = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3000' 
      : (process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com');
      
    const response = await fetch(`${baseUrl}/api/categories?id=${categoryId}`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      console.log(`Category API returned ${response.status} for ID: ${categoryId}`);
      return null;
    }
    
    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error('Error fetching category for SEO:', error);
    // إرجاع بيانات افتراضية بدلاً من null
    return {
      id: categoryId,
      name: 'فئة',
      name_ar: 'فئة',
      description: 'وصف الفئة',
      description_ar: 'وصف الفئة',
    };
  }
}

// إنشاء metadata للفئة
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string; categoryId: string }>
}): Promise<Metadata> {
  const { locale: localeParam, categoryId } = await params;
  const locale = (localeParam || 'ar') as Locale;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  
  // جلب بيانات الفئة
  const category = await getCategoryForSEO(categoryId);
  
  if (!category) {
    return {
      title: locale === 'ar' ? 'فئة غير موجودة | دروب هجر' : 'Category Not Found | DROOB HAJER',
      description: locale === 'ar' 
        ? 'عذراً، لم نتمكن من العثور على هذه الفئة'
        : 'Sorry, we could not find this category',
    };
  }
  
  const categoryName = locale === 'ar' ? category.name_ar : category.name;
  const categoryDescription = locale === 'ar' ? category.description_ar : category.description;
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const title = locale === 'ar' 
    ? `${categoryName} - تجهيزات فندقية متخصصة | ${siteName}`
    : `${categoryName} - Specialized Hotel Equipment | ${siteName}`;
    
  const description = categoryDescription || (locale === 'ar'
    ? `استكشف مجموعة ${categoryName} من التجهيزات الفندقية عالية الجودة في دروب هجر. احصل على عروض أسعار مخصصة لمشروعك الفندقي.`
    : `Explore ${categoryName} collection of high-quality hotel equipment at DROOB HAJER. Get custom quotes for your hotel project.`);
  
  return {
    title: title,
    description: description,
    keywords: locale === 'ar'
      ? `${categoryName}, تجهيزات فندقية, أثاث فندقي, دروب هجر, معدات فندقية, ${categoryName} فندقي`
      : `${categoryName}, hotel equipment, hotel furniture, DROOB HAJER, hotel supplies, hotel ${categoryName}`,
    
    openGraph: {
      title: title,
      description: description,
      url: `${baseUrl}/${locale}/category/${categoryId}`,
      siteName: siteName,
      images: category.image_url ? [
        {
          url: category.image_url,
          width: 800,
          height: 600,
          alt: categoryName,
        },
      ] : [
        {
          url: `${baseUrl}/images/og-category.jpg`,
          width: 1200,
          height: 630,
          alt: categoryName,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },
    
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: category.image_url ? [category.image_url] : [`${baseUrl}/images/og-category.jpg`],
    },
    
    alternates: {
      canonical: `${baseUrl}/${locale}/category/${categoryId}`,
      languages: {
        'ar': `${baseUrl}/ar/category/${categoryId}`,
        'en': `${baseUrl}/en/category/${categoryId}`,
      },
    },
  };
}

export default async function CategoryLayout({ 
  children 
}: CategoryLayoutProps) {
  return (
    <>
      {children}
    </>
  );
}
