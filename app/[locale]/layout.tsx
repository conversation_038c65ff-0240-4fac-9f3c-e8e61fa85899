import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { locales, getDirection, type Locale } from '../../lib/i18n';
import '../globals.css';

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

// إنشاء metadata ديناميكي للـ layout
export async function generateMetadata(): Promise<Metadata> {
  return {
    icons: {
      icon: [
        { url: '/favicon.svg', type: 'image/svg+xml' },
        { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
        { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      ],
      apple: [
        { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
      ],
      shortcut: '/favicon.ico',
    },
    other: {
      'theme-color': '#3B82F6',
      'msapplication-TileColor': '#3B82F6',
      'msapplication-TileImage': '/favicon-32x32.png',
    },
    // إزالة title و description من هنا لتجنب التداخل مع metadata الخاص بكل صفحة
  };
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale: localeParam } = await params;

  // التحقق من صحة اللغة
  if (!locales.includes(localeParam as Locale)) {
    notFound();
  }

  const locale = localeParam as Locale;
  const direction = getDirection(locale);

  return (
    <div lang={locale} dir={direction} className={`${direction === 'rtl' ? 'rtl' : 'ltr'} font-tajawal min-h-screen`}>
      {children}
    </div>
  );
}
