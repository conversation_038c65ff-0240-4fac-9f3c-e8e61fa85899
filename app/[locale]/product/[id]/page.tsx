import { redirect } from 'next/navigation';
import { getProductWithDetails } from '@/lib/mysql-database';
import { generateProductUrl } from '@/utils/generateSlug';

// تعريف نوع Locale محلياً
type Locale = 'ar' | 'en';

interface ProductRedirectPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

export default async function ProductRedirectPage({ params }: ProductRedirectPageProps) {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const id = resolvedParams?.id || '';

  try {
    // جلب بيانات المنتج لإنشاء الرابط الصحيح
    const product = await getProductWithDetails(id);

    if (product) {
      // إنشاء الرابط الجديد مع slug صحيح
      const newUrl = generateProductUrl(product, locale);
      redirect(`/${locale}${newUrl}`);
    } else {
      // إذا لم يتم العثور على المنتج، توجيه إلى صفحة المنتجات
      redirect(`/${locale}/products`);
    }
  } catch (error) {
    console.error('Error redirecting product:', error);
    // في حالة الخطأ، توجيه إلى صفحة المنتجات
    redirect(`/${locale}/products`);
  }
}

