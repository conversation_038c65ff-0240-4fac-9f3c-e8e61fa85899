'use client';

import { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Locale } from '../../../../lib/i18n';
// import { getTranslation } from '../../../../lib/translations';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ProductError({ error, reset }: ErrorProps) {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const router = useRouter();

  // Translation function (currently not used but available for future use)
  // const t = (key: string) => getTranslation(locale, key as keyof typeof translations);

  useEffect(() => {
    // تسجيل الخطأ للمراقبة
    console.error('❌ Product page error:', error);
    
    // إرسال تقرير الخطأ (يمكن إضافة خدمة مراقبة هنا)
    if (typeof window !== 'undefined') {
      // يمكن إضافة Sentry أو خدمة مراقبة أخرى هنا
      console.error('Product page error details:', {
        message: error.message,
        digest: error.digest,
        stack: error.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      });
    }
  }, [error]);

  return (
    <>
      <Navbar locale={locale} />
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
            </div>
            
            <h1 className="text-xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'حدث خطأ غير متوقع' : 'An unexpected error occurred'}
            </h1>
            
            <p className="text-gray-600 mb-6">
              {locale === 'ar' 
                ? 'عذراً، حدث خطأ أثناء تحميل صفحة المنتج. يرجى المحاولة مرة أخرى.'
                : 'Sorry, an error occurred while loading the product page. Please try again.'
              }
            </p>
            
            <div className="space-y-3">
              <button
                onClick={reset}
                className="w-full bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors"
              >
                {locale === 'ar' ? 'إعادة المحاولة' : 'Try Again'}
              </button>
              
              <button
                onClick={() => router.push(`/${locale}/products`)}
                className="w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
              >
                {locale === 'ar' ? 'العودة إلى المنتجات' : 'Back to Products'}
              </button>
              
              <button
                onClick={() => router.push(`/${locale}`)}
                className="w-full text-primary hover:text-primary/80 transition-colors"
              >
                {locale === 'ar' ? 'العودة إلى الصفحة الرئيسية' : 'Back to Home'}
              </button>
            </div>
            
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-500">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto">
                  {error.message}
                  {error.stack && `\n\n${error.stack}`}
                </pre>
              </details>
            )}
          </div>
        </div>
      </div>
      <Footer locale={locale} />
    </>
  );
}
