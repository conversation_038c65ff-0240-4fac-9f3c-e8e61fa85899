import { Metadata } from 'next';
import { Locale } from '../../lib/i18n';
import { getPageSEO } from '../../lib/seo.config';
import { getProductsWithDetailsLimited, getCategories } from '../../lib/mysql-database';
import ServerHomePage from '../../components/server/ServerHomePage';
import { Category, ProductWithDetails } from '../../types/mysql-database';
import StructuredData from '../../components/SEO/StructuredData';

// إنشاء metadata للصفحة الرئيسية
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  const seoData = getPageSEO(locale, 'home');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.additionalMetaTags?.find(tag => tag.name === 'keywords')?.content,

    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: `${baseUrl}/${locale}`,
      siteName: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      images: [
        {
          url: `${baseUrl}/images/og-home.jpg`,
          width: 1200,
          height: 630,
          alt: seoData.title,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [`${baseUrl}/images/og-home.jpg`],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}`,
      languages: {
        'ar': `${baseUrl}/ar`,
        'en': `${baseUrl}/en`,
      },
    },
  };
}

export default async function HomePage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات مباشرة من قاعدة البيانات
  let categories: Category[] = [];
  let featuredProducts: ProductWithDetails[] = [];

  try {
    console.log('🚀 Server: Fetching homepage data from database...');

    // جلب الفئات النشطة مباشرة من قاعدة البيانات
    const allCategories = await getCategories();
    categories = allCategories.filter((cat: Category) => cat.is_active);

    // جلب المنتجات المميزة مباشرة من قاعدة البيانات (أول 6 منتجات)
    featuredProducts = await getProductsWithDetailsLimited(1, 6);

    console.log(`✅ Server: Fetched ${featuredProducts.length} featured products and ${categories.length} categories`);
  } catch (error) {
    console.error('❌ Server: Error fetching homepage data from database:', error);
    // في حالة الخطأ، سيتم استخدام البيانات الفارغة
  }

  return (
    <>
      <StructuredData locale={locale} type="website" />
      <StructuredData locale={locale} type="organization" />
      <ServerHomePage
        locale={locale}
        categories={categories}
        featuredProducts={featuredProducts}
      />
    </>
  );
}
