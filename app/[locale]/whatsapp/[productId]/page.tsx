import { Metadata } from 'next';
import { Suspense } from 'react';
import { Locale } from '../../../../lib/i18n';
import Navbar from '../../../../components/Navbar';
import WhatsAppProductHandler from '../../../../components/WhatsAppProductHandler';

// إنشاء metadata لصفحة واتساب المنتج
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string; productId: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return {
    title: locale === 'ar' ? 'توجيه إلى واتساب | دروب هجر' : 'WhatsApp Redirect | DROOB HAJER',
    description: locale === 'ar' 
      ? 'سيتم توجيهك إلى واتساب للتواصل معنا حول المنتج المحدد'
      : 'You will be redirected to WhatsApp to contact us about the selected product',
    robots: 'noindex, nofollow', // منع فهرسة هذه الصفحة
  };
}

export default async function WhatsAppProductPage({
  params
}: {
  params: Promise<{ locale: string; productId: string }>
}) {
  const { locale: localeParam, productId } = await params;
  const locale = (localeParam || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <Suspense fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-700">
              {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </h2>
          </div>
        </div>
      }>
        <WhatsAppProductHandler locale={locale} productId={productId} />
      </Suspense>
    </>
  );
}
