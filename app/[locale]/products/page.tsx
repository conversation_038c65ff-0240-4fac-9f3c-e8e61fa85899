import { Suspense } from 'react';
import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { getPageSEO } from '../../../lib/seo.config';
import { getProductsWithDetailsLimited, getCategories } from '../../../lib/mysql-database';
import ServerProductsPage from '../../../components/ServerProductsPage';
import { Category, ProductWithDetails } from '../../../types/mysql-database';

// إنشاء metadata لصفحة المنتجات
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  const seoData = getPageSEO(locale, 'products');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.additionalMetaTags?.find(tag => tag.name === 'keywords')?.content,

    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: `${baseUrl}/${locale}/products`,
      siteName: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      images: [
        {
          url: `${baseUrl}/images/og-products.jpg`,
          width: 1200,
          height: 630,
          alt: seoData.title,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [`${baseUrl}/images/og-products.jpg`],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}/products`,
      languages: {
        'ar': `${baseUrl}/ar/products`,
        'en': `${baseUrl}/en/products`,
      },
    },
  };
}

// Loading component for Suspense fallback
function ProductsLoading() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-primary py-12">
        <div className="container mx-auto px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-white/20 rounded w-48 mx-auto mb-4"></div>
            <div className="h-4 bg-white/10 rounded w-96 mx-auto"></div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-4 py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    </div>
  );
}

export default async function ProductsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات مباشرة من قاعدة البيانات
  let products: ProductWithDetails[] = [];
  let categories: Category[] = [];

  try {
    console.log('🚀 Server: Fetching products and categories from database...');

    // جلب الفئات النشطة مباشرة من قاعدة البيانات
    const allCategories = await getCategories();
    categories = allCategories.filter((cat: Category) => cat.is_active);

    // جلب المنتجات مباشرة من قاعدة البيانات (أول 12 منتج)
    products = await getProductsWithDetailsLimited(1, 12);

    console.log(`✅ Server: Fetched ${products.length} products and ${categories.length} categories`);
  } catch (error) {
    console.error('❌ Server: Error fetching data from database:', error);
    // في حالة الخطأ، سيتم استخدام البيانات الفارغة
  }

  return (
    <Suspense fallback={<ProductsLoading />}>
      <ServerProductsPage
        locale={locale}
        products={products}
        categories={categories}
      />
    </Suspense>
  );
}
