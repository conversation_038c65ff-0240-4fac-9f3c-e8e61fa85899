import { NextResponse } from 'next/server';
import { getStats } from '../../../../utils/database';

export async function GET() {
  try {
    const stats = getStats();
    return NextResponse.json({ success: true, data: stats });
  } catch (error) {
    console.error('Stats API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
