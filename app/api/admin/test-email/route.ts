import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { getContactInfo } from '@/lib/mysql-database';

// دالة لاختبار SMTP مع Nodemailer
interface ContactInfo {
  email: string;
  Password: string;
  host?: string;
  port?: number;
}

async function testWithNodemailer(contactInfo: ContactInfo, email: string) {
  const emailPass = contactInfo.Password;
  const smtpHost = contactInfo.host || 'smtp.titan.email';
  const smtpPort = contactInfo.port || 465;

  console.log('🧪 اختبار مع Nodemailer...');
  console.log('📧 من:', contactInfo.email);
  console.log('📬 إلى:', email);
  console.log('🏠 خادم:', smtpHost);
  console.log('🔌 منفذ:', smtpPort);

  // إعدادات SMTP متعددة للتجربة
  const transporterConfigs = [
    {
      name: 'SSL (465)',
      host: smtpHost,
      port: smtpPort,
      secure: smtpPort === 465,
      auth: {
        user: contactInfo.email,
        pass: emailPass
      },
      tls: {
        rejectUnauthorized: false
      }
    },
    {
      name: 'TLS (587)',
      host: smtpHost,
      port: 587,
      secure: false,
      auth: {
        user: contactInfo.email,
        pass: emailPass
      },
      tls: {
        rejectUnauthorized: false,
        starttls: {
          enable: true
        }
      }
    }
  ];

  for (const config of transporterConfigs) {
    try {
      console.log(`🔄 محاولة ${config.name}...`);
      const transporter = nodemailer.createTransport(config);
      await transporter.verify();

      const mailOptions = {
        from: `"DROOB HAJER - اختبار" <${contactInfo.email}>`,
        to: email,
        subject: `🧪 اختبار إعدادات البريد الإلكتروني - ${new Date().toLocaleDateString('ar-SA')}`,
        html: `
          <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px;">
            <h2>🧪 اختبار SMTP ناجح!</h2>
            <p>تم إرسال هذا الإيميل بنجاح باستخدام Nodemailer مع ${config.name}</p>
            <p><em>الوقت: ${new Date().toLocaleString('ar-SA')}</em></p>
          </div>
        `
      };

      const info = await transporter.sendMail(mailOptions);
      console.log('✅ نجح إرسال الإيميل مع Nodemailer!');

      return {
        success: true,
        message: `تم إرسال الإيميل التجريبي بنجاح باستخدام ${config.name}!`,
        method: 'Nodemailer',
        config: config.name,
        messageId: info.messageId
      };
    } catch (error) {
      console.log(`❌ فشل ${config.name}:`, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  throw new Error('فشل جميع إعدادات Nodemailer');
}

// دالة لاختبار مع Resend API
async function testWithResend(contactInfo: ContactInfo, email: string) {
  const resendApiKey = process.env.RESEND_API_KEY;

  if (!resendApiKey) {
    throw new Error('Resend API key غير موجود');
  }

  console.log('🧪 اختبار مع Resend API...');

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${resendApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      from: `DROOB HAJER <${contactInfo.email}>`,
      to: [email],
      subject: `🧪 اختبار إعدادات البريد الإلكتروني - ${new Date().toLocaleDateString('ar-SA')}`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px;">
          <h2>🧪 اختبار Resend API ناجح!</h2>
          <p>تم إرسال هذا الإيميل بنجاح باستخدام Resend API</p>
          <p><em>الوقت: ${new Date().toLocaleString('ar-SA')}</em></p>
        </div>
      `
    }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Resend API error: ${errorData.message || 'Unknown error'}`);
  }

  const data = await response.json();
  console.log('✅ نجح إرسال الإيميل مع Resend!');

  return {
    success: true,
    message: 'تم إرسال الإيميل التجريبي بنجاح باستخدام Resend API!',
    method: 'Resend',
    messageId: data.id
  };
}

// POST - اختبار إرسال الإيميل
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json({
        success: false,
        message: 'يرجى إدخال الإيميل'
      }, { status: 400 });
    }

    // جلب إعدادات الإيميل من قاعدة البيانات
    const contactInfo = await getContactInfo();

    if (!contactInfo?.email) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على إعدادات الإيميل في قاعدة البيانات'
      }, { status: 400 });
    }

    if (!contactInfo?.Password) {
      return NextResponse.json({
        success: false,
        message: 'كلمة مرور الإيميل غير موجودة في قاعدة البيانات'
      }, { status: 400 });
    }

    console.log('🧪 بدء اختبار إرسال الإيميل...');
    console.log('📧 من:', contactInfo.email);
    console.log('📬 إلى:', email);

    // تجربة طرق مختلفة لإرسال الإيميل
    const methods = [
      { name: 'Nodemailer', func: testWithNodemailer },
      { name: 'Resend', func: testWithResend }
    ];

    let lastError = null;

    // تأكيد أن contactInfo.email و Password ليسا undefined
    const safeContactInfo = {
      ...contactInfo,
      email: contactInfo.email ?? '',
      Password: contactInfo.Password ?? ''
    };

    for (const method of methods) {
      try {
        console.log(`🔄 محاولة ${method.name}...`);
        const result = await method.func(safeContactInfo, email);

        console.log(`✅ نجح ${method.name}!`);
        return NextResponse.json(result);

      } catch (error) {
        console.log(`❌ فشل ${method.name}:`, error instanceof Error ? error.message : 'Unknown error');
        lastError = error;
      }
    }

    // إذا فشلت جميع الطرق
    throw lastError || new Error('فشل جميع طرق الإرسال');

  } catch (error) {
    console.error('❌ خطأ في اختبار الإيميل:', error);
    
    let errorMessage = 'خطأ غير معروف في إرسال الإيميل';
    
    if (error instanceof Error) {
      if (error.message.includes('Invalid login') || error.message.includes('authentication failed')) {
        errorMessage = 'خطأ في المصادقة: تحقق من الإيميل وكلمة المرور';
      } else if (error.message.includes('ECONNECTION') || error.message.includes('ETIMEDOUT')) {
        errorMessage = 'خطأ في الاتصال: تحقق من إعدادات الخادم والمنفذ';
      } else if (error.message.includes('ENOTFOUND')) {
        errorMessage = 'خطأ في العثور على الخادم: تحقق من عنوان SMTP';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json({
      success: false,
      message: errorMessage,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
