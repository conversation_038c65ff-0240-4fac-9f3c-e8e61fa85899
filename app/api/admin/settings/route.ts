import { NextRequest, NextResponse } from 'next/server';
import { getSettings, updateSettings } from '../../../../utils/database';

export async function GET() {
  try {
    const settings = getSettings();
    return NextResponse.json({ success: true, data: settings });
  } catch (error) {
    console.error('Settings GET API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const updatedSettings = updateSettings(body);
    return NextResponse.json({ success: true, data: updatedSettings });
  } catch (error) {
    console.error('Settings PUT API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
