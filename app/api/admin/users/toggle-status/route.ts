import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { executeQuery, executeQuerySingle } from '../../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  is_active: boolean;
}

// التحقق من صحة JWT token
function verifyToken(token: string): { userId: number } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: number };
  } catch {
    return null;
  }
}

// استخراج token من الطلب
async function extractToken(req: NextRequest): Promise<string | null> {
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const cookieStore = await cookies();
  const tokenFromCookie = cookieStore.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export async function PUT(req: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = await extractToken(req);
    if (!token) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      }, { status: 401 });
    }

    const { userId } = await req.json();

    // التحقق من وجود معرف المستخدم
    if (!userId) {
      return NextResponse.json({
        message: 'معرف المستخدم مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من أن المستخدم لا يعطل نفسه
    if (decoded.userId === userId) {
      return NextResponse.json({
        message: 'لا يمكنك تعطيل حسابك الخاص',
        success: false
      }, { status: 400 });
    }

    // الحصول على حالة المستخدم الحالية
    const user = await executeQuerySingle<AdminUser>(
      'SELECT id, username, is_active FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!user) {
      return NextResponse.json({
        message: 'المستخدم غير موجود',
        success: false
      }, { status: 404 });
    }

    // تبديل الحالة
    const newStatus = !user.is_active;

    await executeQuery(
      'UPDATE admins SET is_active = ?, updated_at = NOW() WHERE id = ?',
      [newStatus, userId]
    );

    return NextResponse.json({
      success: true,
      message: newStatus ? 'تم تفعيل المستخدم بنجاح' : 'تم إلغاء تفعيل المستخدم بنجاح',
      newStatus
    });

  } catch (error) {
    console.error('Toggle user status error:', error);
    return NextResponse.json({
      message: 'حدث خطأ في الخادم',
      success: false
    }, { status: 500 });
  }
}
