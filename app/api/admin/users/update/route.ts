import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { executeQuery, executeQuerySingle } from '../../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string): { userId: number } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: number };
  } catch {
    return null;
  }
}

// استخراج token من الطلب
async function extractToken(req: NextRequest): Promise<string | null> {
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const cookieStore = await cookies();
  const tokenFromCookie = cookieStore.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export async function PUT(req: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = await extractToken(req);
    if (!token) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      }, { status: 401 });
    }

    const { userId, username, email } = await req.json();

    // التحقق من وجود البيانات المطلوبة
    if (!userId || !username || !email) {
      return NextResponse.json({
        message: 'جميع الحقول مطلوبة',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        message: 'البريد الإلكتروني غير صحيح',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود المستخدم
    const existingUser = await executeQuerySingle(
      'SELECT id FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!existingUser) {
      return NextResponse.json({
        message: 'المستخدم غير موجود',
        success: false
      }, { status: 404 });
    }

    // التحقق من عدم وجود اسم المستخدم مع مستخدم آخر
    const duplicateUsername = await executeQuerySingle(
      'SELECT id FROM admins WHERE username = ? AND id != ? AND deleted_at IS NULL',
      [username, userId]
    );

    if (duplicateUsername) {
      return NextResponse.json({
        message: 'اسم المستخدم موجود بالفعل',
        success: false
      }, { status: 409 });
    }

    // التحقق من عدم وجود البريد الإلكتروني مع مستخدم آخر
    const duplicateEmail = await executeQuerySingle(
      'SELECT id FROM admins WHERE email = ? AND id != ? AND deleted_at IS NULL',
      [email, userId]
    );

    if (duplicateEmail) {
      return NextResponse.json({
        message: 'البريد الإلكتروني موجود بالفعل',
        success: false
      }, { status: 409 });
    }

    // تحديث بيانات المستخدم
    await executeQuery(
      'UPDATE admins SET username = ?, email = ?, updated_at = NOW() WHERE id = ?',
      [username, email, userId]
    );

    // الحصول على بيانات المستخدم المحدثة
    const updatedUser = await executeQuerySingle(
      'SELECT id, username, email, is_active, last_login, created_at, updated_at FROM admins WHERE id = ?',
      [userId]
    );

    return NextResponse.json({
      success: true,
      message: 'تم تحديث البيانات بنجاح',
      user: updatedUser
    });

  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json({
      message: 'حدث خطأ في الخادم',
      success: false
    }, { status: 500 });
  }
}
