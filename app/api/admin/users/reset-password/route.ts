import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { executeQuery, executeQuerySingle } from '../../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
  role: string;
}

// التحقق من صحة JWT token
function verifyToken(token: string): { userId: number } | null {
  try {
    return jwt.verify(token, JWT_SECRET) as { userId: number };
  } catch {
    return null;
  }
}

// استخراج token من الطلب
async function extractToken(req: NextRequest): Promise<string | null> {
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const cookieStore = await cookies();
  const tokenFromCookie = cookieStore.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export async function PUT(req: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = await extractToken(req);
    if (!token) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      }, { status: 401 });
    }

    // التحقق من أن المستخدم الحالي هو أدمن
    const currentAdmin = await executeQuerySingle<AdminUser>(
      'SELECT id, username, email, role, is_active FROM admins WHERE id = ? AND deleted_at IS NULL',
      [decoded.userId]
    );

    if (!currentAdmin || !currentAdmin.is_active) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 403 });
    }

    const { userId, newPassword } = await req.json();

    // التحقق من وجود البيانات المطلوبة
    if (!userId || !newPassword) {
      return NextResponse.json({
        message: 'معرف المستخدم وكلمة المرور الجديدة مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من قوة كلمة المرور الجديدة
    if (newPassword.length < 6) {
      return NextResponse.json({
        message: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود المستخدم المراد تغيير كلمة مروره
    const targetUser = await executeQuerySingle<AdminUser>(
      'SELECT id, username, email, password_hash, is_active FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!targetUser) {
      return NextResponse.json({
        message: 'المستخدم غير موجود',
        success: false
      }, { status: 404 });
    }

    // التحقق من أن كلمة المرور الجديدة مختلفة عن الحالية
    const isSamePassword = await bcrypt.compare(newPassword, targetUser.password_hash);

    if (isSamePassword) {
      return NextResponse.json({
        message: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية',
        success: false
      }, { status: 400 });
    }

    // تشفير كلمة المرور الجديدة
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // تحديث كلمة المرور في قاعدة البيانات
    await executeQuery(
      'UPDATE admins SET password_hash = ?, updated_at = NOW() WHERE id = ?',
      [newPasswordHash, userId]
    );

    // تسجيل العملية في السجلات (اختياري)
    console.log(`Admin ${currentAdmin.username} (ID: ${currentAdmin.id}) reset password for user ${targetUser.username} (ID: ${targetUser.id})`);

    return NextResponse.json({
      success: true,
      message: `تم تغيير كلمة مرور المستخدم "${targetUser.username}" بنجاح`
    });

  } catch (error) {
    console.error('Reset password error:', error);
    return NextResponse.json({
      message: 'حدث خطأ في الخادم',
      success: false
    }, { status: 500 });
  }
}
