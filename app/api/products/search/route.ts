import { NextRequest, NextResponse } from 'next/server';
import { getProducts } from '../../../../lib/mysql-database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const q = searchParams.get('q');

    if (!q || typeof q !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Search query is required',
        messageAr: 'استعلام البحث مطلوب'
      }, { status: 400 });
    }

    // البحث في المنتجات
    const allProducts = await getProducts();
    const searchQuery = q.toLowerCase();

    const filteredProducts = allProducts.filter(product =>
      product.title.toLowerCase().includes(searchQuery) ||
      (product.title_ar && product.title_ar.toLowerCase().includes(searchQuery)) ||
      (product.description && product.description.toLowerCase().includes(searchQuery)) ||
      (product.description_ar && product.description_ar.toLowerCase().includes(searchQuery))
    );

    return NextResponse.json({
      success: true,
      data: filteredProducts
    });
  } catch (error) {
    console.error('Products Search API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
