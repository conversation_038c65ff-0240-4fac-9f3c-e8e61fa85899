import { NextRequest, NextResponse } from 'next/server';
import { getProductWithDetails, getCategoryById, getSubcategoryById } from '@/lib/mysql-database';
import { measureAsync, recordError } from '@/lib/monitoring';
import { isExternalClient, getClientType, logExternalClientError } from '@/lib/external-client-handler';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now();
  let productId = 'unknown';

  try {
    const { id } = await params;
    productId = id;
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const isExternal = isExternalClient(request);
    const clientType = getClientType(request);

    console.log(`🔍 Product API: Request for ID ${id} from ${isExternal ? 'External' : 'Internal'} client: ${clientType}`);

    if (!id || typeof id !== 'string') {
      console.log(`❌ Product API: Invalid ID provided: ${id}`);
      recordError(`Invalid product ID: ${id}`, {
        clientType,
        isExternal,
        userAgent
      }, 'low');

      return NextResponse.json({
        success: false,
        error: 'Invalid product ID',
        messageAr: 'معرف المنتج غير صحيح'
      }, { status: 400 });
    }

    // قياس وقت جلب المنتج من قاعدة البيانات
    const product = await measureAsync(
      'product_fetch_db',
      () => getProductWithDetails(id),
      { productId: id, clientType, isExternal }
    );

    if (!product) {
      console.log(`❌ Product API: Product not found for ID: ${id}`);
      recordError(`Product not found: ${id}`, {
        clientType,
        isExternal,
        userAgent
      }, 'low');

      return NextResponse.json({
        success: false,
        error: 'Product not found',
        messageAr: 'المنتج غير موجود'
      }, { status: 404 });
    }

    // جلب بيانات الفئة والفئة الفرعية بشكل متوازي
    const [category, subcategory] = await Promise.all([
      product.category_id ? measureAsync(
        'category_fetch_db',
        () => getCategoryById(product.category_id!),
        { categoryId: product.category_id, clientType, isExternal }
      ) : null,
      product.subcategory_id ? measureAsync(
        'subcategory_fetch_db',
        () => getSubcategoryById(product.subcategory_id!),
        { subcategoryId: product.subcategory_id, clientType, isExternal }
      ) : null
    ]);

    const duration = Date.now() - startTime;
    console.log(`✅ Product API: Successfully returned product ${id} with related data in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: {
        product,
        category,
        subcategory
      }
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600'
      }
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    console.error(`❌ Product API Error for ID ${productId} after ${duration}ms:`, error);

    // تسجيل الخطأ في نظام المراقبة
    recordError(error as Error, {
      productId,
      clientType: getClientType(request),
      isExternal: isExternalClient(request),
      userAgent: request.headers.get('user-agent'),
      duration,
      endpoint: `/api/products/${productId}`
    }, 'high');

    // تسجيل خاص للعملاء الخارجيين
    if (isExternalClient(request)) {
      logExternalClientError(request, error as Error, 'product_api');
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي',
      ...(process.env.NODE_ENV === 'development' && { details: errorMessage })
    }, { status: 500 });
  }
}
