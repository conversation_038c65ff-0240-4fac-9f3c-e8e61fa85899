import { NextRequest, NextResponse } from 'next/server';
import {
  getProductsWithDetails,
  getFeaturedProductsWithDetails,
  getProductsByCategoryWithDetails,
  getProductsBySubcategoryWithDetails
} from '@/lib/mysql-database';
import { ProductWithDetails } from '@/types/mysql-database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured');
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');

    let products: ProductWithDetails[];

    if (featured === 'true') {
      products = await getFeaturedProductsWithDetails();
    } else if (categoryId && typeof categoryId === 'string') {
      products = await getProductsByCategoryWithDetails(categoryId);
    } else if (subcategoryId && typeof subcategoryId === 'string') {
      products = await getProductsBySubcategoryWithDetails(subcategoryId);
    } else {
      products = await getProductsWithDetails();
    }

    return NextResponse.json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
