import { NextResponse } from 'next/server';
import { getProducts } from '../../../lib/mysql-database';
import { getCategories } from '../../../lib/mysql-database';
import { getSubcategories } from '../../../lib/mysql-database';
import { Product, Category, Subcategory } from '../../../types/mysql-database';

// دالة لإنشاء XML للـ sitemap مع دعم hreflang
function generateSitemapXML(urls: Array<{
  loc: string;
  lastmod: string;
  changefreq: string;
  priority: string;
}>) {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  // تجميع URLs حسب النوع والمعرف لإنشاء hreflang
  const urlGroups: { [key: string]: typeof urls } = {};

  urls.forEach(url => {
    // استخراج النوع والمعرف من URL
    const urlParts = url.loc.replace(siteUrl, '').split('/');
    let groupKey = '';

    if (urlParts.length === 2 && urlParts[1] === '') {
      // الصفحة الرئيسية
      groupKey = 'home';
    } else if (urlParts.length === 2) {
      // صفحات اللغة الرئيسية (/ar, /en)
      groupKey = 'home';
    } else if (urlParts.length === 3) {
      // صفحات ثابتة (/ar/about, /en/products)
      groupKey = urlParts[2];
    } else if (urlParts.length === 4) {
      // صفحات ديناميكية (/ar/category/id, /en/subcategory/id)
      groupKey = `${urlParts[2]}-${urlParts[3]}`;
    }

    if (!urlGroups[groupKey]) {
      urlGroups[groupKey] = [];
    }
    urlGroups[groupKey].push(url);
  });

  const urlsXML = urls.map(url => {
    // العثور على المجموعة التي تنتمي إليها هذه الصفحة
    const urlParts = url.loc.replace(siteUrl, '').split('/');
    let groupKey = '';

    if (urlParts.length === 2 && urlParts[1] === '') {
      groupKey = 'home';
    } else if (urlParts.length === 2) {
      groupKey = 'home';
    } else if (urlParts.length === 3) {
      groupKey = urlParts[2];
    } else if (urlParts.length === 4) {
      groupKey = `${urlParts[2]}-${urlParts[3]}`;
    }

    const relatedUrls = urlGroups[groupKey] || [];

    // إنشاء hreflang links
    const hreflangLinks = relatedUrls.map(relatedUrl => {
      const relatedParts = relatedUrl.loc.replace(siteUrl, '').split('/');
      let hreflang = 'x-default';

      if (relatedParts.length >= 2) {
        if (relatedParts[1] === 'ar') hreflang = 'ar';
        else if (relatedParts[1] === 'en') hreflang = 'en';
        else if (relatedParts[1] === '') hreflang = 'x-default';
      }

      return `    <xhtml:link rel="alternate" hreflang="${hreflang}" href="${relatedUrl.loc}" />`;
    }).join('\n');

    return `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
${hreflangLinks}
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${urlsXML}
</urlset>`;
}

export async function GET() {
  try {
    const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
    const currentTime = new Date().toISOString();
    const urls: Array<{
      loc: string;
      lastmod: string;
      changefreq: string;
      priority: string;
    }> = [];

    // الصفحات الثابتة
    const staticPages = [
      { loc: `${siteUrl}/ar`, changefreq: 'daily', priority: '1.0' },
      { loc: `${siteUrl}/en`, changefreq: 'daily', priority: '1.0' },
      { loc: `${siteUrl}/ar/products`, changefreq: 'daily', priority: '0.9' },
      { loc: `${siteUrl}/en/products`, changefreq: 'daily', priority: '0.9' },
      { loc: `${siteUrl}/ar/categories`, changefreq: 'weekly', priority: '0.8' },
      { loc: `${siteUrl}/en/categories`, changefreq: 'weekly', priority: '0.8' },
      { loc: `${siteUrl}/ar/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/about`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/ar/contact`, changefreq: 'monthly', priority: '0.6' },
      { loc: `${siteUrl}/en/contact`, changefreq: 'monthly', priority: '0.6' },
    ];

    // إضافة الصفحات الثابتة
    staticPages.forEach(page => {
      urls.push({
        ...page,
        lastmod: currentTime,
      });
    });

    // جلب المنتجات
    try {
      const products = await getProducts();
      if (products && products.length > 0) {
        products.forEach((product: Product) => {
          const lastmod = product.updated_at ? new Date(product.updated_at).toISOString() : currentTime;

          // إنشاء slug من عنوان المنتج
          const generateSlug = (text: string) => {
            if (!text) return '';
            return text
              .toLowerCase()
              .replace(/[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF-]/g, '')
              .replace(/\s+/g, ' ')
              .trim()
              .replace(/\s/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-+|-+$/g, '');
          };

          const arSlug = generateSlug(product.title_ar);
          const enSlug = generateSlug(product.title);

          urls.push({
            loc: `${siteUrl}/ar/products/${arSlug}-${product.id}`,
            changefreq: 'weekly',
            priority: '0.8',
            lastmod: lastmod,
          });

          urls.push({
            loc: `${siteUrl}/en/products/${enSlug}-${product.id}`,
            changefreq: 'weekly',
            priority: '0.8',
            lastmod: lastmod,
          });
        });
      }
    } catch (error) {
      console.error('Error fetching products for sitemap:', error);
    }

    // جلب الفئات
    try {
      const categories = await getCategories();
      if (categories && categories.length > 0) {
        categories.forEach((category: Category) => {
          const lastmod = category.updated_at ? new Date(category.updated_at).toISOString() : currentTime;

          urls.push({
            loc: `${siteUrl}/ar/category/${category.id}`,
            changefreq: 'weekly',
            priority: '0.7',
            lastmod: lastmod,
          });

          urls.push({
            loc: `${siteUrl}/en/category/${category.id}`,
            changefreq: 'weekly',
            priority: '0.7',
            lastmod: lastmod,
          });
        });
      }
    } catch (error) {
      console.error('Error fetching categories for sitemap:', error);
    }

    // جلب الفئات الفرعية
    try {
      const subcategories = await getSubcategories();
      if (subcategories && subcategories.length > 0) {
        subcategories.forEach((subcategory: Subcategory) => {
          const lastmod = subcategory.updated_at ? new Date(subcategory.updated_at).toISOString() : currentTime;

          urls.push({
            loc: `${siteUrl}/ar/subcategory/${subcategory.id}`,
            changefreq: 'weekly',
            priority: '0.6',
            lastmod: lastmod,
          });

          urls.push({
            loc: `${siteUrl}/en/subcategory/${subcategory.id}`,
            changefreq: 'weekly',
            priority: '0.6',
            lastmod: lastmod,
          });
        });
      }
    } catch (error) {
      console.error('Error fetching subcategories for sitemap:', error);
    }

    // إنشاء sitemap XML
    const sitemapXML = generateSitemapXML(urls);

    // إرجاع XML مع headers صحيحة
    return new NextResponse(sitemapXML, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // تخزين مؤقت لساعة واحدة
      },
    });

  } catch (error) {
    console.error('Error generating sitemap:', error);
    return NextResponse.json(
      { error: 'Failed to generate sitemap' },
      { status: 500 }
    );
  }
}
