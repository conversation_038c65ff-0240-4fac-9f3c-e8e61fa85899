import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
interface JwtPayload {
  // Add properties according to your JWT payload structure
  userId?: string;
  email?: string;
  iat?: number;
  exp?: number;
  [key: string]: unknown;
}

function verifyToken(token: string): JwtPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JwtPayload;
  } catch {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextRequest): string | null {
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const tokenFromCookie = req.cookies.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export async function DELETE(req: NextRequest) {
  // التحقق من المصادقة
  const token = extractToken(req);
  if (!token) {
    return NextResponse.json({
      success: false,
      message: 'Authentication required',
      messageAr: 'المصادقة مطلوبة'
    }, { status: 401 });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return NextResponse.json({
      success: false,
      message: 'Invalid token',
      messageAr: 'رمز المصادقة غير صحيح'
    }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { imageUrl } = body;

    if (!imageUrl) {
      return NextResponse.json({
        success: false,
        message: 'Image URL is required',
        messageAr: 'رابط الصورة مطلوب'
      }, { status: 400 });
    }

    // التحقق من أن الصورة في مجلد uploads
    if (!imageUrl.startsWith('/uploads/')) {
      return NextResponse.json({
        success: false,
        message: 'Invalid image URL',
        messageAr: 'رابط الصورة غير صحيح'
      }, { status: 400 });
    }

    // استخراج اسم الملف من الرابط
    const fileName = imageUrl.replace('/uploads/', '');
    const filePath = path.join(process.cwd(), 'public', 'uploads', fileName);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({
        success: false,
        message: 'Image file not found',
        messageAr: 'ملف الصورة غير موجود'
      }, { status: 404 });
    }

    // حذف الملف
    fs.unlinkSync(filePath);

    console.log('🗑️ تم حذف صورة الهيرو:', fileName);

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully',
      messageAr: 'تم حذف الصورة بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في حذف الصورة:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to delete image',
      messageAr: 'فشل في حذف الصورة',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    }, { status: 500 });
  }
}
