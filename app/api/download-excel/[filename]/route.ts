import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    const { filename } = await params;
    const excelDir = path.join(process.cwd(), 'public', 'uploads', 'excel');
    const filePath = path.join(excelDir, filename as string);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({
        success: false,
        message: 'الملف غير موجود'
      }, { status: 404 });
    }

    // قراءة الملف
    const fileBuffer = fs.readFileSync(filePath);

    // إنشاء الاستجابة مع الملف
    const response = new NextResponse(fileBuffer);

    // تعيين headers للتحميل
    response.headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    response.headers.set('Content-Length', fileBuffer.length.toString());

    return response;

  } catch (error) {
    console.error('Error downloading Excel file:', error);
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ أثناء تحميل الملف'
    }, { status: 500 });
  }
}
