import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// إعدادات API Route لزيادة حد حجم الطلبات
export const runtime = 'nodejs';
export const maxDuration = 30; // 30 ثانية
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No files provided',
        messageAr: 'لم يتم توفير ملفات'
      }, { status: 400 });
    }

    // التحقق من نوع الملفات وحجمها
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    for (const file of files) {
      if (!allowedTypes.includes(file.type)) {
        return NextResponse.json({
          success: false,
          message: `Invalid file type for ${file.name}. Only images are allowed.`,
          messageAr: `نوع ملف غير صحيح للملف ${file.name}. الصور فقط مسموحة.`
        }, { status: 400 });
      }

      if (file.size > maxSize) {
        return NextResponse.json({
          success: false,
          message: `File ${file.name} is too large. Maximum size is 5MB.`,
          messageAr: `الملف ${file.name} كبير جداً. الحد الأقصى 5 ميجابايت.`
        }, { status: 400 });
      }
    }

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // رفع جميع الملفات
    const uploadedFiles = [];

    for (const file of files) {
      // إنشاء اسم ملف فريد وآمن
      const timestamp = Date.now();
      const randomString = crypto.randomBytes(8).toString('hex');
      const fileExtension = path.extname(file.name);
      const fileName = `${timestamp}_${randomString}${fileExtension}`;
      const filePath = path.join(uploadDir, fileName);

      // حفظ الملف
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      fs.writeFileSync(filePath, buffer);

      // إضافة معلومات الملف إلى القائمة
      const fileUrl = `/uploads/${fileName}`;
      uploadedFiles.push(fileUrl);

      console.log(`✅ تم رفع الملف بنجاح: ${fileName}`);
    }

    return NextResponse.json({
      success: true,
      message: `${files.length} file(s) uploaded successfully`,
      messageAr: `تم رفع ${files.length} ملف بنجاح`,
      files: uploadedFiles
    });

  } catch (error: unknown) {
    console.error('❌ خطأ في رفع الملف:', error);
    return NextResponse.json({
      success: false,
      message: 'Upload failed',
      messageAr: 'فشل في رفع الملف',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}