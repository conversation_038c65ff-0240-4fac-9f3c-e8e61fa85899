import { NextResponse } from 'next/server';
import { testConnection } from '@/lib/database-config';
import { monitoring } from '@/lib/monitoring';

export async function GET() {
  const startTime = Date.now();
  
  try {
    // فحص اتصال قاعدة البيانات
    const dbHealthy = await testConnection();
    
    // الحصول على إحصائيات النظام
    const performanceStats = monitoring.getPerformanceStats();
    const errorStats = monitoring.getErrorStats();
    
    const health = {
      status: dbHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: {
        connected: dbHealthy,
        responseTime: Date.now() - startTime
      },
      performance: performanceStats,
      errors: errorStats,
      memory: process.memoryUsage(),
      version: process.version,
      environment: process.env.NODE_ENV
    };
    
    const statusCode = dbHealthy ? 200 : 503;
    
    return NextResponse.json(health, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
    
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  }
}
