import { NextResponse } from 'next/server';

// GET: إرجاع مفتاح IndexNow للتحقق
export async function GET() {
  try {
    const apiKey = 'f6d29396e4f1414ea4bffac192eb5e7f';
    
    return new NextResponse(apiKey, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'public, max-age=86400', // تخزين مؤقت ليوم واحد
      },
    });

  } catch (error) {
    console.error('خطأ في ملف مفتاح IndexNow:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
