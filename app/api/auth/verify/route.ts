import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '../../../../lib/auth';
import { getAdminUser } from '../../../../lib/secure-storage';

// GET - التحقق من صحة المصادقة
export async function GET(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const tokenPayload = requireAuth(request);
    
    if (!tokenPayload) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required',
        messageAr: 'المصادقة مطلوبة'
      }, { status: 401 });
    }
    
    // الحصول على بيانات المستخدم الحديثة
    const adminData = await getAdminUser();
    
    // التحقق من أن المستخدم ما زال موجوداً وصالحاً
    if (adminData.id !== tokenPayload.userId || adminData.username !== tokenPayload.username) {
      return NextResponse.json({
        success: false,
        message: 'Invalid token',
        messageAr: 'رمز المصادقة غير صالح'
      }, { status: 401 });
    }
    
    // إرجاع بيانات المستخدم
    return NextResponse.json({
      success: true,
      user: {
        id: adminData.id,
        username: adminData.username,
        email: adminData.email,
        role: adminData.role,
        lastLogin: adminData.lastLogin
      }
    });
    
  } catch (error) {
    console.error('Verify API error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
