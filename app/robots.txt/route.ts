import { NextResponse } from 'next/server';

export async function GET() {
  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  
  const robotsTxt = `User-agent: *
Allow: /

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/

# Disallow user-specific pages
Disallow: /cart
Disallow: /checkout
Disallow: /profile
Disallow: /login
Disallow: /register

# Disallow technical directories
Disallow: /_next/
Disallow: /static/

# Disallow file types
Disallow: *.json
Disallow: *.xml

# Allow important pages
Allow: /ar/
Allow: /en/
Allow: /ar/products
Allow: /en/products
Allow: /ar/categories
Allow: /en/categories
Allow: /ar/about
Allow: /en/about
Allow: /ar/contact
Allow: /en/contact

# Sitemap location
Sitemap: ${siteUrl}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1`;

  return new NextResponse(robotsTxt, {
    status: 200,
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400', // تخزين مؤقت ليوم واحد
    },
  });
}
