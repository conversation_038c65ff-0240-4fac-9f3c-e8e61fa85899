'use client';

import React from 'react';
import Link from 'next/link';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        {/* رقم 404 */}
        <div className="mb-8">
          <h1 className="text-9xl font-black text-gray-300 mb-4 select-none">
            404
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full"></div>
        </div>

        {/* الرسالة */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            الصفحة غير موجودة
          </h2>
          <p className="text-gray-600 text-lg leading-relaxed">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
          </p>
        </div>

        {/* الأزرار */}
        <div className="space-y-4">
          <Link
            href="/"
            className="inline-flex items-center gap-2 bg-gradient-to-r from-primary to-secondary text-white px-8 py-3 rounded-lg font-semibold hover:from-primary/90 hover:to-secondary/90 transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            <i className="ri-home-line text-lg"></i>
            العودة إلى الصفحة الرئيسية
          </Link>
          
          <div className="text-center">
            <button
              onClick={() => window.history.back()}
              className="inline-flex items-center gap-2 text-gray-600 hover:text-primary transition-colors duration-200"
            >
              <i className="ri-arrow-left-line text-lg"></i>
              العودة إلى الصفحة السابقة
            </button>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            إذا كنت تعتقد أن هذا خطأ، يرجى{' '}
            <Link href="/contact" className="text-primary hover:underline">
              التواصل معنا
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
