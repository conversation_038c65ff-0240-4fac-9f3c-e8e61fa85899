# دليل إعداد HTTPS لموقع دروب هجر

## 🚨 المشكلة الحالية

Google Search Console يُظهر الخطأ التالي:
```
الأسباب المؤدّية إلى عدم عرض الصفحات عبر بروتوكول HTTPS
يجب عرض الصفحة عبر بروتوكول HTTPS لتكون مؤهّلة للحصول على تجربة صفحة "جيّدة".
```

## 🎯 الحل المطلوب

تفعيل HTTPS على الموقع بالكامل مع إعادة التوجيه الإجباري من HTTP إلى HTTPS.

## 📋 الخطوات المطلوبة

### الخطوة 1: الحصول على شهادة SSL

#### أ) باستخدام Let's Encrypt (مجاني):
```bash
# تثبيت Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# الحصول على الشهادة
sudo certbot --nginx -d droobhajer.com -d www.droobhajer.com

# تجديد تلقائي
sudo crontab -e
# أضف هذا السطر:
0 12 * * * /usr/bin/certbot renew --quiet
```

#### ب) من خلال مزود الاستضافة (Hostinger):
1. ادخل إلى لوحة التحكم
2. اذهب إلى SSL Certificates
3. فعّل SSL للدومين
4. انتظر حتى يتم التفعيل (5-10 دقائق)

### الخطوة 2: تحديث إعدادات Nginx

استخدم الملف المُحدث `nginx.conf` الذي يحتوي على:

```nginx
# إعادة توجيه HTTP إلى HTTPS
server {
    listen 80;
    server_name droobhajer.com www.droobhajer.com;
    return 301 https://$server_name$request_uri;
}

# إعدادات HTTPS
server {
    listen 443 ssl http2;
    server_name droobhajer.com www.droobhajer.com;
    
    # مسارات الشهادات (يجب تحديثها)
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # باقي الإعدادات...
}
```

### الخطوة 3: تحديث إعدادات Apache (.htaccess)

تم تحديث الملف `.htaccess` ليتضمن:

```apache
# إجبار استخدام HTTPS
RewriteCond %{HTTPS} off
RewriteCond %{HTTP:X-Forwarded-Proto} !https
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# HSTS Header
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
```

### الخطوة 4: تحديث Next.js

تم تحديث `next.config.js` لإضافة headers الأمان:

```javascript
{
  key: 'Strict-Transport-Security',
  value: 'max-age=31536000; includeSubDomains; preload',
},
{
  key: 'Content-Security-Policy',
  value: "upgrade-insecure-requests;",
}
```

## 🔧 الملفات المُحدثة

1. **`.htaccess`** - إعادة توجيه HTTP إلى HTTPS
2. **`nginx.conf`** - إعدادات SSL/TLS
3. **`next.config.js`** - Headers الأمان
4. **`public/https-test.html`** - صفحة اختبار HTTPS

## ✅ خطوات التحقق

### 1. اختبار محلي:
```bash
# اختبار إعادة التوجيه
curl -I http://droobhajer.com
# يجب أن يُظهر: Location: https://droobhajer.com

# اختبار HTTPS
curl -I https://droobhajer.com
# يجب أن يُظهر: HTTP/2 200
```

### 2. اختبار عبر المتصفح:
- زيارة: `http://droobhajer.com` (يجب إعادة التوجيه إلى HTTPS)
- زيارة: `https://droobhajer.com/https-test.html`

### 3. أدوات التحقق الخارجية:
- [SSL Labs Test](https://www.ssllabs.com/ssltest/)
- [Security Headers](https://securityheaders.com/)
- [Why No Padlock](https://www.whynopadlock.com/)

## 🚀 خطوات التطبيق على Hostinger

### 1. تفعيل SSL في لوحة التحكم:
```
1. ادخل إلى hPanel
2. اذهب إلى SSL Certificates
3. اختر "Manage SSL"
4. فعّل "Force HTTPS"
```

### 2. تحديث ملفات الإعدادات:
```bash
# رفع الملفات المُحدثة
scp .htaccess user@server:/var/www/html/
scp nginx.conf user@server:/etc/nginx/sites-available/droobhajer
```

### 3. إعادة تشغيل الخدمات:
```bash
sudo nginx -t
sudo systemctl reload nginx
sudo systemctl restart nginx
```

## 📊 النتائج المتوقعة

بعد تطبيق هذه الإعدادات:

1. ✅ جميع طلبات HTTP ستُعاد توجيهها إلى HTTPS
2. ✅ Google Search Console لن يُظهر خطأ HTTPS
3. ✅ تحسن في تقييم Core Web Vitals
4. ✅ ظهور قفل الأمان في المتصفح
5. ✅ تحسن في ترتيب محركات البحث

## 🔍 استكشاف الأخطاء

### إذا لم يعمل HTTPS:
1. **تحقق من الشهادة:**
   ```bash
   openssl s_client -connect droobhajer.com:443
   ```

2. **تحقق من إعدادات DNS:**
   ```bash
   nslookup droobhajer.com
   ```

3. **تحقق من Nginx logs:**
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

### إذا ظهرت أخطاء Mixed Content:
1. تحقق من جميع الروابط الداخلية
2. استخدم HTTPS في جميع المراجع الخارجية
3. أضف `upgrade-insecure-requests` في CSP

## 🔧 أدوات الفحص والاختبار

### 1. سكريبت الفحص التلقائي:
```bash
# تشغيل فحص شامل لـ HTTPS
./check-https.sh
```

### 2. صفحات الاختبار:
- `/https-test.html` - اختبار HTTPS تفاعلي
- `/icon-test.html` - اختبار الأيقونات
- `/google-verification.html` - دليل Google Search Console

### 3. أوامر الفحص اليدوي:
```bash
# فحص إعادة التوجيه
curl -I http://droobhajer.com

# فحص HTTPS
curl -I https://droobhajer.com

# فحص شهادة SSL
openssl s_client -connect droobhajer.com:443 -servername droobhajer.com
```

## 📊 قائمة التحقق النهائية

- [ ] تفعيل SSL في لوحة تحكم Hostinger
- [ ] تحديث جميع URLs إلى HTTPS
- [ ] إضافة إعادة التوجيه من HTTP إلى HTTPS
- [ ] تفعيل HSTS Headers
- [ ] إضافة Security Headers
- [ ] اختبار الموقع عبر HTTPS
- [ ] فحص Mixed Content
- [ ] تحديث Google Search Console
- [ ] اختبار الأيقونات
- [ ] فحص Core Web Vitals

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من صفحة الاختبار: `/https-test.html`
2. شغّل سكريبت الفحص: `./check-https.sh`
3. راجع logs الخادم
4. تواصل مع دعم Hostinger لتفعيل SSL

---

**تاريخ الإنشاء**: 2025-07-01
**آخر تحديث**: 2025-07-01
**الحالة**: 🔄 في انتظار التطبيق
**الأولوية**: 🔴 عالية جداً
