'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '../lib/i18n';
import MobileCategoriesPage from './mobile/MobileCategoriesPage';
import Navbar from './Navbar';
import Footer from './Footer';
import CategoriesPage from './CategoriesPage';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات غير الضرورية للتحميل الأولي
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null
});

// أنواع البيانات
interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  product_count: number;
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  subcategories: Subcategory[];
}

interface ResponsiveCategoriesPageProps {
  locale: Locale;
  categories?: Category[];
}

const ResponsiveCategoriesPage: React.FC<ResponsiveCategoriesPageProps> = ({
  locale,
  categories
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
      const isSmallScreen = window.innerWidth <= 768;

      setIsMobile(isMobileDevice || isSmallScreen);
      setIsLoading(false);
    };

    checkDevice();

    const handleResize = () => {
      const isSmallScreen = window.innerWidth <= 768;
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

      setIsMobile(isMobileDevice || isSmallScreen);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">
            {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (isMobile) {
    return (
      <MobileCategoriesPage
        locale={locale}
        categories={categories}
      />
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main>
        <CategoriesPage locale={locale} />
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
};

export default ResponsiveCategoriesPage;
