# إدارة Sitemap للموقع

## 📋 نظرة عامة
هذا الدليل يشرح كيفية إدارة وصيانة ملفات sitemap للموقع بعد حل مشكلة Google Search Console.

## 🔧 الملفات المهمة

### 1. ملف الإنشاء الرئيسي
- **المسار**: `/scripts/generate-sitemap.cjs`
- **الوظيفة**: إنشاء sitemap.xml و sitemap-index.xml و robots.txt
- **التحسينات**: يشمل XML Schema validation و namespace declarations

### 2. ملفات الإخراج
- **sitemap.xml**: الملف الرئيسي للفهرسة
- **sitemap-index.xml**: ملف فهرس للـ sitemaps
- **robots.txt**: ملف توجيهات محركات البحث

### 3. أدوات المراقبة
- **monitor-sitemap.js**: مراقبة حالة sitemap
- **test-sitemap.js**: اختبار الوصول والصحة

## 🚀 الاستخدام

### إنشاء sitemap جديد
```bash
# إنشاء sitemap مع البيانات الحديثة من قاعدة البيانات
node scripts/generate-sitemap.cjs
```

### مراقبة الحالة
```bash
# فحص سريع
node monitor-sitemap.js

# مراقبة مستمرة
node monitor-sitemap.js --monitor --interval 30
```

### اختبار مفصل
```bash
node test-sitemap.js
```

## 📊 ما يتم تضمينه في Sitemap

### الصفحات الثابتة (10 URLs)
- الصفحة الرئيسية (ar/en)
- صفحة المنتجات (ar/en)
- صفحة الفئات (ar/en)
- صفحة من نحن (ar/en)
- صفحة اتصل بنا (ar/en)

### المحتوى الديناميكي
- **المنتجات**: من جدول `products` (نشط وغير محذوف)
- **الفئات**: من جدول `categories` (نشط وغير محذوف)
- **الفئات الفرعية**: من جدول `subcategories` (نشط وغير محذوف)

## ⚙️ الإعدادات

### أولويات الصفحات
- **الصفحة الرئيسية**: 1.0
- **صفحة المنتجات**: 0.9
- **صفحة الفئات**: 0.8
- **المنتجات الفردية**: 0.8
- **الفئات الفردية**: 0.7
- **الفئات الفرعية**: 0.6
- **الصفحات الأخرى**: 0.6

### تكرار التحديث
- **الصفحات الرئيسية**: يومياً
- **المنتجات والفئات**: أسبوعياً
- **الصفحات الثابتة**: شهرياً

## 🔄 التحديث التلقائي

### إضافة إلى cron job
```bash
# تحديث sitemap كل يوم في الساعة 2:00 صباحاً
0 2 * * * cd /var/www/html && node scripts/generate-sitemap.cjs
```

### إضافة إلى package.json
```json
{
  "scripts": {
    "sitemap": "node scripts/generate-sitemap.cjs",
    "sitemap:monitor": "node monitor-sitemap.js",
    "sitemap:test": "node test-sitemap.js"
  }
}
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في قاعدة البيانات**: تحقق من متغيرات .env.local
2. **ملف غير صالح**: تحقق من صحة XML
3. **مشاكل الوصول**: تحقق من إعدادات الخادم

### أوامر التشخيص
```bash
# فحص حالة قاعدة البيانات
node -e "require('./scripts/generate-sitemap.cjs')"

# فحص صحة XML
curl -s https://droobhajer.com/sitemap.xml | head -10

# فحص headers
curl -I https://droobhajer.com/sitemap.xml
```

## 📈 أفضل الممارسات

1. **تحديث منتظم**: قم بتحديث sitemap عند إضافة محتوى جديد
2. **مراقبة مستمرة**: استخدم أدوات المراقبة المتوفرة
3. **نسخ احتياطية**: احتفظ بنسخ من إعدادات sitemap
4. **اختبار دوري**: اختبر sitemap بعد كل تحديث

## 🔗 روابط مفيدة
- [Google Search Console](https://search.google.com/search-console)
- [Sitemap Protocol](https://www.sitemaps.org/protocol.html)
- [Google Sitemap Guidelines](https://developers.google.com/search/docs/crawling-indexing/sitemaps/overview)
