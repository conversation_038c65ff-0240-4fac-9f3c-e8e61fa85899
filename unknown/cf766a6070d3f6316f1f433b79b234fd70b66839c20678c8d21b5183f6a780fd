<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حل مشكلة Google Search Console - دروب هجر</title>
    
    <!-- الأيقونات المفضلة -->
    <link rel="icon" type="image/png" sizes="16x16" href="/icons8-circled-d-ios-17-filled-16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons8-circled-d-ios-17-filled-32.png">
    <link rel="shortcut icon" href="/icons8-circled-d-ios-17-filled-32.png">
    
    <!-- Meta tags محسنة -->
    <meta name="description" content="دليل حل مشكلة عدم فهرسة الصفحات في Google Search Console">
    <meta name="robots" content="index, follow">
    
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 1000px;
            margin: 0 auto;
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            display: block;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        h1 {
            color: #3B82F6;
            margin: 0;
            font-size: 2.5em;
        }
        .problem-box {
            background: #fff3cd;
            border: 2px solid #ffc107;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            color: #856404;
        }
        .solution-box {
            background: #d4edda;
            border: 2px solid #28a745;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            color: #155724;
        }
        .steps {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #3B82F6;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-weight: bold;
        }
        .url-list {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 5px solid #2196f3;
        }
        .url-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3B82F6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px 5px;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.warning {
            background: #ffc107;
            color: #333;
        }
        .checklist {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .checklist-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            display: flex;
            align-items: center;
        }
        .checklist-item input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="/icons8-circled-d-ios-17-filled-32.png" alt="شعار دروب هجر" class="logo">
            <h1>حل مشكلة Google Search Console</h1>
            <p>دليل شامل لحل مشكلة عدم فهرسة الصفحات</p>
        </div>
        
        <div class="problem-box">
            <h3>🚨 المشكلة المُكتشفة</h3>
            <p><strong>Google Search Console يُظهر:</strong></p>
            <ul>
                <li>عنوان URL غير متوفّر على محرّك بحث Google</li>
                <li>لم تتم فهرسة هذه الصفحة</li>
                <li>لم يتم اكتشاف أي خرائط موقع للإحالة</li>
                <li>البحث عن www.droobhajer.com بينما الموقع على droobhajer.com</li>
            </ul>
        </div>
        
        <div class="solution-box">
            <h3>✅ الحل المُطبق</h3>
            <p>تم إنشاء حلول متعددة لضمان الفهرسة الصحيحة:</p>
            <ul>
                <li>إنشاء sitemap منفصل للـ www subdomain</li>
                <li>إضافة الصفحة الرئيسية إلى sitemap</li>
                <li>تحديث robots.txt مع جميع sitemaps</li>
                <li>إعداد إعادة التوجيه الصحيحة</li>
            </ul>
        </div>
        
        <div class="url-list">
            <h3>🔗 الملفات الجديدة المُنشأة</h3>
            <div class="url-item">
                <strong>Sitemap الرئيسي:</strong> 
                <a href="/sitemap.xml" target="_blank">https://droobhajer.com/sitemap.xml</a>
            </div>
            <div class="url-item">
                <strong>Sitemap للـ www:</strong> 
                <a href="/sitemap-www.xml" target="_blank">https://droobhajer.com/sitemap-www.xml</a>
            </div>
            <div class="url-item">
                <strong>Sitemap Index:</strong> 
                <a href="/sitemap-index.xml" target="_blank">https://droobhajer.com/sitemap-index.xml</a>
            </div>
            <div class="url-item">
                <strong>Robots.txt:</strong> 
                <a href="/robots.txt" target="_blank">https://droobhajer.com/robots.txt</a>
            </div>
        </div>
        
        <div class="steps">
            <h2>خطوات حل المشكلة في Google Search Console:</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>إضافة Property جديد</strong>
                <p>أضف droobhajer.com (بدون www) كـ property منفصل في Google Search Console</p>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>رفع Sitemaps الجديدة</strong>
                <p>في كلا الـ properties، أضف:</p>
                <ul>
                    <li>sitemap.xml</li>
                    <li>sitemap-www.xml</li>
                    <li>sitemap-index.xml</li>
                </ul>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>طلب إعادة الفهرسة</strong>
                <p>استخدم أداة URL Inspection لطلب فهرسة الصفحات المهمة</p>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>تحديد الدومين المفضل</strong>
                <p>في إعدادات Search Console، حدد droobhajer.com كدومين مفضل</p>
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <strong>مراقبة النتائج</strong>
                <p>انتظر 24-48 ساعة ومراقبة تقرير Coverage</p>
            </div>
        </div>
        
        <div class="checklist">
            <h3>📋 قائمة التحقق</h3>
            <div class="checklist-item">
                <input type="checkbox" id="check1">
                <label for="check1">تم إنشاء sitemap.xml مع الصفحة الرئيسية</label>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check2">
                <label for="check2">تم إنشاء sitemap-www.xml للـ www subdomain</label>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check3">
                <label for="check3">تم تحديث robots.txt مع جميع sitemaps</label>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check4">
                <label for="check4">تم إعداد إعادة التوجيه من www إلى non-www</label>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check5">
                <label for="check5">تم إضافة property جديد في Search Console</label>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check6">
                <label for="check6">تم رفع sitemaps في Search Console</label>
            </div>
            <div class="checklist-item">
                <input type="checkbox" id="check7">
                <label for="check7">تم طلب إعادة فهرسة الصفحات المهمة</label>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/sitemap.xml" class="btn success">عرض Sitemap الرئيسي</a>
            <a href="/sitemap-www.xml" class="btn success">عرض Sitemap للـ www</a>
            <a href="/robots.txt" class="btn">عرض Robots.txt</a>
            <a href="/" class="btn">العودة للموقع</a>
        </div>
        
        <div class="solution-box" style="margin-top: 30px;">
            <h3>📈 النتائج المتوقعة</h3>
            <p>بعد تطبيق هذه الحلول:</p>
            <ul>
                <li>✅ ستظهر جميع الصفحات في Google Search Console</li>
                <li>✅ ستتم فهرسة الصفحة الرئيسية والصفحات الفرعية</li>
                <li>✅ ستحل مشكلة "عنوان URL غير متوفّر"</li>
                <li>✅ ستتحسن رؤية الموقع في محركات البحث</li>
            </ul>
        </div>
    </div>
    
    <script>
        // حفظ حالة checkboxes في localStorage
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            const savedState = localStorage.getItem(checkbox.id);
            if (savedState === 'true') {
                checkbox.checked = true;
            }
            
            checkbox.addEventListener('change', function() {
                localStorage.setItem(this.id, this.checked);
            });
        });
    </script>
</body>
</html>
