# دليل استخدام Next-SEO في مشروع دروب هجر

## نظرة عامة

تم تطبيق `next-seo` في المشروع لتحسين محركات البحث (SEO) بطريقة احترافية ومرنة. هذا الدليل يوضح كيفية استخدام المكونات المختلفة.

## الملفات الرئيسية

### 1. إعدادات SEO العامة
- `lib/seo.config.ts` - الإعدادات العامة والخاصة بكل صفحة
- `components/SEO/PageSEO.tsx` - مكونات SEO للصفحات
- `components/SEO/JsonLd.tsx` - مكونات البيانات المنظمة

### 2. التطبيق في الصفحات
- `app/layout.tsx` - الإعدادات العامة للموقع
- `app/[locale]/page.tsx` - الصفحة الرئيسية
- `app/[locale]/products/page.tsx` - صفحة المنتجات
- `app/[locale]/categories/page.tsx` - صفحة الفئات

## كيفية الاستخدام

### 1. للصفحات العادية

```tsx
import PageSEO from '../../../components/SEO/PageSEO';
import { WebPageJsonLdComponent } from '../../../components/SEO/JsonLd';

export default function MyPage({ params }) {
  const { locale } = params;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return (
    <>
      <PageSEO 
        locale={locale} 
        page="home" // أو "products" أو "categories" أو "about" أو "contact" أو "cart"
        customData={{
          title: "عنوان مخصص", // اختياري
          description: "وصف مخصص", // اختياري
          canonical: `${baseUrl}/${locale}/my-page`,
          keywords: ["كلمة1", "كلمة2"], // اختياري
          noIndex: false, // اختياري
        }}
      />
      
      <WebPageJsonLdComponent 
        locale={locale}
        title="عنوان الصفحة"
        description="وصف الصفحة"
        url={`${baseUrl}/${locale}/my-page`}
        breadcrumbs={[
          { name: "الرئيسية", url: `${baseUrl}/${locale}` },
          { name: "صفحتي", url: `${baseUrl}/${locale}/my-page` },
        ]}
      />
      
      {/* محتوى الصفحة */}
    </>
  );
}
```

### 2. لصفحات المنتجات

```tsx
import { ProductSEO } from '../../../components/SEO/PageSEO';
import { ProductJsonLdComponent } from '../../../components/SEO/JsonLd';

export default function ProductPage({ product, locale }) {
  return (
    <>
      <ProductSEO 
        locale={locale} 
        product={{
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          image: product.image,
          category: product.category,
        }}
      />
      
      <ProductJsonLdComponent 
        locale={locale}
        product={{
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          currency: 'SAR',
          image: product.image,
          brand: 'دروب هجر',
          category: product.category,
          sku: product.sku,
          availability: 'in stock',
          condition: 'new',
          rating: {
            value: 4.5,
            count: 10
          }
        }}
      />
      
      {/* محتوى صفحة المنتج */}
    </>
  );
}
```

### 3. لصفحات الفئات

```tsx
import { CategorySEO } from '../../../components/SEO/PageSEO';
import { CategoryJsonLdComponent } from '../../../components/SEO/JsonLd';

export default function CategoryPage({ category, locale }) {
  return (
    <>
      <CategorySEO 
        locale={locale} 
        category={{
          id: category.id,
          name: category.name,
          description: category.description,
          image: category.image,
          productsCount: category.productsCount,
        }}
      />
      
      <CategoryJsonLdComponent 
        locale={locale}
        category={category}
      />
      
      {/* محتوى صفحة الفئة */}
    </>
  );
}
```

### 4. للأسئلة الشائعة (FAQ)

```tsx
import { FAQJsonLdComponent } from '../../../components/SEO/JsonLd';

export default function FAQPage({ locale }) {
  const faqs = [
    {
      question: "ما هي مدة الشحن؟",
      answer: "نقوم بالشحن خلال 2-3 أيام عمل داخل المملكة."
    },
    // المزيد من الأسئلة...
  ];

  return (
    <>
      <FAQJsonLdComponent locale={locale} faqs={faqs} />
      {/* محتوى صفحة الأسئلة الشائعة */}
    </>
  );
}
```

## المميزات المطبقة

### 1. SEO متعدد اللغات
- دعم كامل للعربية والإنجليزية
- `hreflang` tags للغات البديلة
- محتوى مخصص لكل لغة

### 2. البيانات المنظمة (Structured Data)
- Organization Schema للشركة
- Product Schema للمنتجات
- WebPage Schema للصفحات
- Breadcrumb Schema للتنقل
- FAQ Schema للأسئلة الشائعة

### 3. Open Graph و Twitter Cards
- صور مخصصة لكل صفحة
- عناوين ووصف محسنة
- دعم كامل لمشاركة وسائل التواصل

### 4. تحسينات تقنية
- Canonical URLs
- Meta robots tags
- Viewport و theme-color
- Apple touch icons

## إعدادات مهمة

### متغيرات البيئة
```env
NEXT_PUBLIC_BASE_URL=https://droobhajer.com
```

### الصور المطلوبة
- `/images/logo.png` - شعار الشركة
- `/images/og-default.jpg` - صورة افتراضية للمشاركة
- `/images/og-home.jpg` - صورة الصفحة الرئيسية
- `/images/og-products.jpg` - صورة صفحة المنتجات
- `/images/og-categories.jpg` - صورة صفحة الفئات

## نصائح للاستخدام الأمثل

1. **استخدم عناوين وصفية**: تأكد من أن العناوين واضحة ومفيدة
2. **اكتب وصف جذاب**: الوصف يظهر في نتائج البحث
3. **استخدم الكلمات المفتاحية بذكاء**: لا تفرط في استخدامها
4. **حدث الصور**: تأكد من وجود صور عالية الجودة للمشاركة
5. **اختبر النتائج**: استخدم أدوات Google للتحقق من البيانات المنظمة

## أدوات الاختبار

- [Google Rich Results Test](https://search.google.com/test/rich-results)
- [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/)
- [Twitter Card Validator](https://cards-dev.twitter.com/validator)
- [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)

## الصيانة

- راجع وحدث المحتوى بانتظام
- تأكد من صحة الروابط الداخلية
- راقب أداء الصفحات في Google Search Console
- حدث البيانات المنظمة عند إضافة منتجات جديدة
