'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface AdminProtectionProps {
  children: React.ReactNode;
}

const AdminProtection: React.FC<AdminProtectionProps> = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // دالة التحقق من المصادقة
  const checkAuthentication = useCallback((): boolean => {
    if (typeof window === 'undefined') return false;

    try {
      // التحقق من التوكن في localStorage
      const localToken = localStorage.getItem('authToken');

      // التحقق من التوكن في الكوكيز
      const cookieToken = document.cookie
        .split('; ')
        .find(row => row.startsWith('authToken='))
        ?.split('=')[1];

      const hasToken = !!(localToken || cookieToken);

      console.log('🔐 Authentication check:', {
        pathname,
        localToken: !!localToken,
        cookieToken: !!cookieToken,
        hasToken
      });

      return hasToken;
    } catch (error) {
      console.error('❌ Authentication check error:', error);
      return false;
    }
  }, [pathname]);

  // دالة إظهار صفحة 404
  const show404 = () => {
    // إزالة أي توكنات موجودة
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authToken');
      document.cookie = 'authToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    }

    // إظهار صفحة 404 مخصصة
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="text-6xl font-bold text-gray-400 mb-4">404</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">الصفحة غير موجودة</h1>
          <p className="text-gray-600 mb-6">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.
          </p>
          <button
            onClick={() => router.push('/')}
            className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
          >
            العودة إلى الصفحة الرئيسية
          </button>
        </div>
      </div>
    );
  };

  useEffect(() => {
    // التحقق من أن المسار يبدأ بـ /admin وليس صفحة تسجيل الدخول
    if (pathname.startsWith('/admin') && pathname !== '/admin/login') {
      const isAuth = checkAuthentication();
      setIsAuthenticated(isAuth);
      
      if (!isAuth) {
        console.log('🚫 Unauthorized access blocked for:', pathname);
        // لا نقوم بإعادة التوجيه، بل نظهر 404
      } else {
        console.log('✅ Access granted for:', pathname);
      }
    } else {
      // إذا لم يكن مسار إدارة محمي، السماح بالوصول
      setIsAuthenticated(true);
    }
    
    setIsLoading(false);
  }, [pathname, router, checkAuthentication]);

  // إذا كان التحميل جارياً
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    );
  }

  // إذا لم يكن مصادق عليه وهو في صفحة محمية
  if (pathname.startsWith('/admin') && pathname !== '/admin/login' && !isAuthenticated) {
    return show404();
  }

  // إذا كان مصادق عليه أو في صفحة غير محمية
  return <>{children}</>;
};

export default AdminProtection;
