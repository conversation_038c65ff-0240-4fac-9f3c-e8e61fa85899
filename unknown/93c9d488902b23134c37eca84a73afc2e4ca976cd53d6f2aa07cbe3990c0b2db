# مكون الصفحة الرئيسية للهواتف المحمولة (Mobile Home Page)

## نظرة عامة

تم إنشاء مكون مستقل للصفحة الرئيسية مخصص للهواتف المحمولة بتصميم يشبه تطبيقات المتاجر الأصلية (Native Mobile Apps). يوفر هذا المكون تجربة مستخدم محسنة للأجهزة المحمولة مع تفاعل سلس ومناسب للمس.

## الملفات المضافة

### 1. `components/MobileHomePage.tsx`
المكون الرئيسي للصفحة الرئيسية المحمولة يتضمن:

#### الميزات الرئيسية:
- **رأس التطبيق المحمول**: شعار الشركة، بحث، وسلة التسوق مع عداد
- **بانر متحرك**: عرض الصور الترويجية مع تبديل تلقائي
- **أزرار الإجراءات السريعة**: وصول سريع للفئات، المنتجات، اتصل بنا، ومن نحن
- **قسم الفئات**: عرض الفئات في شبكة 2×3 مع صور
- **المنتجات المميزة**: قائمة عمودية مع إمكانية إضافة للسلة مباشرة
- **شريط التنقل السفلي**: تنقل ثابت في الأسفل مع 5 أقسام رئيسية

#### التفاعل والأنيميشن:
- تأثيرات اللمس (Touch feedback) مع `active:scale-95`
- انتقالات سلسة بين الأقسام
- أنيميشن للبانر والعناصر
- إشعارات Toast للتفاعل مع المستخدم

### 2. `components/ResponsiveHomePage.tsx`
مكون wrapper للكشف عن نوع الجهاز وعرض النسخة المناسبة:

#### الوظائف:
- **كشف الجهاز**: تحديد ما إذا كان الجهاز محمول أو ديسكتوب
- **عرض تكيفي**: عرض `MobileHomePage` للأجهزة المحمولة أو النسخة العادية للديسكتوب
- **مراقبة تغيير الحجم**: تحديث العرض عند تغيير حجم الشاشة

### 3. `components/MobileToast.tsx`
مكون الإشعارات للهواتف المحمولة:

#### الميزات:
- إشعارات نجاح/خطأ/معلومات
- إغلاق تلقائي بعد مدة محددة
- تصميم مناسب للهواتف المحمولة
- أيقونات مختلفة حسب نوع الإشعار

## التحديثات على الملفات الموجودة

### 1. `app/[locale]/page.tsx`
- تم استبدال المكونات المنفصلة بـ `ResponsiveHomePage`
- إضافة جلب البيانات من الخادم (SSR) للفئات والمنتجات المميزة
- معالجة الأخطاء وتوفير fallback للبيانات

### 2. `app/globals.css`
إضافة أنماط CSS مخصصة للمكون المحمول:

```css
/* Mobile App Styles */
.mobile-app-header { /* رأس التطبيق مع blur effect */ }
.mobile-card-hover { /* تأثيرات اللمس للكروت */ }
.safe-area-pb { /* دعم safe area للأجهزة الحديثة */ }
.line-clamp-1, .line-clamp-2, .line-clamp-3 { /* قطع النص */ }
```

## كيفية الاستخدام

### 1. التشغيل التلقائي
المكون يعمل تلقائياً عند زيارة الصفحة الرئيسية من جهاز محمول أو شاشة صغيرة (≤768px).

### 2. تمرير البيانات
```tsx
<ResponsiveHomePage 
  locale={locale} 
  categories={categories}
  featuredProducts={featuredProducts}
/>
```

### 3. الكشف عن الجهاز
```javascript
const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
const isSmallScreen = window.innerWidth <= 768;
const isMobile = isMobileDevice || isSmallScreen;
```

## الميزات التقنية

### 1. الأداء
- **تحميل ديناميكي**: للمكونات غير الضرورية
- **تخزين مؤقت**: للبيانات مع إعادة التحقق كل 15 دقيقة
- **تحسين الصور**: مع Next.js Image component

### 2. إدارة الحالة
- **localStorage**: لسلة التسوق
- **React State**: للواجهة والتفاعل
- **Custom Events**: لتحديث عداد السلة

### 3. إمكانية الوصول
- **Touch targets**: حد أدنى 44px للأزرار
- **Safe area**: دعم الأجهزة الحديثة مع notch
- **RTL/LTR**: دعم كامل للغتين العربية والإنجليزية

## التخصيص

### 1. الألوان والأنماط
يمكن تخصيص الألوان من خلال متغيرات Tailwind في `tailwind.config.js`:

```javascript
colors: {
  primary: "#2D3748",
  secondary: "#4A5568",
}
```

### 2. المحتوى
- **البانر**: يتم جلبه من إعدادات الموقع
- **الفئات والمنتجات**: من قاعدة البيانات
- **النصوص**: من ملفات الترجمة

### 3. التخطيط
يمكن تعديل تخطيط الشبكة والمسافات من خلال فئات Tailwind CSS.

## الاختبار

### 1. اختبار الاستجابة
- افتح الموقع على أجهزة مختلفة
- استخدم أدوات المطور لمحاكاة الأجهزة المحمولة
- اختبر تغيير حجم الشاشة

### 2. اختبار الوظائف
- إضافة المنتجات للسلة
- البحث عن المنتجات
- التنقل بين الأقسام
- عرض الإشعارات

## المتطلبات

- Next.js 15+
- React 18+
- Tailwind CSS
- TypeScript
- Remix Icons

## الدعم

المكون يدعم:
- جميع المتصفحات الحديثة
- iOS Safari و Android Chrome
- أجهزة مختلفة الأحجام
- اللغتين العربية والإنجليزية
