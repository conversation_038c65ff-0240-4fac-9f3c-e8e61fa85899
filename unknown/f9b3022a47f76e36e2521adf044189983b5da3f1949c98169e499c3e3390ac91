import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// أنواع الملفات المسموحة
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
];

// امتدادات الملفات المسموحة
const ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];

// الحد الأقصى لحجم الملف (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// مسار التخزين الآمن (خارج public)
const SECURE_UPLOADS_DIR = path.join(process.cwd(), 'secure-uploads');

// إنشاء مجلد التخزين الآمن
export function ensureSecureUploadsDir(): void {
  if (!fs.existsSync(SECURE_UPLOADS_DIR)) {
    fs.mkdirSync(SECURE_UPLOADS_DIR, { recursive: true });
  }
}

// فحص نوع الملف بعمق
export function validateFileType(filePath: string, originalMimeType: string): boolean {
  try {
    // قراءة أول بايتات من الملف للتحقق من النوع الحقيقي
    const buffer = fs.readFileSync(filePath);
    
    // فحص Magic Numbers للصور
    const magicNumbers = {
      'image/jpeg': [0xFF, 0xD8, 0xFF],
      'image/png': [0x89, 0x50, 0x4E, 0x47],
      'image/gif': [0x47, 0x49, 0x46],
      'image/webp': [0x52, 0x49, 0x46, 0x46] // RIFF header for WebP
    };
    
    // التحقق من Magic Number
    for (const [mimeType, magic] of Object.entries(magicNumbers)) {
      if (originalMimeType === mimeType) {
        const matches = magic.every((byte, index) => buffer[index] === byte);
        if (matches) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error validating file type:', error);
    return false;
  }
}

// فحص حجم الملف
export function validateFileSize(filePath: string): boolean {
  try {
    const stats = fs.statSync(filePath);
    return stats.size <= MAX_FILE_SIZE;
  } catch (error) {
    console.error('Error validating file size:', error);
    return false;
  }
}

// فحص امتداد الملف
export function validateFileExtension(filename: string): boolean {
  const ext = path.extname(filename).toLowerCase();
  return ALLOWED_EXTENSIONS.includes(ext);
}

// فحص MIME type
export function validateMimeType(mimeType: string): boolean {
  return ALLOWED_MIME_TYPES.includes(mimeType);
}

// إنشاء اسم ملف آمن وفريد
export function generateSecureFilename(originalFilename: string): string {
  const ext = path.extname(originalFilename).toLowerCase();
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(16).toString('hex');
  return `${timestamp}_${randomBytes}${ext}`;
}

// حفظ الملف بشكل آمن
export function saveFileSecurely(tempPath: string, originalFilename: string, mimeType: string): string | null {
  try {
    ensureSecureUploadsDir();
    
    // التحقق من جميع شروط الأمان
    if (!validateMimeType(mimeType)) {
      fs.unlinkSync(tempPath); // حذف الملف المؤقت
      throw new Error('Invalid MIME type');
    }
    
    if (!validateFileExtension(originalFilename)) {
      fs.unlinkSync(tempPath);
      throw new Error('Invalid file extension');
    }
    
    if (!validateFileSize(tempPath)) {
      fs.unlinkSync(tempPath);
      throw new Error('File too large');
    }
    
    if (!validateFileType(tempPath, mimeType)) {
      fs.unlinkSync(tempPath);
      throw new Error('File type validation failed');
    }
    
    // إنشاء اسم ملف آمن
    const secureFilename = generateSecureFilename(originalFilename);
    const securePath = path.join(SECURE_UPLOADS_DIR, secureFilename);
    
    // نقل الملف إلى المكان الآمن
    fs.renameSync(tempPath, securePath);
    
    return secureFilename;
  } catch (error) {
    console.error('Error saving file securely:', error);
    // تنظيف الملف المؤقت في حالة الخطأ
    try {
      if (fs.existsSync(tempPath)) {
        fs.unlinkSync(tempPath);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up temp file:', cleanupError);
    }
    return null;
  }
}

// قراءة الملف الآمن
export function readSecureFile(filename: string): Buffer | null {
  try {
    const filePath = path.join(SECURE_UPLOADS_DIR, filename);
    
    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    // التحقق من أن الملف داخل المجلد الآمن (منع Path Traversal)
    const resolvedPath = path.resolve(filePath);
    const resolvedSecureDir = path.resolve(SECURE_UPLOADS_DIR);
    
    if (!resolvedPath.startsWith(resolvedSecureDir)) {
      throw new Error('Path traversal attempt detected');
    }
    
    return fs.readFileSync(filePath);
  } catch (error) {
    console.error('Error reading secure file:', error);
    return null;
  }
}

// حذف الملف الآمن
export function deleteSecureFile(filename: string): boolean {
  try {
    const filePath = path.join(SECURE_UPLOADS_DIR, filename);
    
    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return false;
    }
    
    // التحقق من أن الملف داخل المجلد الآمن
    const resolvedPath = path.resolve(filePath);
    const resolvedSecureDir = path.resolve(SECURE_UPLOADS_DIR);
    
    if (!resolvedPath.startsWith(resolvedSecureDir)) {
      throw new Error('Path traversal attempt detected');
    }
    
    fs.unlinkSync(filePath);
    return true;
  } catch (error) {
    console.error('Error deleting secure file:', error);
    return false;
  }
}

// الحصول على معلومات الملف
export function getSecureFileInfo(filename: string): { size: number; mtime: Date } | null {
  try {
    const filePath = path.join(SECURE_UPLOADS_DIR, filename);
    
    if (!fs.existsSync(filePath)) {
      return null;
    }
    
    const stats = fs.statSync(filePath);
    return {
      size: stats.size,
      mtime: stats.mtime
    };
  } catch (error) {
    console.error('Error getting file info:', error);
    return null;
  }
}
