import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 بدء رفع الصورة...');

    // التحقق من المصادقة (اختياري - يمكن تعطيله للاختبار)
    // const token = extractToken(request);
    // if (!token) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    // const decoded = verifyToken(token);
    // if (!decoded) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Invalid token',
    //     messageAr: 'رمز المصادقة غير صحيح'
    //   }, { status: 401 });
    // }

    const formData = await request.formData();
    console.log('📦 FormData keys:', Array.from(formData.keys()));

    // البحث عن الملف بأسماء مختلفة
    const file = (formData.get('file') || formData.get('image')) as File;
    console.log('📁 File found:', !!file, file?.name, file?.type, file?.size);

    if (!file) {
      console.log('❌ لم يتم العثور على ملف');
      return NextResponse.json({
        success: false,
        message: 'No file provided',
        messageAr: 'لم يتم توفير ملف'
      }, { status: 400 });
    }

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    console.log('🔍 فحص نوع الملف:', file.type, 'مسموح:', allowedTypes.includes(file.type));

    if (!allowedTypes.includes(file.type)) {
      console.log('❌ نوع ملف غير مدعوم:', file.type);
      return NextResponse.json({
        success: false,
        message: 'Invalid file type. Only images are allowed.',
        messageAr: 'نوع ملف غير صحيح. الصور فقط مسموحة.'
      }, { status: 400 });
    }

    // التحقق من حجم الملف (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    console.log('📏 فحص حجم الملف:', file.size, 'bytes, الحد الأقصى:', maxSize, 'bytes');

    if (file.size > maxSize) {
      console.log('❌ الملف كبير جداً:', file.size, '>', maxSize);
      return NextResponse.json({
        success: false,
        message: 'File too large. Maximum size is 10MB.',
        messageAr: 'الملف كبير جداً. الحد الأقصى 10 ميجابايت.'
      }, { status: 400 });
    }

    // إنشاء مجلد uploads إذا لم يكن موجوداً
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('📁 تم إنشاء مجلد uploads');
    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const fileExtension = path.extname(file.name);
    const newFileName = `hero-${timestamp}-${randomString}${fileExtension}`;
    const newFilePath = path.join(uploadsDir, newFileName);

    // حفظ الملف
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    fs.writeFileSync(newFilePath, buffer);

    // إنشاء URL للصورة
    const imageUrl = `/uploads/${newFileName}`;

    console.log('✅ تم رفع صورة الهيرو بنجاح:', newFileName);

    return NextResponse.json({
      success: true,
      message: 'Image uploaded successfully',
      messageAr: 'تم رفع الصورة بنجاح',
      imageUrl,
      fileName: newFileName
    });

  } catch (error: unknown) {
    console.error('❌ خطأ في رفع الصورة:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to upload image',
      messageAr: 'فشل في رفع الصورة',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
