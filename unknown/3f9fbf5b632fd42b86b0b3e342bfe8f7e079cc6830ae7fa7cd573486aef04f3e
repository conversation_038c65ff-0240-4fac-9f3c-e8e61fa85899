import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const width = searchParams.get('width') || '400';
    const height = searchParams.get('height') || '300';
    const text = searchParams.get('text') || 'No Image';

    // فك تشفير النص وتنظيفه
    const decodedText = decodeURIComponent(text).replace(/[<>&"']/g, (match) => {
      const entities: { [key: string]: string } = {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#39;'
      };
      return entities[match];
    });

    // إنشاء SVG placeholder
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <rect x="1" y="1" width="${parseInt(width) - 2}" height="${parseInt(height) - 2}" fill="none" stroke="#d1d5db" stroke-width="2" stroke-dasharray="5,5"/>
        <text x="50%" y="50%" font-family="Arial, Tahoma, sans-serif" font-size="16" fill="#6b7280" text-anchor="middle" dominant-baseline="middle" direction="rtl">
          ${decodedText}
        </text>
      </svg>
    `;

    return new NextResponse(svg, {
      status: 200,
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=3600',
      },
    });

  } catch (error) {
    console.error('Error generating placeholder:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
