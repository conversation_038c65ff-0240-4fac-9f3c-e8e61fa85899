import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { saveFileSecurely } from '../../../lib/secure-upload';
import { checkRateLimit, RATE_LIMIT_CONFIGS } from '../../../lib/rate-limiter';

export async function POST(req: NextRequest) {
  // TODO: إعادة تفعيل المصادقة لاحقاً
  // const user = requireAdminAuth(req);
  // if (!user) {
  //   return NextResponse.json({
  //     success: false,
  //     message: 'Authentication required',
  //     messageAr: 'المصادقة مطلوبة'
  //   }, { status: 401 });
  // }

  // فحص معدل الطلبات لرفع الملفات
  const rateLimitResult = checkRateLimit(req, 'upload', RATE_LIMIT_CONFIGS.UPLOAD);

  if (!rateLimitResult.allowed) {
    return NextResponse.json({
      success: false,
      message: 'Too many upload requests. Please try again later.',
      messageAr: 'طلبات رفع كثيرة جداً. يرجى المحاولة لاحقاً.',
      retryAfter: rateLimitResult.retryAfter
    }, { status: 429 });
  }

  try {
    const formData = await req.formData();
    const uploadedFiles: string[] = [];
    const errors: string[] = [];

    // إنشاء مجلد مؤقت للرفع
    const tempDir = path.join(process.cwd(), 'temp-uploads');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // معالجة الملفات المرفوعة
    for (const [, value] of formData.entries()) {
      if (value instanceof File) {
        try {
          // التحقق من نوع الملف
          if (!value.type.startsWith('image/')) {
            errors.push(`نوع ملف غير مدعوم: ${value.name}`);
            continue;
          }

          // التحقق من حجم الملف (5MB)
          if (value.size > 5 * 1024 * 1024) {
            errors.push(`حجم الملف كبير جداً: ${value.name}`);
            continue;
          }

          // قراءة محتوى الملف
          const bytes = await value.arrayBuffer();
          const buffer = Buffer.from(bytes);

          // إنشاء ملف مؤقت
          const tempFilename = `temp_${Date.now()}_${Math.random().toString(36).substring(2)}`;
          const tempFilePath = path.join(tempDir, tempFilename);

          // كتابة الملف المؤقت
          fs.writeFileSync(tempFilePath, buffer);

          // حفظ الملف بشكل آمن
          const secureFilename = saveFileSecurely(
            tempFilePath,
            value.name,
            value.type
          );

          if (secureFilename) {
            uploadedFiles.push(`/api/files/${secureFilename}`);
          } else {
            errors.push(`فشل في حفظ الملف: ${value.name}`);
          }

          // حذف الملف المؤقت
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
          }

        } catch (error) {
          console.error('Error processing file:', error);
          errors.push(`خطأ في معالجة الملف: ${value.name}`);
        }
      }
    }

    // إرجاع النتائج
    if (uploadedFiles.length === 0 && errors.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'No valid files uploaded',
        errors: errors,
        message: 'فشل في رفع جميع الملفات'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      files: uploadedFiles,
      errors: errors.length > 0 ? errors : undefined,
      message: `تم رفع ${uploadedFiles.length} ملف بنجاح${errors.length > 0 ? ` مع ${errors.length} أخطاء` : ''}`
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to upload files',
      message: 'فشل في رفع الملفات'
    }, { status: 500 });
  }
}
