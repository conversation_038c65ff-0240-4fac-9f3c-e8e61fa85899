-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 28, 2025 at 06:32 PM
-- Server version: 8.0.30
-- PHP Version: 8.1.10
SET foreign_key_checks = 0;

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `droobhajer_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `username`, `email`, `password_hash`, `is_active`, `last_login`, `created_at`, `updated_at`, `deleted_at`) VALUES
(5, 'ahmed', '<EMAIL>', '$2b$12$CnH9TsWVTQEvSC1RlpHyRejhKQJD089/NyOZ8TN3cUkV0rHMc.Cra', 1, '2025-06-28 17:06:44', '2025-06-24 01:27:17', '2025-06-28 17:06:44', NULL),
(6, 'ali', '<EMAIL>', '$2b$12$ofV36stS29oIA.3feIT8EuM8S8z7WDz1TskdT3SX.yDLpujq2rMFG', 0, NULL, '2025-06-28 17:39:06', '2025-06-28 17:39:28', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_ar` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `description_ar` text COLLATE utf8mb4_unicode_ci,
  `image_url` VARCHAR(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `name_ar`, `description`, `description_ar`, `image_url`, `is_active`, `created_at`, `updated_at`, `deleted_at`) VALUES
('cat-bakery', 'Bakeryware', 'أدوات المخبز', 'Tools and accessories tailored for professional hotel bakeries and pastry chefs.', 'أدوات وخبازات مخصصة للمخابز الفندقية ومحترفي صناعة المعجنات.', 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop&auto=format', 1, '2025-06-27 19:20:29', '2025-06-28 17:32:43', NULL),
('cat-banquet', 'Banquet Supplies', 'تجهيزات الولائم', 'Elegant and functional supplies for weddings, corporate events, and hotel banquets.', 'تجهيزات أنيقة وعملية للولائم والمناسبات في الفنادق مثل الأعراس والمؤتمرات.', 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop&auto=format', 1, '2025-06-27 19:20:29', '2025-06-28 17:32:51', NULL),
('cat-buffetware', 'Buffetware', 'مستلزمات البوفيه', 'Professional buffetware including chafing dishes, dispensers, and platters.', 'مستلزمات بوفيه احترافية تشمل حافظات الطعام، والموزعات، والصواني.', 'https://images.unsplash.com/photo-1555244162-803834f70033?w=400&h=300&fit=crop&auto=format', 1, '2025-06-27 19:20:29', '2025-06-28 17:33:01', NULL),
('cat-chinaware', 'Chinaware', 'أدوات البورسلين', 'Elegant porcelain pieces designed to elevate every dining experience in hotels and restaurants.', 'قطع بورسلين أنيقة مصممة للارتقاء بكل تجربة طعام في الفنادق والمطاعم.', 'https://images.unsplash.com/photo-1578749556568-bc2c40e68b61?w=400&h=300&fit=crop&auto=format', 1, '2025-06-27 19:20:29', '2025-06-28 14:36:09', NULL),
('cat-kitchen', 'Kitchen Equipment', 'معدات المطبخ', 'Heavy-duty kitchen equipment and tools designed for hotel chefs and kitchens.', 'معدات مطبخ احترافية ومتكاملة مصممة خصيصاً للمطابخ الفندقية والطهاة.', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&auto=format', 1, '2025-06-27 19:20:29', '2025-06-28 17:13:48', NULL),
('cat-room-service', 'Room Service Equipment', 'خدمات الغرف', 'Dedicated tools and trolleys for efficient in-room dining experiences.', 'معدات وترولي مخصصة لتقديم خدمة الغرف بكفاءة وأناقة.', 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop&auto=format', 1, '2025-06-27 19:20:29', '2025-06-28 14:36:09', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `contact_info`
--

CREATE TABLE `contact_info` (
  `id` int NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `host` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` int NOT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact_info`
--

INSERT INTO `contact_info` (`id`, `email`, `Password`, `host`, `port`, `updated_at`) VALUES
(2, '<EMAIL>', 'DRha@252', 'smtp.titan.email', 465, '2025-06-27 22:38:56');

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title_ar` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `description_ar` text COLLATE utf8mb4_unicode_ci,
  `price` decimal(10,2) NOT NULL,
  `original_price` decimal(10,2) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1',
  `category_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subcategory_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `is_featured` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `title`, `title_ar`, `description`, `description_ar`, `price`, `original_price`, `is_available`, `category_id`, `subcategory_id`, `is_active`, `is_featured`, `created_at`, `updated_at`, `deleted_at`) VALUES
('d565dcb2-c314-47e5-82be-5d78a353b658', ' Cups & Saucers ', '   فناجين وصحون الشاي ', 'Elegant porcelain cups paired with matching saucers, crafted to enhance the serving experience of hot beverages such as tea, coffee, and espresso. Designed for luxury hotels, fine dining establishments, and hospitality environments where presentation matters. Heat-resistant and dishwasher-safe for everyday professional use.\n\n', 'فناجين بورسلين أنيقة مع صحون متطابقة، مصممة لتقديم المشروبات الساخنة مثل الشاي والقهوة والإسبريسو بأسلوب راقٍ. مناسبة للفنادق الفخمة والمطاعم الراقية وبيئات الضيافة التي تهتم بالتفاصيل. مقاومة للحرارة وآمنة للاستخدام في غسالة الصحون، مما يجعلها مثالية للاستخدام الاحترافي اليومي.\n\n', '600.00', NULL, 1, 'cat-chinaware', 'sub-cups-saucers', 1, 0, '2025-06-27 21:15:19', '2025-06-27 22:27:28', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `product_features`
--

CREATE TABLE `product_features` (
  `id` int NOT NULL,
  `product_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `feature_text` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `feature_text_ar` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sort_order` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_features`
--

INSERT INTO `product_features` (`id`, `product_id`, `feature_text`, `feature_text_ar`, `sort_order`) VALUES
(15, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Elegant design that adds a luxurious touch', 'تصميم أنيق يضفي لمسة فاخرة على الطاولة', 0),
(16, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Heat-resistant and suitable for hot beverages', 'مقاومة للحرارة ومناسبة للمشروبات الساخنة', 1),
(17, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Dishwasher-safe', 'آمنة للاستخدام في غسالة الصحون', 2),
(18, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Made of high-quality porcelain', 'مصنوعة من بورسلين عالي الجودة', 3);

-- --------------------------------------------------------

--
-- Table structure for table `product_images`
--

CREATE TABLE `product_images` (
  `id` int NOT NULL,
  `product_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image_url` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `sort_order` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_images`
--

INSERT INTO `product_images` (`id`, `product_id`, `image_url`, `sort_order`, `created_at`) VALUES
(21, 'd565dcb2-c314-47e5-82be-5d78a353b658', '/uploads/1751058671725_fe5434be2e25bb2a.jpg', 0, '2025-06-27 22:27:28'),
(22, 'd565dcb2-c314-47e5-82be-5d78a353b658', '/uploads/1751058671729_59c5db52b1c6583c.webp', 1, '2025-06-27 22:27:28'),
(23, 'd565dcb2-c314-47e5-82be-5d78a353b658', '/uploads/1751058671730_57d5bb30fdfe9e94.webp', 2, '2025-06-27 22:27:28');

-- --------------------------------------------------------

--
-- Table structure for table `product_specifications`
--

CREATE TABLE `product_specifications` (
  `id` int NOT NULL,
  `product_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `spec_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `spec_key_ar` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `spec_value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `spec_value_ar` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sort_order` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `product_specifications`
--

INSERT INTO `product_specifications` (`id`, `product_id`, `spec_key`, `spec_key_ar`, `spec_value`, `spec_value_ar`, `sort_order`) VALUES
(15, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Set includes', 'عدد القطع', '12 pieces (6 cups + 6 saucers)', '12 قطعة (6 فناجين + 6 صحون)', 0),
(16, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Material', 'نوع المادة', 'Premium porcelain', 'بورسلين فاخر', 1),
(17, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Color', 'اللون', 'White or Gold-rimmed decorative', 'أبيض أو مزخرف بحواف ذهبية', 2),
(18, 'd565dcb2-c314-47e5-82be-5d78a353b658', 'Usage', 'الاستخدام', 'Tea, Coffee, Espresso', 'الشاي، القهوة، الإسبريسو', 3);

-- --------------------------------------------------------

--
-- Table structure for table `quote_requests`
--

CREATE TABLE `quote_requests` (
  `id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_company` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `excel_file_url` text COLLATE utf8mb4_unicode_ci,
  `status` enum('pending','processed','rejected') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `quote_requests`
--

INSERT INTO `quote_requests` (`id`, `customer_name`, `customer_email`, `customer_phone`, `customer_company`, `excel_file_url`, `status`, `notes`, `created_at`, `updated_at`, `deleted_at`) VALUES
('QR-1750816939325-2tq1m9gl9', 'ahmed', '<EMAIL>', '7788996655', 'دروب', 'uploads/excel/QR-1750816939325-2tq1m9gl9.xlsx', 'pending', NULL, '2025-06-25 02:02:19', '2025-06-25 02:02:19', NULL),
('QR-1750817099770-jvk19nttk', 'فايزة ', '<EMAIL>', '0505050406', 'شركة نور ', 'uploads/excel/QR-1750817099770-jvk19nttk.xlsx', 'pending', NULL, '2025-06-25 02:04:59', '2025-06-25 02:04:59', NULL),
('QR-1750817335114-ti5ll3tz9', 'علي ', '<EMAIL>', '0505050505', 'علي عامر ', 'uploads/excel/QR-1750817335114-ti5ll3tz9.xlsx', 'pending', NULL, '2025-06-25 02:08:55', '2025-06-25 02:08:55', NULL),
('QR-1750818400733-16wddmtjv', 'علي ', '<EMAIL>', '+966 50 123 4567', 'sold_np', 'uploads/excel/QR-1750818400733-16wddmtjv.xlsx', 'pending', NULL, '2025-06-25 02:26:40', '2025-06-25 02:26:40', NULL),
('QR-1750818844595-bxq2qgj5f', 'ahmed', '<EMAIL>', '0505050505', 'NA', 'uploads/excel/QR-1750818844595-bxq2qgj5f.xlsx', 'pending', NULL, '2025-06-25 02:34:04', '2025-06-25 02:34:04', NULL),
('QR-1750819228829-3yy14j6q2', 'ahmed', '<EMAIL>', '0505050406', 'sold_npيييييييييي', 'uploads/excel/QR-1750819228829-3yy14j6q2.xlsx', 'pending', NULL, '2025-06-25 02:40:28', '2025-06-25 02:40:28', NULL),
('QR-1750819474255-osoguhehr', 'ahmed', '<EMAIL>', '77441122', 'ضصثقفغع', 'uploads/excel/QR-1750819474255-osoguhehr.xlsx', 'pending', NULL, '2025-06-25 02:44:34', '2025-06-25 02:44:34', NULL),
('QR-1751043860358-7dz4h9zhg', 'ahmed', '<EMAIL>', '0505050406', 'sold_np', 'uploads/excel/QR-1751043860358-7dz4h9zhg.xlsx', 'pending', NULL, '2025-06-27 17:04:20', '2025-06-27 17:04:20', NULL),
('QR-1751044163424-d71lp72zz', 'علي ', '<EMAIL>', '0505050505', 'moly', 'uploads/excel/QR-1751044163424-d71lp72zz.xlsx', 'pending', NULL, '2025-06-27 17:09:23', '2025-06-27 17:09:23', NULL),
('QR-1751045430128-5bviseav2', 'فايزة ', '<EMAIL>', '+966 50 123 4567', 'sold_np', 'uploads/excel/QR-1751045430128-5bviseav2.xlsx', 'pending', NULL, '2025-06-27 17:30:30', '2025-06-27 17:30:30', NULL),
('QR-1751045546593-70esbb2ub', 'ahmed', '<EMAIL>', '0777888555', 'NAfffff', 'uploads/excel/QR-1751045546593-70esbb2ub.xlsx', 'pending', NULL, '2025-06-27 17:32:26', '2025-06-27 17:32:26', NULL),
('QR-1751063829991-fdua2sbju', 'ahmed', '<EMAIL>', '+966 50 123 4567', 'sold_np', 'uploads/excel/QR-1751063829991-fdua2sbju.xlsx', 'pending', NULL, '2025-06-27 22:37:10', '2025-06-27 22:37:10', NULL),
('QR-1751063959702-2fbdg1xj7', 'ahmed', '<EMAIL>', '+966 50 123 4567', 'sold_np', 'uploads/excel/QR-1751063959702-2fbdg1xj7.xlsx', 'pending', NULL, '2025-06-27 22:39:19', '2025-06-27 22:39:19', NULL),
('TEST-1750738854305', 'عميل تجريبي', '<EMAIL>', '966501234567', 'شركة تجريبية', 'uploads/excel/test.xlsx', 'pending', 'طلب تجريبي للاختبار', '2025-06-24 04:20:54', '2025-06-24 04:20:54', NULL),
('TEST-1750738865635', 'عميل تجريبي', '<EMAIL>', '966501234567', 'شركة تجريبية', 'uploads/excel/test.xlsx', 'pending', 'طلب تجريبي للاختبار', '2025-06-24 04:21:05', '2025-06-24 04:21:05', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `quote_request_logs`
--

CREATE TABLE `quote_request_logs` (
  `id` int NOT NULL,
  `quote_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `action_by` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_type` enum('note','status_change') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `quote_request_products`
--

CREATE TABLE `quote_request_products` (
  `id` int NOT NULL,
  `quote_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `quote_request_products`
--

INSERT INTO `quote_request_products` (`id`, `quote_id`, `product_id`, `created_at`) VALUES
(39, 'QR-1751043860358-7dz4h9zhg', '710c0ed7-2af8-4130-b504-53ee50943273', '2025-06-27 17:04:20'),
(40, 'QR-1751044163424-d71lp72zz', '710c0ed7-2af8-4130-b504-53ee50943273', '2025-06-27 17:09:23'),
(41, 'QR-1751045430128-5bviseav2', '710c0ed7-2af8-4130-b504-53ee50943273', '2025-06-27 17:30:30'),
(42, 'QR-1751045546593-70esbb2ub', '710c0ed7-2af8-4130-b504-53ee50943273', '2025-06-27 17:32:26'),
(43, 'QR-1751063829991-fdua2sbju', 'd565dcb2-c314-47e5-82be-5d78a353b658', '2025-06-27 22:37:10'),
(44, 'QR-1751063959702-2fbdg1xj7', 'd565dcb2-c314-47e5-82be-5d78a353b658', '2025-06-27 22:39:19');

-- --------------------------------------------------------

--
-- Table structure for table `subcategories`
--

CREATE TABLE `subcategories` (
  `id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name_ar` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `description_ar` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `image_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subcategories`
--

INSERT INTO `subcategories` (`id`, `name`, `name_ar`, `category_id`, `description`, `description_ar`, `is_active`, `created_at`, `updated_at`, `deleted_at`, `image_url`) VALUES
('sub-cups-saucers', 'Cups & Saucers', 'فناجين وصحون الشاي/ القهوة', 'cat-chinaware', 'Classic porcelain cups and matching saucers for serving tea, coffee, and espresso.', 'فناجين بورسلين كلاسيكية مع صحون متطابقة لتقديم الشاي والقهوة والإسبريسو.', 1, '2025-06-27 20:50:50', '2025-06-28 17:25:11', NULL, 'https://freerangestock.com/thumbnail/95980/two-cups-of-coffee-on-table.jpg'),
('sub-dessert-plates', 'Dessert Plates', 'أطباق الحلى', 'cat-chinaware', 'Delicate plates made for serving desserts, cakes, and sweet creations in style.', 'أطباق رقيقة مخصصة لتقديم الحلويات والكيك والإبداعات الحلوة بأناقة.', 1, '2025-06-27 20:50:50', '2025-06-28 16:32:18', NULL, 'https://freerangestock.com/thumbnail/136550/cake-slices-forksstrawberries-and-coffee-cup-on-white-dish-isolated-on-white-background.jpg'),
('sub-dinner-plates', 'Dinner Plates', 'أطباق العشاء ', 'cat-chinaware', 'Elegant and durable plates designed for main course presentations in luxury hotels and fine dining.', 'أطباق متينة وأنيقة مخصصة لتقديم الوجبة الرئيسية في الفنادق الفاخرة والمطاعم الراقية.', 1, '2025-06-27 20:50:50', '2025-06-28 16:32:40', NULL, 'https://freerangestock.com/thumbnail/151262/a-plate-of-rice-with-shrimp-and-vegetables.jpg'),
('sub-pasta-bowls', 'Pasta Bowls', 'أطباق الباستا العميقة', 'cat-chinaware', 'Wide, deep bowls tailored for pasta, risotto, and creamy dishes with rich sauces.', 'أطباق واسعة وعميقة مصممة خصيصاً للباستا والريزوتو والأطباق ذات الصلصات الغنية.', 1, '2025-06-27 20:50:50', '2025-06-27 20:54:33', NULL, 'https://freerangestock.com/thumbnail/110720/table-set-with-plates-and-bowls-of-food.jpg'),
('sub-ramekins', 'Ramekins', 'أوعية صغيرة', 'cat-chinaware', 'Small heat-resistant bowls perfect for serving desserts, sauces, or individual portions.', 'أوعية صغيرة مقاومة للحرارة مثالية لتقديم الحلويات أو الصلصات أو الحصص الفردية.', 1, '2025-06-27 20:50:50', '2025-06-28 16:32:49', '2025-06-28 16:32:49', 'https://media.istockphoto.com/id/2193997762/photo/a-chocolate-souffl%C3%A9.jpg?b=1&s=612x612&w=0&k=20&c=nh_j_jni58GGT69_eby9PJnVjpx8sB_Cmg6cwjAXWJc='),
('sub-salad-plates', 'Salad Plates', 'أطباق السلطة', 'cat-chinaware', 'Stylish plates ideal for serving salads, appetizers, and light entrées with a refined touch.', 'أطباق أنيقة مثالية لتقديم السلطات والمقبلات والأطباق الخفيفة بطريقة راقية.', 1, '2025-06-27 20:50:50', '2025-06-27 20:55:50', NULL, 'https://media.istockphoto.com/id/185119929/photo/plate-of-salad.jpg?s=2048x2048&w=is&k=20&c=DnP6ojCTTNhb4VKX4QDzoLYSuDGJfoJKIOqA_bOX-R4='),
('sub-serving-platters', 'Serving Platters', 'صواني التقديم', 'cat-chinaware', 'Large and elegant platters designed for buffet displays and table-side service.', 'صواني تقديم كبيرة وأنيقة مصممة للبوفيهات وخدمة الطاولة الجانبية.', 1, '2025-06-27 20:50:50', '2025-06-27 20:56:56', NULL, 'https://media.istockphoto.com/id/1335901923/vector/food-trays-realistic-plastic-white-salvers-rectangular-dinner-container-mockup-top-view-on.jpg?s=612x612&w=0&k=20&c=3C2dFa5RC6ofYvIBcXRuE494RqZYyoUO3fhoq6byiEI='),
('sub-soup-bowls', 'Soup Bowls', 'زَبادي الحساء', 'cat-chinaware', 'Deep bowls crafted for serving soups, stews, and broths while maintaining warmth.', 'زَبادي عميقة مصممة لتقديم الحساء واليخنات والحساء الخفيف مع الحفاظ على الحرارة.', 1, '2025-06-27 20:50:50', '2025-06-27 20:57:38', NULL, 'https://media.istockphoto.com/id/510399130/photo/wide-white-bowl.jpg?s=612x612&w=0&k=20&c=DWKlCI9GDgFydA0-RnofwvsubPUXst6G6MG_97XRosw='),
('sub-soup-tureens', 'Soup Tureens', 'قدور الشوربة ذات الغطاء', 'cat-chinaware', 'Lidded soup tureens used for elegant soup presentation and heat retention.', 'قدور شوربة بغطاء تقدم بطريقة أنيقة وتحافظ على حرارة الحساء لفترة أطول.', 1, '2025-06-27 20:50:50', '2025-06-27 20:58:06', NULL, 'https://media.istockphoto.com/id/178906951/photo/soup-tureen.jpg?b=1&s=612x612&w=0&k=20&c=8e17mUlClr9wSwgUv3JlEKqqKb4UDeNSs-xzeDMrArw=');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `contact_info`
--
ALTER TABLE `contact_info`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_products_category` (`category_id`),
  ADD KEY `idx_products_subcategory` (`subcategory_id`);

--
-- Indexes for table `product_features`
--
ALTER TABLE `product_features`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `product_images`
--
ALTER TABLE `product_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_product_images` (`product_id`);

--
-- Indexes for table `product_specifications`
--
ALTER TABLE `product_specifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `quote_requests`
--
ALTER TABLE `quote_requests`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `quote_request_logs`
--
ALTER TABLE `quote_request_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `quote_id` (`quote_id`);

--
-- Indexes for table `quote_request_products`
--
ALTER TABLE `quote_request_products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `idx_quote_products` (`quote_id`);

--
-- Indexes for table `subcategories`
--
ALTER TABLE `subcategories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `contact_info`
--
ALTER TABLE `contact_info`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `product_features`
--
ALTER TABLE `product_features`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `product_images`
--
ALTER TABLE `product_images`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `product_specifications`
--
ALTER TABLE `product_specifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `quote_request_logs`
--
ALTER TABLE `quote_request_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `quote_request_products`
--
ALTER TABLE `quote_request_products`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`),
  ADD CONSTRAINT `products_ibfk_2` FOREIGN KEY (`subcategory_id`) REFERENCES `subcategories` (`id`);

--
-- Constraints for table `product_features`
--
ALTER TABLE `product_features`
  ADD CONSTRAINT `product_features_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_images`
--
ALTER TABLE `product_images`
  ADD CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `product_specifications`
--
ALTER TABLE `product_specifications`
  ADD CONSTRAINT `product_specifications_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `quote_request_logs`
--
ALTER TABLE `quote_request_logs`
  ADD CONSTRAINT `quote_request_logs_ibfk_1` FOREIGN KEY (`quote_id`) REFERENCES `quote_requests` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `quote_request_products`
--
ALTER TABLE `quote_request_products`
  ADD CONSTRAINT `quote_request_products_ibfk_1` FOREIGN KEY (`quote_id`) REFERENCES `quote_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `quote_request_products_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `subcategories`
--
ALTER TABLE `subcategories`
  ADD CONSTRAINT `subcategories_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
SET foreign_key_checks = 1;
