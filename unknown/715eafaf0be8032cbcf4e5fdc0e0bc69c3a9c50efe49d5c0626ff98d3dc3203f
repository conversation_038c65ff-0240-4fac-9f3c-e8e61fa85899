import { NextRequest, NextResponse } from 'next/server';
import { getCategoryById, updateCategory, deleteCategory } from '@/lib/mysql-database';

// GET - جلب فئة بواسطة ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid category ID',
        messageAr: 'معرف الفئة غير صحيح'
      }, { status: 400 });
    }

    const category = await getCategoryById(id);
    if (!category) {
      return NextResponse.json({
        success: false,
        error: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: category
    });

  } catch (error) {
    console.error('Category GET API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// PUT - تحديث فئة
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid category ID',
        messageAr: 'معرف الفئة غير صحيح'
      }, { status: 400 });
    }

    const body = await request.json();

    // التحقق من وجود الفئة أولاً
    const existingCategory = await getCategoryById(id);
    if (!existingCategory) {
      return NextResponse.json({
        success: false,
        error: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    const updatedCategory = await updateCategory(id, body);

    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: 'Category updated successfully',
      messageAr: 'تم تحديث الفئة بنجاح'
    });

  } catch (error) {
    console.error('Category PUT API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// DELETE - حذف فئة
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid category ID',
        messageAr: 'معرف الفئة غير صحيح'
      }, { status: 400 });
    }

    // التحقق من وجود الفئة أولاً
    const existingCategory = await getCategoryById(id);
    if (!existingCategory) {
      return NextResponse.json({
        success: false,
        error: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    const deleted = await deleteCategory(id);

    if (!deleted) {
      return NextResponse.json({
        success: false,
        error: 'Failed to delete category',
        messageAr: 'فشل في حذف الفئة'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully',
      messageAr: 'تم حذف الفئة بنجاح'
    });

  } catch (error) {
    console.error('Category DELETE API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
