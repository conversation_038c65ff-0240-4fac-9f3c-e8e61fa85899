const mysql = require('mysql2/promise');
const fs = require('fs');

async function loadSampleData() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'droobhajer_db'
    });
    
    // Read the sample data SQL file
    const sampleDataSQL = fs.readFileSync('sample_data.sql', 'utf8');
    
    // Split by semicolon and execute each statement
    const statements = sampleDataSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log('Loading sample data...');
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
          console.log('✓ Executed statement');
        } catch (error) {
          console.log('⚠ Statement already exists or error:', error.message);
        }
      }
    }
    
    // Check what products we have now
    const [rows] = await connection.execute('SELECT id, title_ar, price FROM products WHERE deleted_at IS NULL');
    console.log('\nProducts in database after loading sample data:');
    rows.forEach(product => {
      console.log(`- ID: ${product.id}, Title: ${product.title_ar}, Price: ${product.price}`);
    });
    
    await connection.end();
    console.log('\nSample data loading completed!');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

loadSampleData();
