# إعدادات Nginx للمشروع - Hostinger
# ضع هذا الملف في: /etc/nginx/sites-available/droobhajer
# ثم قم بإنشاء رابط: ln -s /etc/nginx/sites-available/droobhajer /etc/nginx/sites-enabled/

# إعادة توجيه HTTP إلى HTTPS
server {
    listen 80;
    server_name droobhajer.com www.droobhajer.com **************;

    # إعادة توجيه جميع طلبات HTTP إلى HTTPS
    return 301 https://$server_name$request_uri;
}

# إعدادات HTTPS
server {
    listen 443 ssl http2;
    server_name droobhajer.com www.droobhajer.com **************;

    # إعدادات SSL (يجب تحديث مسارات الشهادات)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # إعدادات SSL محسنة
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    client_max_body_size 50M;
}

    # إعدادات عامة
    root /var/www/html;
    

    
    # إعدادات الأمان المحسنة لـ HTTPS
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' https:; frame-ancestors 'none'; upgrade-insecure-requests;" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    index index.html index.htm;
    
    # حد أقصى لحجم الملف المرفوع
    client_max_body_size 50M;
    
    # ضغط الملفات
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # إعدادات الكاش للملفات الثابتة
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # حماية الملفات الحساسة
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|sql|sh|config)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # منع الوصول للمجلدات الحساسة
    location ~ ^/(node_modules|\.next|logs|src|lib|components|types|utils|hooks|styles|data|secure-uploads)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # معالجة الملفات المرفوعة
    location /uploads/ {
        alias /var/www/html/public/uploads/;
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # API routes
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # Next.js app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        
        # Fallback للملفات الثابتة
        try_files $uri $uri/ @nextjs;
    }
    
    location @nextjs {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # صفحات الأخطاء المخصصة
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # إعدادات السجلات
    access_log /var/log/nginx/droobhajer_access.log;
    error_log /var/log/nginx/droobhajer_error.log;
}
