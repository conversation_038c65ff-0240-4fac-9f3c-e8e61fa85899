#!/usr/bin/env node

const https = require('https');
const fs = require('fs');

class SitemapMonitor {
  constructor(sitemapUrl) {
    this.sitemapUrl = sitemapUrl;
    this.logFile = 'sitemap-monitor.log';
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    console.log(logEntry.trim());
    fs.appendFileSync(this.logFile, logEntry);
  }

  async checkSitemap() {
    return new Promise((resolve, reject) => {
      const options = {
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)'
        }
      };

      const req = https.request(this.sitemapUrl, options, (res) => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          timestamp: new Date().toISOString()
        });
      });

      req.on('error', reject);
      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }

  async validateXML() {
    return new Promise((resolve, reject) => {
      const req = https.request(this.sitemapUrl, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          const isValid = data.includes('<?xml') && 
                         data.includes('<urlset') && 
                         data.includes('</urlset>');
          resolve({ isValid, size: data.length });
        });
      });

      req.on('error', reject);
      req.end();
    });
  }

  async runCheck() {
    try {
      this.log('🔍 بدء فحص sitemap...');
      
      // فحص الوصول
      const accessCheck = await this.checkSitemap();
      this.log(`📊 Status Code: ${accessCheck.statusCode}`);
      this.log(`📋 Content-Type: ${accessCheck.headers['content-type']}`);
      this.log(`⏰ Last-Modified: ${accessCheck.headers['last-modified']}`);
      this.log(`💾 Cache-Control: ${accessCheck.headers['cache-control']}`);

      // فحص صحة XML
      const xmlCheck = await this.validateXML();
      this.log(`✅ XML Valid: ${xmlCheck.isValid ? 'نعم' : 'لا'}`);
      this.log(`📏 File Size: ${xmlCheck.size} bytes`);

      // تقييم الحالة العامة
      const isHealthy = accessCheck.statusCode === 200 && 
                       xmlCheck.isValid && 
                       accessCheck.headers['content-type']?.includes('xml');

      this.log(`🎯 الحالة العامة: ${isHealthy ? '✅ جيد' : '❌ يحتاج مراجعة'}`);
      this.log('─'.repeat(50));

      return { accessCheck, xmlCheck, isHealthy };

    } catch (error) {
      this.log(`❌ خطأ في الفحص: ${error.message}`);
      return { error: error.message };
    }
  }

  async startMonitoring(intervalMinutes = 60) {
    this.log(`🚀 بدء مراقبة sitemap كل ${intervalMinutes} دقيقة`);
    
    // فحص أولي
    await this.runCheck();
    
    // مراقبة دورية
    setInterval(async () => {
      await this.runCheck();
    }, intervalMinutes * 60 * 1000);
  }
}

// الاستخدام
const monitor = new SitemapMonitor('https://droobhajer.com/sitemap.xml');

// إذا تم تشغيل الملف مباشرة
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--monitor')) {
    const interval = parseInt(args[args.indexOf('--interval') + 1]) || 60;
    monitor.startMonitoring(interval);
  } else {
    monitor.runCheck().then(() => {
      console.log('\n💡 لبدء المراقبة المستمرة، استخدم:');
      console.log('node monitor-sitemap.js --monitor --interval 30');
    });
  }
}

module.exports = SitemapMonitor;
