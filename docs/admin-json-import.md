# دليل استخدام نظام إضافة المنتجات عبر JSON

## نظرة عامة

تم تطوير نظام إضافة المنتجات في لوحة الأدمن ليدعم إضافة منتج واحد أو عدة منتجات في نفس الوقت باستخدام JSON.

## الميزات الجديدة

### 1. إضافة منتج واحد
يمكنك إضافة منتج واحد باستخدام كائن JSON:

```json
{
  "id": "PROD-001",
  "title": "Product Name",
  "titleAr": "اسم المنتج",
  "description": "Product description",
  "descriptionAr": "وصف المنتج",
  "price": 100.50,
  "originalPrice": 150.00,
  "available": true,
  "categoryId": "category-id",
  "subcategoryId": "subcategory-id",
  "features": ["Feature 1", "Feature 2"],
  "featuresAr": ["ميزة 1", "ميزة 2"],
  "specifications": [
    {
      "nameEn": "Material",
      "nameAr": "المادة",
      "valueEn": "Stainless Steel",
      "valueAr": "فولاذ مقاوم للصدأ"
    }
  ],
  "isActive": true,
  "isFeatured": false
}
```

### 2. إضافة عدة منتجات
يمكنك إضافة عدة منتجات باستخدام مصفوفة من كائنات JSON:

```json
[
  {
    "id": "PROD-001",
    "title": "Product 1",
    "titleAr": "المنتج الأول",
    "description": "Description 1",
    "descriptionAr": "وصف المنتج الأول",
    "price": 100.50,
    "categoryId": "category-id",
    "subcategoryId": "subcategory-id",
    "features": ["Feature 1"],
    "featuresAr": ["ميزة 1"],
    "isActive": true
  },
  {
    "id": "PROD-002",
    "title": "Product 2",
    "titleAr": "المنتج الثاني",
    "description": "Description 2",
    "descriptionAr": "وصف المنتج الثاني",
    "price": 200.75,
    "categoryId": "category-id",
    "subcategoryId": "subcategory-id",
    "features": ["Feature 2"],
    "featuresAr": ["ميزة 2"],
    "isActive": true
  }
]
```

## الحقول المطلوبة

### الحقول الأساسية المطلوبة:
- `title`: اسم المنتج بالإنجليزية
- `titleAr`: اسم المنتج بالعربية
- `description`: وصف المنتج بالإنجليزية
- `descriptionAr`: وصف المنتج بالعربية
- `price`: سعر المنتج
- `categoryId`: معرف الفئة الرئيسية
- `subcategoryId`: معرف الفئة الفرعية

### الحقول الاختيارية:
- `id`: معرف مخصص للمنتج (إذا لم يتم توفيره، سيتم إنشاء UUID تلقائياً)
- `originalPrice`: السعر الأصلي قبل الخصم
- `available`: حالة توفر المنتج (افتراضي: true)
- `images`: مصفوفة من روابط الصور
- `features`: مصفوفة من المميزات بالإنجليزية
- `featuresAr`: مصفوفة من المميزات بالعربية
- `specifications`: مصفوفة من المواصفات
- `isActive`: حالة نشاط المنتج (افتراضي: true)
- `isFeatured`: هل المنتج مميز (افتراضي: false)

## كيفية الاستخدام

1. انتقل إلى صفحة إدارة المنتجات في لوحة الأدمن
2. اضغط على زر "إضافة منتج عبر JSON"
3. الصق بيانات JSON في المنطقة المخصصة
4. اضغط على "إضافة المنتج/المنتجات"

## معالجة الأخطاء

- إذا كان هناك خطأ في منتج واحد من مصفوفة المنتجات، سيتم عرض رسالة خطأ تحدد المنتج المحدد
- سيتم التحقق من صحة جميع الحقول المطلوبة قبل الإضافة
- سيتم التحقق من عدم تكرار معرفات المنتجات

## التحديثات التقنية

### قاعدة البيانات
- تم إضافة دالة `addMultipleProductsWithDetails()` لدعم إضافة عدة منتجات

### API
- تم تحديث `/api/admin/products` ليدعم كلا من المنتج الواحد والمنتجات المتعددة
- يتم التحقق تلقائياً من نوع البيانات المرسلة (كائن أو مصفوفة)

### الواجهة
- تم تحديث واجهة الأدمن لتوضيح إمكانية إضافة منتج واحد أو عدة منتجات
- تم إضافة أمثلة واضحة لكلا الحالتين
