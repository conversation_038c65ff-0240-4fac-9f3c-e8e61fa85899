# دليل إعداد IndexNow - 5 دقائق للفهرسة السريعة

## 🚀 ما هو IndexNow؟

IndexNow هو بروتوكول مفتوح المصدر يسمح لمواقع الويب بإشعار محركات البحث فوراً عند إضافة أو تحديث أو حذف المحتوى. يدعم:
- **Microsoft Bing** 
- **Google** (تجريبي)
- **Yandex**
- **Seznam.cz**
- محركات بحث أخرى

## ✅ الإعداد المكتمل في موقعك

تم إعداد IndexNow بالكامل في موقع دروب هجر! إليك ما تم تطبيقه:

### 1. الملفات المضافة:
```
lib/indexnow.ts              # خدمة IndexNow الرئيسية
app/api/indexnow/route.ts    # API endpoint
app/[key]/route.ts           # ملف التحقق من المفتاح
lib/hooks/useIndexNow.ts     # Hook للمكونات
```

### 2. مفتاح Bing المحدد:
- مفتاح API: `********************************`
- متاح في: `https://droobhajer.com/********************************.txt`
- مسجل في Bing Webmaster Tools

## 🚀 إرسال فوري للصفحات الرئيسية

### تشغيل السكريبت المباشر:
```bash
# تشغيل السكريبت لإرسال جميع الصفحات الرئيسية
node scripts/submit-to-indexnow.js

# أو إضافته لـ package.json scripts:
npm run indexnow:submit
```

### إضافة إلى package.json:
```json
{
  "scripts": {
    "indexnow:submit": "node scripts/submit-to-indexnow.js",
    "indexnow:verify": "curl https://droobhajer.com/********************************.txt"
  }
}
```

## 🔧 كيفية الاستخدام

### 1. إرسال تلقائي (موصى به):
```typescript
import { autoSubmitToIndexNow } from '@/lib/hooks/useIndexNow';

// عند إضافة منتج جديد
await autoSubmitToIndexNow('product', productId);

// عند تحديث فئة
await autoSubmitToIndexNow('category', categoryId);

// عند تحديث الصفحات الرئيسية
await autoSubmitToIndexNow('main-pages');
```

### 2. استخدام Hook في المكونات:
```typescript
import { useIndexNow } from '@/lib/hooks/useIndexNow';

function ProductForm() {
  const { submitProduct } = useIndexNow();
  
  const handleSave = async (productId: string) => {
    // حفظ المنتج
    await saveProduct(productId);
    
    // إشعار محركات البحث
    await submitProduct(productId);
  };
}
```

### 3. استخدام API مباشرة:
```typescript
import { indexNowService } from '@/lib/indexnow';

// إرسال منتج واحد
await indexNowService.submitProductUpdates(['product-123']);

// إرسال عدة فئات
await indexNowService.submitCategoryUpdates(['cat-1', 'cat-2']);

// إرسال URLs مخصصة
await indexNowService.submitUrls([
  'https://droobhajer.com/ar/new-page',
  'https://droobhajer.com/en/new-page'
]);
```

## 🌐 استخدام API عبر HTTP

### الحصول على معلومات IndexNow:
```bash
curl https://droobhajer.com/api/indexnow
```

### إرسال منتجات:
```bash
curl -X POST https://droobhajer.com/api/indexnow \
  -H "Content-Type: application/json" \
  -d '{
    "type": "products",
    "ids": ["product-123", "product-456"]
  }'
```

### إرسال فئات:
```bash
curl -X POST https://droobhajer.com/api/indexnow \
  -H "Content-Type: application/json" \
  -d '{
    "type": "categories", 
    "ids": ["category-1"]
  }'
```

### إرسال URLs مخصصة:
```bash
curl -X POST https://droobhajer.com/api/indexnow \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://droobhajer.com/ar/new-page",
      "https://droobhajer.com/en/new-page"
    ]
  }'
```

## 📊 مراقبة الأداء

### 1. فحص حالة الخدمة:
```bash
# فحص API
curl https://droobhajer.com/api/indexnow

# فحص ملف المفتاح
curl https://droobhajer.com/********************************.txt
```

### 2. مراقبة السجلات:
```javascript
// في console المتصفح أو server logs
console.log('✅ تم إرسال URL بنجاح إلى IndexNow');
console.log('❌ فشل إرسال IndexNow: [سبب الخطأ]');
```

### 3. أدوات المراقبة:
- **Bing Webmaster Tools**: مراقبة فهرسة Bing
- **Google Search Console**: مراقبة فهرسة Google
- **Server Logs**: مراقبة طلبات IndexNow

## ⚡ أفضل الممارسات

### 1. متى تستخدم IndexNow:
✅ **استخدم عند:**
- إضافة منتج جديد
- تحديث معلومات منتج
- إضافة فئة جديدة
- تحديث محتوى صفحة مهمة
- حذف صفحة

❌ **لا تستخدم عند:**
- تغييرات CSS أو JavaScript فقط
- تحديثات تحليلات أو إعلانات
- تغييرات لا تؤثر على المحتوى

### 2. حدود الاستخدام:
- **حد أقصى**: 10,000 URL في الطلب الواحد
- **تكرار**: لا توجد حدود صارمة، لكن استخدم بحكمة
- **حجم**: كل URL يجب أن يكون أقل من 2048 حرف

### 3. معالجة الأخطاء:
```typescript
try {
  const result = await indexNowService.submitUrl(url);
  if (result.success) {
    console.log('✅ تم الإرسال بنجاح');
  } else {
    console.error('❌ فشل الإرسال:', result.message);
  }
} catch (error) {
  console.error('❌ خطأ في IndexNow:', error);
}
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. "ملف المفتاح غير موجود"
```bash
# تحقق من وجود الملف
curl https://droobhajer.com/[api-key].txt

# إذا لم يوجد، أعد تشغيل الخادم
```

#### 2. "فشل في الإرسال - 400 Bad Request"
- تحقق من صحة URLs
- تأكد من أن URLs تنتمي لنفس النطاق

#### 3. "فشل في الإرسال - 429 Too Many Requests"
- قلل من تكرار الطلبات
- استخدم batch requests

#### 4. "مفتاح API غير صحيح"
```bash
# تحقق من المفتاح
curl https://droobhajer.com/api/indexnow
```

## 📈 النتائج المتوقعة

### خلال 24-48 ساعة:
- ✅ بدء فهرسة الصفحات الجديدة في Bing
- ✅ تحسن في سرعة اكتشاف المحتوى

### خلال أسبوع:
- ✅ تحسن ملحوظ في فهرسة Bing
- ✅ ظهور أسرع للمحتوى الجديد

### خلال شهر:
- ✅ تحسن عام في رؤية الموقع
- ✅ زيادة في الزيارات العضوية

## 🎯 الخطوات التالية

1. **مراقبة الأداء** في Bing Webmaster Tools
2. **تطبيق IndexNow** في جميع عمليات CRUD
3. **مراقبة السجلات** للتأكد من عدم وجود أخطاء
4. **تحسين المحتوى** بناءً على البيانات

---

🎉 **تهانينا!** IndexNow الآن يعمل بكامل طاقته في موقعك. محركات البحث ستحصل على إشعارات فورية بكل تحديث جديد!
