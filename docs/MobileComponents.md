# مكونات الهواتف المحمولة - دروب هجر

## نظرة عامة

تم إنشاء مجموعة شاملة من المكونات المحمولة لتوفير تجربة مستخدم محسنة للهواتف المحمولة والأجهزة اللوحية. تتضمن هذه المكونات صفحات السلة، من نحن، اتصل بنا، والمنتجات.

## المكونات المحمولة

### 1. `MobileCartPage.tsx`
مكون السلة للهواتف المحمولة مع الميزات التالية:

#### الميزات:
- عرض منتجات السلة بتصميم مناسب للهواتف
- تحكم في الكمية مع أزرار + و -
- حذف المنتجات من السلة
- إ<PERSON><PERSON><PERSON>غ السلة بالكامل
- نموذج طلب عرض سعر منبثق
- حساب المجموع الإجمالي
- إشعارات Toast للتفاعل

#### الوظائف:
- `handleQuantityChange()`: تحديث كمية المنتج
- `handleRemoveItem()`: حذف منتج من السلة
- `handleClearCart()`: إفراغ السلة
- `handleSubmitQuote()`: إرسال طلب عرض سعر

### 2. `MobileAboutPage.tsx`
صفحة "من نحن" للهواتف المحمولة:

#### الميزات:
- قسم البطل مع العنوان والوصف
- عرض الرؤية والمهمة
- قائمة القيم مع الأيقونات
- معلومات الفريق
- دعوة للتواصل
- تكامل مع إعدادات الموقع

#### التخصيص:
- يستخدم الإعدادات من لوحة التحكم
- قيم افتراضية في حالة عدم وجود إعدادات
- دعم كامل للغتين العربية والإنجليزية

### 3. `MobileContactPage.tsx`
صفحة التواصل للهواتف المحمولة:

#### الميزات:
- معلومات التواصل (العنوان، الهاتف، البريد، ساعات العمل)
- نموذج تواصل تفاعلي
- أزرار سريعة للواتساب والاتصال
- إشعارات للنجاح والأخطاء
- تصميم مناسب للمس

#### النموذج:
- الاسم الكامل (مطلوب)
- البريد الإلكتروني (مطلوب)
- رقم الهاتف (اختياري)
- الموضوع (اختياري)
- الرسالة (مطلوب)

### 4. `MobileProductsPage.tsx`
صفحة المنتجات للهواتف المحمولة:

#### الميزات:
- عرض المنتجات في قائمة عمودية
- بحث تفاعلي
- فلترة حسب الفئة
- ترتيب حسب السعر والاسم والتاريخ
- إضافة للسلة مباشرة
- عرض حالة التوفر

## المكونات المتجاوبة

### 1. `ResponsiveCartPage.tsx`
### 2. `ResponsiveAboutPage.tsx`
### 3. `ResponsiveContactPage.tsx`
### 4. `ResponsiveProductsPage.tsx`

هذه المكونات تتحقق من نوع الجهاز وتعرض النسخة المناسبة:
- النسخة المحمولة للهواتف والأجهزة الصغيرة
- النسخة العادية للديسكتوب

## المكونات المشتركة

### `MobileHeader.tsx`
رأس الصفحة للهواتف المحمولة:
- شعار الشركة أو زر الرجوع
- العنوان والعنوان الفرعي
- أزرار البحث والفلترة
- أيقونات الواتساب وتبديل اللغة والسلة

### `MobileBottomNav.tsx`
شريط التنقل السفلي:
- الرئيسية، الفئات، المنتجات، السلة، اتصل بنا
- عداد السلة
- تمييز الصفحة النشطة

### `MobileToast.tsx`
إشعارات منبثقة:
- أنواع مختلفة (نجاح، خطأ، معلومات)
- إغلاق تلقائي
- أيقونات مناسبة

## التحديثات على الصفحات

تم تحديث الصفحات التالية لاستخدام المكونات المتجاوبة:

### `app/[locale]/cart/page.tsx`
```typescript
import ResponsiveCartPage from '../../../components/ResponsiveCartPage';

return (
  <>
    <Navbar locale={locale} />
    <ResponsiveCartPage locale={locale} />
  </>
);
```

### `app/[locale]/about/page.tsx`
```typescript
import ResponsiveAboutPage from '../../../components/ResponsiveAboutPage';

return (
  <>
    <Navbar locale={locale} />
    <ResponsiveAboutPage locale={locale} />
  </>
);
```

### `app/[locale]/contact/page.tsx`
```typescript
import ResponsiveContactPage from '../../../components/ResponsiveContactPage';

return (
  <>
    <Navbar locale={locale} />
    <ResponsiveContactPage locale={locale} />
  </>
);
```

### `app/[locale]/products/page.tsx`
```typescript
import ResponsiveProductsPage from '../../../components/ResponsiveProductsPage';

return (
  <>
    <Navbar locale={locale} />
    <ResponsiveProductsPage 
      locale={locale}
      initialProducts={products}
      initialCategories={categories}
    />
  </>
);
```

## الميزات التقنية

### 1. كشف الجهاز
- فحص User Agent للأجهزة المحمولة
- فحص عرض الشاشة (≤ 768px)
- معالجة تغيير حجم النافذة
- مؤقت احتياطي لمنع التعليق

### 2. إدارة الحالة
- استخدام localStorage للسلة
- React State للواجهة
- Custom Events لتحديث العدادات
- معالجة الأخطاء

### 3. التصميم
- Tailwind CSS للأنماط
- أيقونات Remix Icon
- تأثيرات الحركة والانتقال
- دعم Safe Area للأجهزة الحديثة

### 4. إمكانية الوصول
- Touch targets بحد أدنى 44px
- دعم RTL/LTR
- تباين ألوان مناسب
- تنقل بالكيبورد

## الاستخدام

### تشغيل المكونات المحمولة
المكونات تعمل تلقائياً عند:
- فتح الموقع على هاتف محمول
- تصغير نافذة المتصفح لأقل من 768px
- استخدام أجهزة تدعم اللمس

### التخصيص
يمكن تخصيص المكونات من خلال:
- إعدادات الموقع في لوحة التحكم
- تعديل الألوان في `tailwind.config.js`
- تحديث النصوص في ملفات المكونات

## الاختبار

### اختبار الاستجابة
1. افتح الموقع في المتصفح
2. اضغط F12 لفتح أدوات المطور
3. اختر وضع الجهاز المحمول
4. اختبر التنقل بين الصفحات

### اختبار الوظائف
1. إضافة منتجات للسلة
2. تعديل الكميات
3. إرسال نماذج التواصل
4. البحث والفلترة

## الصيانة

### إضافة صفحات جديدة
1. إنشاء مكون محمول جديد في `components/mobile/`
2. إنشاء مكون متجاوب في `components/`
3. تحديث الصفحة في `app/[locale]/`
4. إضافة رابط في `MobileBottomNav` إذا لزم الأمر

### تحديث التصميم
- تعديل الأنماط في ملفات المكونات
- إضافة أنماط جديدة في `globals.css`
- اختبار على أجهزة مختلفة

## الأداء

### التحسينات المطبقة
- تحميل ديناميكي للمكونات
- تخزين مؤقت للبيانات
- تحسين الصور
- تقليل إعادة الرسم

### مراقبة الأداء
- استخدام React DevTools
- مراقبة أوقات التحميل
- اختبار على شبكات بطيئة
