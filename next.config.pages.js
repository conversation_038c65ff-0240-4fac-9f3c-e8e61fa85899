/** @type {import('next').NextConfig} */
const nextConfig = {
  // تفعيل Pages Router مع getServerSideProps
  experimental: {
    appDir: false // تعطيل App Router
  },
  
  // إعدادات الصور
  images: {
    domains: [
      'localhost',
      'droobhajer.com',
      'www.droobhajer.com',
      'images.unsplash.com',
      'via.placeholder.com'
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // إعدادات i18n
  i18n: {
    locales: ['ar', 'en'],
    defaultLocale: 'ar',
    localeDetection: true,
  },

  // إعدادات الأداء
  poweredByHeader: false,
  compress: true,
  
  // إعدادات الأمان
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },

  // إعدادات إعادة التوجيه
  async redirects() {
    return [
      // إعادة توجيه من App Router إلى Pages Router
      {
        source: '/app/:path*',
        destination: '/:path*',
        permanent: true,
      },
    ];
  },

  // إعدادات إعادة الكتابة
  async rewrites() {
    return [
      // إعادة كتابة للمنتجات
      {
        source: '/:locale/products/:slug',
        destination: '/:locale/products/[slug]',
      },
    ];
  },
};

module.exports = nextConfig;
