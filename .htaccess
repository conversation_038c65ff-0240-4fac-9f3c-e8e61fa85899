# حماية الملفات الحساسة
<Files ".env*">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "ecosystem.config.js">
    Order allow,deny
    Deny from all
</Files>

# حماية مجلدات النظام وإعدادات HTTPS
<IfModule mod_rewrite.c>
    RewriteEngine On

    # إجبار استخدام HTTPS
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP:X-Forwarded-Proto} !https
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # إعادة توجيه www إلى non-www مع HTTPS
    RewriteCond %{HTTP_HOST} ^www\.droobhajer\.com$ [NC]
    RewriteRule ^(.*)$ https://droobhajer.com/$1 [R=301,L]

    # منع الوصول للمجلدات الحساسة
    RewriteRule ^\.next/ - [F,L]
    RewriteRule ^node_modules/ - [F,L]
    RewriteRule ^logs/ - [F,L]
    RewriteRule ^src/ - [F,L]
    RewriteRule ^lib/ - [F,L]
    RewriteRule ^components/ - [F,L]
    RewriteRule ^types/ - [F,L]
    RewriteRule ^utils/ - [F,L]
    RewriteRule ^hooks/ - [F,L]
    RewriteRule ^styles/ - [F,L]
    RewriteRule ^data/ - [F,L]
    RewriteRule ^secure-uploads/ - [F,L]

    # إعادة توجيه جميع الطلبات إلى Next.js مع HTTPS
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ https://localhost:3000/$1 [P,L]
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تحسين الكاش
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# أمان إضافي مع تعزيز HTTPS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"

    # إجبار HTTPS لمدة سنة كاملة مع subdomains
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # CSP محسن للأمان مع دعم HTTPS
    Header always set Content-Security-Policy "default-src 'self' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' https:; frame-ancestors 'none'; upgrade-insecure-requests;"

    # إضافة header للتأكد من استخدام HTTPS
    Header always set X-Forwarded-Proto "https"

    # منع mixed content
    Header always set Content-Security-Policy "upgrade-insecure-requests"
</IfModule>
