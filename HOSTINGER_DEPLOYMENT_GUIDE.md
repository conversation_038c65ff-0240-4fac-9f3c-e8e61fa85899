# 🚀 دليل نشر المشروع على Hostinger

## 📋 المتطلبات الأساسية

### 1. إعدادات Hostinger المطلوبة:
- **Node.js Hosting** (يفضل Node.js 18+ أو 20+)
- **MySQL Database** 
- **Domain Name** مربوط بالسيرفر
- **SSL Certificate** (مجاني من Hostinger)

### 2. معلومات مطلوبة منك:
- عنوان IP السيرفر أو النطاق
- بيانات قاعدة البيانات MySQL
- بيانات الإيميل للإشعارات

## 🔧 خطوات النشر

### الخطوة 1: إعداد قاعدة البيانات

1. **إنشاء قاعدة البيانات:**
   ```sql
   -- قم بتشغيل هذا الملف في phpMyAdmin أو MySQL
   -- الملف موجود في: droobhajer_db.sql
   ```

2. **تحديث إعدادات قاعدة البيانات:**
   ```bash
   # في ملف .env.production
   DB_HOST=localhost  # أو عنوان IP قاعدة البيانات
   DB_USER=your_db_username
   DB_PASSWORD=your_db_password
   DB_NAME=your_database_name
   ```

### الخطوة 2: إعداد الإيميل

1. **إنشاء حساب إيميل في Hostinger:**
   - اذهب إلى لوحة تحكم Hostinger
   - Email → Create Email Account
   - أنشئ: `<EMAIL>`
   - أنشئ: `<EMAIL>`

2. **تحديث إعدادات الإيميل:**
   ```bash
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-email-password
   ADMIN_EMAIL=<EMAIL>
   SMTP_HOST=smtp.hostinger.com
   SMTP_PORT=465
   ```

### الخطوة 3: إعداد النطاق والأمان

1. **تحديث النطاق:**
   ```bash
   NEXT_PUBLIC_APP_URL=https://your-domain.com
   NEXT_PUBLIC_SITE_URL=https://your-domain.com
   NEXTAUTH_URL=https://your-domain.com
   COOKIE_DOMAIN=your-domain.com
   ```

2. **إنشاء مفاتيح أمان جديدة:**
   ```bash
   # استخدم مولد كلمات مرور قوية لإنشاء:
   JWT_SECRET=your-super-secure-jwt-secret-32-chars-min
   ENCRYPTION_KEY=your-super-secure-encryption-key-32-chars-min
   DEFAULT_ADMIN_PASSWORD=YourSecurePassword@2024!
   ```

### الخطوة 4: رفع الملفات

1. **ضغط المشروع:**
   ```bash
   # احذف هذه المجلدات قبل الضغط:
   - node_modules/
   - .next/
   - .git/
   ```

2. **رفع الملفات عبر File Manager أو FTP:**
   - ارفع جميع الملفات إلى `/public_html/`
   - تأكد من رفع ملف `.env.production`

### الخطوة 5: تثبيت Dependencies

```bash
# عبر SSH أو Terminal في Hostinger
cd /home/<USER>/public_html/
npm install
npm run build
```

### الخطوة 6: إعداد PM2 (Process Manager)

```bash
# تثبيت PM2
npm install -g pm2

# إنشاء ملف ecosystem
# الملف موجود في: ecosystem.config.js

# تشغيل التطبيق
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🔒 إعدادات الأمان

### 1. حماية الملفات الحساسة:
```bash
# إنشاء ملف .htaccess في المجلد الرئيسي
# الملف موجود في: .htaccess
```

### 2. تحديث كلمات المرور:
- كلمة مرور المدير الافتراضية
- مفاتيح التشفير
- أسرار JWT

## 📧 اختبار الإعدادات

### 1. اختبار قاعدة البيانات:
```
https://your-domain.com/api/test-db-connection
```

### 2. اختبار الإيميل:
```
https://your-domain.com/api/admin/test-email
```

### 3. اختبار تسجيل الدخول:
```
https://your-domain.com/admin/login
Username: admin
Password: [كلمة المرور التي حددتها]
```

## 🚨 ملاحظات مهمة

1. **النسخ الاحتياطية:** قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
2. **التحديثات:** راقب تحديثات الأمان
3. **المراقبة:** استخدم PM2 لمراقبة حالة التطبيق
4. **الأداء:** فعّل ضغط Gzip في Hostinger
5. **SSL:** تأكد من تفعيل شهادة SSL

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من logs: `pm2 logs`
2. تحقق من حالة التطبيق: `pm2 status`
3. راجع ملف الأخطاء في `/logs/`
