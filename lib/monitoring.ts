// نظام مراقبة الأداء والأخطاء

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  context?: Record<string, unknown>;
}

interface ErrorLog {
  message: string;
  stack?: string;
  context: Record<string, unknown>;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class MonitoringService {
  private metrics: PerformanceMetric[] = [];
  private errors: ErrorLog[] = [];
  private maxMetrics = 1000;
  private maxErrors = 500;

  // تسجيل مقياس أداء
  recordMetric(name: string, value: number, context?: Record<string, unknown>) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      context
    };

    this.metrics.push(metric);
    
    // الحفاظ على حد أقصى من المقاييس
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // طباعة المقاييس المهمة
    if (name.includes('error') || value > 5000) {
      console.warn(`⚠️ Performance metric: ${name} = ${value}ms`, context);
    }
  }

  // تسجيل خطأ
  recordError(
    error: Error | string,
    context: Record<string, unknown> = {},
    severity: ErrorLog['severity'] = 'medium'
  ) {
    const errorLog: ErrorLog = {
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      context,
      timestamp: Date.now(),
      severity
    };

    this.errors.push(errorLog);
    
    // الحفاظ على حد أقصى من الأخطاء
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }

    // طباعة الأخطاء حسب الشدة
    const logMethod = severity === 'critical' ? console.error : 
                     severity === 'high' ? console.error :
                     severity === 'medium' ? console.warn : console.log;

    logMethod(`${this.getSeverityEmoji(severity)} Error [${severity}]:`, {
      message: errorLog.message,
      context: errorLog.context,
      timestamp: new Date(errorLog.timestamp).toISOString()
    });
  }

  // قياس وقت تنفيذ دالة
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    context?: Record<string, unknown>
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, context);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordMetric(`${name}_error`, duration, context);
      this.recordError(error as Error, { ...context, operation: name }, 'high');
      throw error;
    }
  }

  // قياس وقت تنفيذ دالة متزامنة
  measure<T>(
    name: string,
    fn: () => T,
    context?: Record<string, unknown>
  ): T {
    const startTime = Date.now();
    
    try {
      const result = fn();
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, context);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordMetric(`${name}_error`, duration, context);
      this.recordError(error as Error, { ...context, operation: name }, 'high');
      throw error;
    }
  }

  // الحصول على إحصائيات الأداء
  getPerformanceStats(metricName?: string) {
    const filteredMetrics = metricName 
      ? this.metrics.filter(m => m.name === metricName)
      : this.metrics;

    if (filteredMetrics.length === 0) return null;

    const values = filteredMetrics.map(m => m.value);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    
    // حساب المئين 95
    const sorted = values.sort((a, b) => a - b);
    const p95Index = Math.floor(sorted.length * 0.95);
    const p95 = sorted[p95Index];

    return {
      count: filteredMetrics.length,
      average: Math.round(avg),
      min,
      max,
      p95,
      recent: filteredMetrics.slice(-10)
    };
  }

  // الحصول على إحصائيات الأخطاء
  getErrorStats(severity?: ErrorLog['severity']) {
    const filteredErrors = severity 
      ? this.errors.filter(e => e.severity === severity)
      : this.errors;

    const last24h = filteredErrors.filter(
      e => Date.now() - e.timestamp < 24 * 60 * 60 * 1000
    );

    return {
      total: filteredErrors.length,
      last24h: last24h.length,
      recent: filteredErrors.slice(-10),
      bySeverity: {
        critical: this.errors.filter(e => e.severity === 'critical').length,
        high: this.errors.filter(e => e.severity === 'high').length,
        medium: this.errors.filter(e => e.severity === 'medium').length,
        low: this.errors.filter(e => e.severity === 'low').length,
      }
    };
  }

  private getSeverityEmoji(severity: ErrorLog['severity']): string {
    switch (severity) {
      case 'critical': return '🚨';
      case 'high': return '❌';
      case 'medium': return '⚠️';
      case 'low': return 'ℹ️';
      default: return '❓';
    }
  }

  // تصدير البيانات للمراقبة الخارجية
  exportData() {
    return {
      metrics: this.metrics,
      errors: this.errors,
      stats: {
        performance: this.getPerformanceStats(),
        errors: this.getErrorStats()
      },
      timestamp: Date.now()
    };
  }

  // مسح البيانات القديمة
  cleanup(olderThanMs: number = 24 * 60 * 60 * 1000) {
    const cutoff = Date.now() - olderThanMs;
    
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
    this.errors = this.errors.filter(e => e.timestamp > cutoff);
    
    console.log(`🧹 Monitoring cleanup: Removed old data older than ${olderThanMs}ms`);
  }
}

// إنشاء instance واحد للتطبيق
export const monitoring = new MonitoringService();

// دوال مساعدة للاستخدام السريع
export const recordMetric = monitoring.recordMetric.bind(monitoring);
export const recordError = monitoring.recordError.bind(monitoring);
export const measureAsync = monitoring.measureAsync.bind(monitoring);
export const measure = monitoring.measure.bind(monitoring);

// تنظيف دوري للبيانات القديمة
if (typeof window === 'undefined') {
  // تشغيل التنظيف كل ساعة في الخادم
  setInterval(() => {
    monitoring.cleanup();
  }, 60 * 60 * 1000);
}
