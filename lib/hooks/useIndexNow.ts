import { useCallback } from 'react';
import { indexNowService } from '../indexnow';

interface UseIndexNowReturn {
  submitProduct: (productId: string) => Promise<void>;
  submitCategory: (categoryId: string) => Promise<void>;
  submitSubcategory: (subcategoryId: string) => Promise<void>;
  submitUrls: (urls: string[]) => Promise<void>;
  submitMainPages: () => Promise<void>;
}

export function useIndexNow(): UseIndexNowReturn {
  
  const submitProduct = useCallback(async (productId: string) => {
    try {
      const result = await indexNowService.submitProductUpdates([productId]);
      if (result.success) {
        console.log(`✅ تم إرسال المنتج ${productId} إلى IndexNow`);
      } else {
        console.error(`❌ فشل إرسال المنتج ${productId}:`, result.message);
      }
    } catch (error) {
      console.error('خطأ في إرسال المنتج إلى IndexNow:', error);
    }
  }, []);

  const submitCategory = useCallback(async (categoryId: string) => {
    try {
      const result = await indexNowService.submitCategoryUpdates([categoryId]);
      if (result.success) {
        console.log(`✅ تم إرسال الفئة ${categoryId} إلى IndexNow`);
      } else {
        console.error(`❌ فشل إرسال الفئة ${categoryId}:`, result.message);
      }
    } catch (error) {
      console.error('خطأ في إرسال الفئة إلى IndexNow:', error);
    }
  }, []);

  const submitSubcategory = useCallback(async (subcategoryId: string) => {
    try {
      const result = await indexNowService.submitSubcategoryUpdates([subcategoryId]);
      if (result.success) {
        console.log(`✅ تم إرسال الفئة الفرعية ${subcategoryId} إلى IndexNow`);
      } else {
        console.error(`❌ فشل إرسال الفئة الفرعية ${subcategoryId}:`, result.message);
      }
    } catch (error) {
      console.error('خطأ في إرسال الفئة الفرعية إلى IndexNow:', error);
    }
  }, []);

  const submitUrls = useCallback(async (urls: string[]) => {
    try {
      const result = await indexNowService.submitUrls(urls);
      if (result.success) {
        console.log(`✅ تم إرسال ${urls.length} URL إلى IndexNow`);
      } else {
        console.error(`❌ فشل إرسال URLs:`, result.message);
      }
    } catch (error) {
      console.error('خطأ في إرسال URLs إلى IndexNow:', error);
    }
  }, []);

  const submitMainPages = useCallback(async () => {
    try {
      const result = await indexNowService.submitMainPages();
      if (result.success) {
        console.log('✅ تم إرسال الصفحات الرئيسية إلى IndexNow');
      } else {
        console.error('❌ فشل إرسال الصفحات الرئيسية:', result.message);
      }
    } catch (error) {
      console.error('خطأ في إرسال الصفحات الرئيسية إلى IndexNow:', error);
    }
  }, []);

  return {
    submitProduct,
    submitCategory,
    submitSubcategory,
    submitUrls,
    submitMainPages
  };
}

// دالة مساعدة لإرسال تلقائي عند تحديث البيانات
export async function autoSubmitToIndexNow(
  type: 'product' | 'category' | 'subcategory' | 'main-pages',
  id?: string
) {
  try {
    let result;
    
    switch (type) {
      case 'product':
        if (!id) throw new Error('معرف المنتج مطلوب');
        result = await indexNowService.submitProductUpdates([id]);
        break;
      case 'category':
        if (!id) throw new Error('معرف الفئة مطلوب');
        result = await indexNowService.submitCategoryUpdates([id]);
        break;
      case 'subcategory':
        if (!id) throw new Error('معرف الفئة الفرعية مطلوب');
        result = await indexNowService.submitSubcategoryUpdates([id]);
        break;
      case 'main-pages':
        result = await indexNowService.submitMainPages();
        break;
      default:
        throw new Error('نوع غير مدعوم');
    }

    if (result.success) {
      console.log(`✅ تم إرسال ${type} ${id || ''} تلقائياً إلى IndexNow`);
    } else {
      console.error(`❌ فشل الإرسال التلقائي لـ ${type}:`, result.message);
    }

    return result;
  } catch (error) {
    console.error('خطأ في الإرسال التلقائي إلى IndexNow:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'خطأ غير معروف'
    };
  }
}
