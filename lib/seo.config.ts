import { DefaultSeoProps } from 'next-seo';
import { Locale } from './i18n';
import { getPageKeywords } from './main-keywords';

// الإعدادات العامة للـ SEO
export const getDefaultSEO = (locale: Locale): DefaultSeoProps => {
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    titleTemplate: `%s | ${siteName}`,
    defaultTitle: siteName,
    description: locale === 'ar'
      ? 'دروب هجر - المتخصصة في أطباق البوفيه الفاخرة ومعدات العرض الاحترافية. نوفر أطباق تقديم مستطيلة ودائرية، صحون عرض كبيرة، معدات بوفيه احترافية، وأدوات الضيافة الراقية للفنادق والمنتجعات والمطاعم الفاخرة. خدمة متخصصة وعروض أسعار مخصصة.'
      : 'DROOB HAJER - Specialists in luxury buffet plates and professional display equipment. We provide rectangular and round serving plates, large display platters, professional buffet equipment, and premium hospitality supplies for luxury hotels, resorts, and fine dining restaurants. Specialized service with custom quotations.',
    
    canonical: `${baseUrl}/${locale}`,
    
    openGraph: {
      type: 'website',
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      url: `${baseUrl}/${locale}`,
      siteName: siteName,
      title: siteName,
      description: locale === 'ar'
        ? 'منصة دروب هجر المتخصصة في تجهيزات الفنادق والمنتجعات - عروض أسعار مخصصة لأثاث الغرف الفندقية والمعدات الفندقية الشاملة'
        : 'DROOB HAJER specialized platform for hotel and resort equipment - Customized quotes for hotel room furniture and comprehensive hotel equipment',
      images: [
        {
          url: `${baseUrl}/images/og-default.jpg`,
          width: 1200,
          height: 630,
          alt: siteName,
          type: 'image/jpeg',
        },
      ],
    },
    
    twitter: {
      handle: '@droobhajer',
      site: '@droobhajer',
      cardType: 'summary_large_image',
    },
    
    additionalMetaTags: [
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      {
        name: 'theme-color',
        content: '#3B82F6',
      },
      {
        name: 'msapplication-TileColor',
        content: '#3B82F6',
      },
      {
        name: 'apple-mobile-web-app-capable',
        content: 'yes',
      },
      {
        name: 'apple-mobile-web-app-status-bar-style',
        content: 'default',
      },
      {
        name: 'format-detection',
        content: 'telephone=no',
      },
      {
        name: 'keywords',
        content: locale === 'ar'
          ? 'دروب هجر, تجهيزات فندقية, أثاث فندقي, بياضات فنادق, معدات فندقية, تجهيز غرف فندقية, أدوات ضيافة, مستلزمات فنادق, عرض سعر فندقي, تصميم فندقي, مشاريع فندقية, السعودية'
          : 'DROOB HAJER, hotel equipment, hotel furniture, hotel linens, hospitality supplies, hotel room setup, guest amenities, hotel supplies, hotel quotation, hotel design, hotel projects, Saudi Arabia',
      },
      {
        name: 'author',
        content: 'DROOB HAJER',
      },
      {
        name: 'publisher',
        content: 'DROOB HAJER',
      },
      {
        name: 'robots',
        content: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
      },
      {
        name: 'googlebot',
        content: 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1',
      },
      {
        name: 'bingbot',
        content: 'index, follow',
      },
      {
        property: 'og:locale',
        content: locale === 'ar' ? 'ar_SA' : 'en_US',
      },
      {
        property: 'og:site_name',
        content: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      },
      {
        name: 'application-name',
        content: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      },
      {
        name: 'msapplication-tooltip',
        content: locale === 'ar' ? 'منصة التجهيزات الفندقية الشاملة' : 'Comprehensive Hotel Equipment Platform',
      },
      {
        name: 'geo.region',
        content: 'SA',
      },
      {
        name: 'geo.country',
        content: 'Saudi Arabia',
      },
      {
        name: 'ICBM',
        content: '24.7136, 46.6753', // إحداثيات الرياض
      },
      {
        name: 'distribution',
        content: 'global',
      },
      {
        name: 'rating',
        content: 'general',
      },
    ],
    
    additionalLinkTags: [
      {
        rel: 'icon',
        href: '/favicon.ico',
        sizes: '32x32',
      },
      {
        rel: 'icon',
        href: '/favicon.svg',
        type: 'image/svg+xml',
      },
      {
        rel: 'apple-touch-icon',
        href: '/apple-touch-icon.png',
        sizes: '180x180',
      },
      {
        rel: 'manifest',
        href: '/manifest.json',
      },
      {
        rel: 'sitemap',
        href: '/sitemap.xml',
      },
      {
        rel: 'alternate',
        type: 'application/rss+xml',
        href: `${baseUrl}/rss.xml`,
      },
      {
        rel: 'preconnect',
        href: 'https://fonts.googleapis.com',
      },
      {
        rel: 'preconnect',
        href: 'https://fonts.gstatic.com',
        crossOrigin: 'anonymous',
      },
      {
        rel: 'dns-prefetch',
        href: 'https://www.google-analytics.com',
      },
      {
        rel: 'alternate',
        hrefLang: 'ar',
        href: `${baseUrl}/ar`,
      },
      {
        rel: 'alternate',
        hrefLang: 'en',
        href: `${baseUrl}/en`,
      },
      {
        rel: 'alternate',
        hrefLang: 'x-default',
        href: `${baseUrl}/ar`,
      },
      {
        rel: 'canonical',
        href: `${baseUrl}/${locale}`,
      },
    ],
    
    robotsProps: {
      nosnippet: false,
      notranslate: false,
      noimageindex: false,
      noarchive: false,
      maxSnippet: -1,
      maxImagePreview: 'large',
      maxVideoPreview: -1,
    },
  };
};

// إعدادات خاصة للصفحات المختلفة
export const getPageSEO = (
  locale: Locale,
  page: 'home' | 'products' | 'categories' | 'about' | 'contact' | 'cart',
  customData?: {
    title?: string;
    description?: string;
    keywords?: string[];
    canonical?: string;
    noIndex?: boolean;
  }
) => {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  
  const pageConfigs = {
    home: {
      title: locale === 'ar' ? 'دروب هجر - أطباق البوفيه ومعدات الضيافة الفاخرة' : 'DROOB HAJER - Premium Buffet & Hospitality Equipment',
      description: locale === 'ar'
        ? 'دروب هجر - متجر متخصص في تجهيزات الفنادق والمطاعم. أطباق بوفيه فاخرة، معدات مطابخ، أدوات ضيافة، وتجهيزات فندقية عالية الجودة بأفضل الأسعار.'
        : 'DROOB HAJER - Specialized store for hotel and restaurant equipment. Luxury buffet plates, kitchen equipment, hospitality supplies, and high-quality hotel equipment at best prices.',
      canonical: `${baseUrl}/${locale}`,
    },
    products: {
      title: locale === 'ar' ? 'أطباق البوفيه ومعدات العرض - مجموعة شاملة' : 'Premium Buffet Plates & Display Equipment Collection',
      description: locale === 'ar'
        ? 'تصفح مجموعة شاملة من أطباق البوفيه الفاخرة ومعدات العرض الاحترافية. أطباق تقديم، سخانات بوفيه، عربات متحركة، وأدوات ضيافة راقية للفنادق والمطاعم.'
        : 'Browse comprehensive collection of luxury buffet plates and professional display equipment. Serving plates, buffet warmers, mobile carts, and elegant hospitality tools for hotels and restaurants.',
      canonical: `${baseUrl}/${locale}/products`,
    },
    categories: {
      title: locale === 'ar' ? 'فئات التجهيزات الفندقية - دروب هجر' : 'Hotel Equipment Categories - DROOB HAJER',
      description: locale === 'ar'
        ? 'استكشف فئات التجهيزات الفندقية: بوفيه، أدوات تقديم، سخانات، عربات، موزعات عصير، معدات مطابخ، ومستلزمات ضيافة. تصفح بسهولة واطلب عرض سعر مخصص.'
        : 'Explore hotel equipment categories: buffet, serving tools, warmers, trolleys, juice dispensers, kitchen equipment, and hospitality supplies. Browse easily and request custom quote.',
      canonical: `${baseUrl}/${locale}/categories`,
    },
    about: {
      title: locale === 'ar' ? 'من نحن - دروب هجر للتجهيزات الفندقية' : 'About Us - DROOB HAJER Hotel Equipment',
      description: locale === 'ar'
        ? 'تعرف على دروب هجر، الرائدة في تجهيزات الفنادق والمنتجعات بالسعودية. حلول شاملة للمستثمرين وشركات التصميم مع عروض أسعار مخصصة وضمان جودة عالية.'
        : 'Learn about DROOB HAJER, leading in hotel and resort equipment in Saudi Arabia. Comprehensive solutions for investors and design companies with custom quotes and quality assurance.',
      canonical: `${baseUrl}/${locale}/about`,
    },
    contact: {
      title: locale === 'ar' ? 'تواصل معنا - دروب هجر للتجهيزات الفندقية' : 'Contact Us - DROOB HAJER Hotel Equipment',
      description: locale === 'ar'
        ? 'تواصل مع خبراء دروب هجر للحصول على استشارات مجانية وعروض أسعار مخصصة للتجهيزات الفندقية. فريق متخصص في تجهيز الفنادق والمنتجعات والمطاعم.'
        : 'Contact DROOB HAJER experts for free consultations and custom quotes for hotel equipment. Specialized team in hotel, resort and restaurant equipment setup.',
      canonical: `${baseUrl}/${locale}/contact`,
    },
    cart: {
      title: locale === 'ar' ? 'طلب عرض السعر - مراجعة التجهيزات المختارة' : 'Quote Request - Review Selected Equipment',
      description: locale === 'ar'
        ? 'راجع التجهيزات الفندقية المختارة واطلب عرض سعر مخصص لمشروعك. خدمة احترافية وعروض أسعار تنافسية مضمونة.'
        : 'Review your selected hotel equipment and request a custom quote for your project. Professional service and competitive pricing guaranteed.',
      canonical: `${baseUrl}/${locale}/cart`,
      noIndex: true,
    },
  };
  
  const config = pageConfigs[page];
  
  return {
    title: customData?.title || config.title,
    description: customData?.description || config.description,
    canonical: customData?.canonical || config.canonical,
    noindex: customData?.noIndex || false,
    nofollow: customData?.noIndex || false,
    
    openGraph: {
      title: customData?.title || config.title,
      description: customData?.description || config.description,
      url: customData?.canonical || config.canonical,
      images: [
        {
          url: `${baseUrl}/images/og-${page}.jpg`,
          width: 1200,
          height: 630,
          alt: customData?.title || config.title,
          type: 'image/jpeg',
        },
      ],
    },
    
    additionalMetaTags: [
      {
        name: 'keywords',
        content: customData?.keywords?.join(', ') || getPageKeywords(
          page === 'cart' ? 'home' : page as 'home' | 'products' | 'categories' | 'about' | 'contact',
          locale
        ).join(', '),
      },
    ],
  };
};
