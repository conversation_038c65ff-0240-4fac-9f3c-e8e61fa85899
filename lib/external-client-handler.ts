import { NextRequest } from 'next/server';

// قائمة User-Agent للعملاء الخارجيين المعروفين
const KNOWN_EXTERNAL_CLIENTS = [
  'Googlebot',
  'Bingbot',
  'Slurp', // Yahoo
  'DuckDuckBot',
  '<PERSON><PERSON><PERSON><PERSON>',
  'YandexBot',
  'facebookexternalhit',
  'Twitterbot',
  'LinkedInBot',
  'WhatsApp',
  'Telegram',
  'ChatGPT',
  'Claude',
  'GPTBot',
  'CCBot',
  'anthropic-ai',
  'OpenAI',
];

// فحص ما إذا كان الطلب من عميل خارجي
export function isExternalClient(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent') || '';
  
  return KNOWN_EXTERNAL_CLIENTS.some(client => 
    userAgent.toLowerCase().includes(client.toLowerCase())
  );
}

// الحصول على نوع العميل
export function getClientType(request: NextRequest): string {
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  for (const client of KNOWN_EXTERNAL_CLIENTS) {
    if (userAgent.toLowerCase().includes(client.toLowerCase())) {
      return client;
    }
  }
  
  return 'unknown';
}

// معالجة خاصة للعملاء الخارجيين
export function handleExternalClientRequest(request: NextRequest) {
  const clientType = getClientType(request);
  const pathname = request.nextUrl.pathname;
  
  console.log(`🤖 External client detected: ${clientType} requesting ${pathname}`);
  
  // إضافة headers خاصة للعملاء الخارجيين
  const headers = new Headers();
  headers.set('X-Client-Type', clientType);
  headers.set('X-External-Client', 'true');
  
  return { clientType, headers };
}

// تسجيل أخطاء العملاء الخارجيين
export function logExternalClientError(
  request: NextRequest,
  error: Error,
  context: string
) {
  const clientType = getClientType(request);
  const pathname = request.nextUrl.pathname;
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  console.error(`❌ External client error:`, {
    clientType,
    pathname,
    userAgent,
    context,
    error: error.message,
    timestamp: new Date().toISOString()
  });
}

// إنشاء استجابة آمنة للعملاء الخارجيين
export function createSafeExternalResponse(
  request: NextRequest,
  fallbackContent: string = 'Content temporarily unavailable'
) {
  const clientType = getClientType(request);
  
  return new Response(fallbackContent, {
    status: 200,
    headers: {
      'Content-Type': 'text/plain',
      'X-Client-Type': clientType,
      'X-Fallback-Response': 'true',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  });
}
