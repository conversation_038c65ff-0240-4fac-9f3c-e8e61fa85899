import mysql from 'mysql2/promise';

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4',
  timezone: '+00:00',
  connectionLimit: 5, // تقليل عدد الاتصالات
  queueLimit: 0,
  idleTimeout: 300000, // 5 دقائق
  maxIdle: 2, // الحد الأقصى للاتصالات الخاملة
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
};

// إنشاء pool للاتصالات
let pool: mysql.Pool | null = null;

export function getPool(): mysql.Pool {
  if (!pool) {
    pool = mysql.createPool(dbConfig);
  }
  return pool;
}

// دالة للحصول على اتصال واحد
export async function getConnection(): Promise<mysql.PoolConnection> {
  const pool = getPool();
  return await pool.getConnection();
}

// دالة لتنفيذ استعلام
export async function executeQuery<T = Record<string, unknown>>(
  query: string,
  params: unknown[] = []
): Promise<T[]> {
  let connection: mysql.PoolConnection | null = null;
  try {
    connection = await getConnection();
    const [rows] = await connection.execute(query, params);
    return rows as T[];
  } catch (error) {
    console.error('❌ Database query error:', error);
    console.error('❌ Query:', query);
    console.error('❌ Params:', params);
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

// دالة لتنفيذ استعلام واحد
export async function executeQuerySingle<T = Record<string, unknown>>(
  query: string,
  params: unknown[] = []
): Promise<T | null> {
  const results = await executeQuery<T>(query, params);
  return results.length > 0 ? results[0] : null;
}

// دالة لتنفيذ استعلام إدراج والحصول على ID
export async function executeInsert(
  query: string,
  params: unknown[] = []
): Promise<{ insertId: number; affectedRows: number }> {
  const connection = await getConnection();
  try {
    const [result] = await connection.execute(query, params);
    const insertResult = result as mysql.ResultSetHeader;
    return {
      insertId: insertResult.insertId,
      affectedRows: insertResult.affectedRows
    };
  } finally {
    connection.release();
  }
}

// دالة لتنفيذ استعلام تحديث أو حذف
export async function executeUpdate(
  query: string,
  params: unknown[] = []
): Promise<{ affectedRows: number; changedRows: number }> {
  const connection = await getConnection();
  try {
    const [result] = await connection.execute(query, params);
    const updateResult = result as mysql.ResultSetHeader;
    return {
      affectedRows: updateResult.affectedRows,
      changedRows: updateResult.changedRows || 0
    };
  } finally {
    connection.release();
  }
}

// دالة لإغلاق pool الاتصالات
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}

// دالة لإعادة تعيين pool الاتصالات
export async function resetPool(): Promise<void> {
  await closePool();
  pool = mysql.createPool(dbConfig);
}

// دالة لاختبار الاتصال
export async function testConnection(): Promise<boolean> {
  try {
    const connection = await getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}
