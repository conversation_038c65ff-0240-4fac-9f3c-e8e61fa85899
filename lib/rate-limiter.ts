import { NextApiRequest } from 'next';
import { NextRequest } from 'next/server';

// إعدادات Rate Limiting
interface RateLimitConfig {
  windowMs: number; // نافذة زمنية بالميلي ثانية
  maxRequests: number; // عدد الطلبات المسموح
  skipSuccessfulRequests?: boolean; // تجاهل الطلبات الناجحة
  skipFailedRequests?: boolean; // تجاهل الطلبات الفاشلة
}

// أنواع مختلفة من Rate Limiting
export const RATE_LIMIT_CONFIGS = {
  // تسجيل الدخول - صارم جداً
  LOGIN: {
    windowMs: 15 * 60 * 1000, // 15 دقيقة
    maxRequests: 5, // 5 محاولات فقط
    skipSuccessfulRequests: true
  },
  
  // API عام - متوسط
  API_GENERAL: {
    windowMs: 60 * 1000, // دقيقة واحدة
    maxRequests: 100, // 100 طلب في الدقيقة
    skipSuccessfulRequests: false
  },
  
  // رفع الملفات - محدود
  UPLOAD: {
    windowMs: 60 * 1000, // دقيقة واحدة
    maxRequests: 10, // 10 ملفات في الدقيقة
    skipSuccessfulRequests: false
  },
  
  // العمليات الإدارية - متوسط
  ADMIN: {
    windowMs: 60 * 1000, // دقيقة واحدة
    maxRequests: 200, // 200 طلب في الدقيقة
    skipSuccessfulRequests: false
  },
  
  // طلبات التسعير - محدود
  QUOTE_REQUEST: {
    windowMs: 60 * 60 * 1000, // ساعة واحدة
    maxRequests: 5, // 5 طلبات في الساعة
    skipSuccessfulRequests: false
  }
};

// تخزين محاولات الطلبات في الذاكرة
const requestStore = new Map<string, { count: number; resetTime: number; blocked: boolean }>();

// تنظيف البيانات القديمة كل 10 دقائق
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of requestStore.entries()) {
    if (now > data.resetTime) {
      requestStore.delete(key);
    }
  }
}, 10 * 60 * 1000);

// الحصول على IP العميل - دعم NextApiRequest و NextRequest
export function getClientIP(req: NextApiRequest | NextRequest): string {
  try {
    // التحقق من نوع الطلب
    if (req && typeof req === 'object' && 'socket' in req && req.socket) {
      // NextApiRequest
      const forwarded = req.headers['x-forwarded-for'];
      const ip = forwarded
        ? (Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0])
        : req.socket.remoteAddress;
      return ip || 'unknown';
    } else if (req && typeof req === 'object' && 'headers' in req && typeof req.headers.get === 'function') {
      // NextRequest
      const forwarded = req.headers.get('x-forwarded-for');
      const realIP = req.headers.get('x-real-ip');
      const ip = forwarded
        ? forwarded.split(',')[0].trim()
        : realIP || 'unknown';
      return ip;
    } else {
      return 'unknown';
    }
  } catch (error) {
    console.error('Error getting client IP:', error);
    return 'unknown';
  }
}

// إنشاء مفتاح فريد للطلب
function createRateLimitKey(ip: string, endpoint: string): string {
  return `${ip}:${endpoint}`;
}

// فحص Rate Limit - دعم NextApiRequest و NextRequest
export function checkRateLimit(
  req: NextApiRequest | NextRequest,
  endpoint: string,
  config: RateLimitConfig
): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
  const ip = getClientIP(req);
  return checkRateLimitByIP(ip, endpoint, config);
}

// فحص Rate Limit بـ IP مباشرة
export function checkRateLimitByIP(
  ip: string,
  endpoint: string,
  config: RateLimitConfig
): { allowed: boolean; remaining: number; resetTime: number; retryAfter?: number } {
  const key = createRateLimitKey(ip, endpoint);
  const now = Date.now();
  
  // الحصول على البيانات الحالية أو إنشاء جديدة
  let requestData = requestStore.get(key);
  
  if (!requestData || now > requestData.resetTime) {
    // إنشاء نافذة جديدة
    requestData = {
      count: 0,
      resetTime: now + config.windowMs,
      blocked: false
    };
    requestStore.set(key, requestData);
  }
  
  // زيادة العداد
  requestData.count++;
  
  // فحص الحد الأقصى
  if (requestData.count > config.maxRequests) {
    requestData.blocked = true;
    const retryAfter = Math.ceil((requestData.resetTime - now) / 1000);
    
    return {
      allowed: false,
      remaining: 0,
      resetTime: requestData.resetTime,
      retryAfter
    };
  }
  
  return {
    allowed: true,
    remaining: config.maxRequests - requestData.count,
    resetTime: requestData.resetTime
  };
}

// Middleware للـ Rate Limiting - دعم NextApiRequest و NextRequest
export function rateLimitMiddleware(config: RateLimitConfig) {
  return (req: NextApiRequest | NextRequest, endpoint: string) => {
    return checkRateLimit(req, endpoint, config);
  };
}

// حظر IP مؤقتاً
export function blockIP(ip: string, durationMs: number = 60 * 60 * 1000): void {
  const key = `blocked:${ip}`;
  const now = Date.now();
  
  requestStore.set(key, {
    count: 0,
    resetTime: now + durationMs,
    blocked: true
  });
}

// فحص ما إذا كان IP محظوراً
export function isIPBlocked(ip: string): boolean {
  const key = `blocked:${ip}`;
  const data = requestStore.get(key);
  
  if (!data) return false;
  
  const now = Date.now();
  if (now > data.resetTime) {
    requestStore.delete(key);
    return false;
  }
  
  return data.blocked;
}

// إلغاء حظر IP
export function unblockIP(ip: string): void {
  const key = `blocked:${ip}`;
  requestStore.delete(key);
}

// الحصول على إحصائيات Rate Limiting
export function getRateLimitStats(): {
  totalKeys: number;
  blockedIPs: number;
  activeWindows: number;
} {
  const now = Date.now();
  let blockedIPs = 0;
  let activeWindows = 0;
  
  for (const [key, data] of requestStore.entries()) {
    if (key.startsWith('blocked:')) {
      if (now <= data.resetTime) {
        blockedIPs++;
      }
    } else {
      if (now <= data.resetTime) {
        activeWindows++;
      }
    }
  }
  
  return {
    totalKeys: requestStore.size,
    blockedIPs,
    activeWindows
  };
}

// تنظيف جميع البيانات (للطوارئ)
export function clearAllRateLimits(): void {
  requestStore.clear();
}

// تسجيل محاولة مشبوهة
export function logSuspiciousActivity(
  ip: string,
  endpoint: string,
  reason: string,
  userAgent?: string | object
): void {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    ip,
    endpoint,
    reason,
    userAgent: userAgent || 'unknown'
  };
  
  // في التطبيق الحقيقي، يجب حفظ هذا في قاعدة بيانات أو ملف log
  console.warn('🚨 Suspicious activity detected:', logEntry);
  
  // حظر IP تلقائياً في حالات معينة
  if (reason.includes('brute force') || reason.includes('too many requests')) {
    blockIP(ip, 24 * 60 * 60 * 1000); // حظر لمدة 24 ساعة
  }
}
