# نظام رفع صور الهيرو - Hero Images Upload System

## نظرة عامة

تم إنشاء نظام متكامل لرفع وإدارة صور الهيرو في صفحة إعدادات الموقع. النظام يسمح برفع الصور من الكمبيوتر مباشرة وحفظها في مجلد `public/uploads`.

## الميزات الجديدة

### 🖼️ رفع الصور
- **رفع متعدد**: إمكانية رفع عدة صور في نفس الوقت
- **معاينة فورية**: عرض الصور فور رفعها
- **شريط التقدم**: عرض تقدم رفع كل صورة
- **التحقق من النوع**: قبول الصور فقط (JPG, PNG, WebP, etc.)
- **حد الحجم**: حد أقصى 10MB لكل صورة
- **حد العدد**: حد أقصى 5 صور هيرو

### 🎛️ إدارة الصور
- **إعادة الترتيب**: تحريك الصور لليسار واليمين
- **الحذف**: حذف الصور من القائمة والخادم
- **المعاينة**: عرض مصغر للصور مع معلومات الملف

### 🔒 الأمان
- **مصادقة JWT**: جميع عمليات الرفع والحذف محمية
- **التحقق من النوع**: قبول الصور فقط
- **مسارات آمنة**: حفظ الصور في مجلد محدد فقط

## الملفات الجديدة

### 1. API Endpoints

#### `/api/upload-image.ts`
- **الوظيفة**: رفع الصور إلى الخادم
- **الطريقة**: POST
- **المصادقة**: مطلوبة (JWT)
- **المدخلات**: ملف صورة عبر FormData
- **المخرجات**: رابط الصورة المرفوعة

```typescript
// مثال على الاستجابة
{
  "success": true,
  "message": "Image uploaded successfully",
  "messageAr": "تم رفع الصورة بنجاح",
  "imageUrl": "/uploads/hero-1234567890-abc123.jpg",
  "fileName": "hero-1234567890-abc123.jpg"
}
```

#### `/api/delete-image.ts`
- **الوظيفة**: حذف الصور من الخادم
- **الطريقة**: DELETE
- **المصادقة**: مطلوبة (JWT)
- **المدخلات**: رابط الصورة في body
- **المخرجات**: تأكيد الحذف

```typescript
// مثال على الطلب
{
  "imageUrl": "/uploads/hero-1234567890-abc123.jpg"
}
```

### 2. React Components

#### `HeroImageUpload.tsx`
مكون React متكامل لإدارة صور الهيرو:

**الخصائص (Props):**
- `images`: مصفوفة روابط الصور الحالية
- `onImagesChange`: دالة callback عند تغيير الصور
- `maxImages`: الحد الأقصى لعدد الصور (افتراضي: 5)

**الميزات:**
- رفع متعدد مع شريط تقدم
- معاينة الصور مع أزرار التحكم
- إعادة ترتيب الصور
- حذف الصور (محلياً ومن الخادم)
- رسائل خطأ واضحة
- نصائح للمستخدم

## كيفية الاستخدام

### 1. في صفحة الإعدادات

اذهب إلى:
```
http://localhost:3000/admin/settings
```

ثم اختر تبويب "صور الهيرو"

### 2. رفع صور جديدة

1. اضغط على زر "إضافة صور"
2. اختر صورة أو عدة صور من الكمبيوتر
3. انتظر انتهاء الرفع (ستظهر شريط التقدم)
4. ستظهر الصور في القائمة فوراً

### 3. إدارة الصور

- **إعادة الترتيب**: استخدم أزرار الأسهم لتحريك الصور
- **الحذف**: اضغط على أيقونة سلة المهملات
- **المعاينة**: مرر الماوس على الصورة لرؤية أزرار التحكم

### 4. حفظ التغييرات

بعد إضافة أو تعديل الصور، اضغط على "حفظ الإعدادات" لحفظ التغييرات في قاعدة البيانات.

## هيكل الملفات

```
public/
├── uploads/                    # مجلد الصور المرفوعة
│   ├── hero-1234567890-abc.jpg # صور الهيرو
│   └── hero-1234567890-def.png
└── images/
    └── placeholder.jpg         # صورة بديلة عند فشل التحميل

src/
├── pages/api/
│   ├── upload-image.ts         # API رفع الصور
│   └── delete-image.ts         # API حذف الصور
├── components/admin/
│   └── HeroImageUpload.tsx     # مكون إدارة الصور
└── pages/admin/
    └── settings.tsx            # صفحة الإعدادات (محدثة)
```

## تسمية الملفات

الصور المرفوعة تحصل على أسماء فريدة:
```
hero-{timestamp}-{randomString}.{extension}
```

مثال:
```
hero-1703123456789-abc123def.jpg
```

## معالجة الأخطاء

### أخطاء الرفع
- **حجم كبير**: "الملف كبير جداً. الحد الأقصى 10MB"
- **نوع خاطئ**: "الملف ليس صورة صالحة"
- **عدد زائد**: "يمكن رفع 5 صور كحد أقصى"
- **خطأ شبكة**: "خطأ في الشبكة"

### أخطاء الحذف
- **ملف غير موجود**: "ملف الصورة غير موجود"
- **رابط خاطئ**: "رابط الصورة غير صحيح"
- **خطأ صلاحيات**: "لا توجد صلاحيات كافية"

## الأمان والحماية

### 1. المصادقة
- جميع عمليات الرفع والحذف تتطلب JWT token صحيح
- التحقق من صحة المستخدم قبل أي عملية

### 2. التحقق من الملفات
- قبول الصور فقط (MIME type checking)
- حد أقصى لحجم الملف (10MB)
- تسمية آمنة للملفات

### 3. مسارات آمنة
- حفظ الصور في مجلد محدد فقط (`public/uploads`)
- منع الوصول لمجلدات أخرى
- التحقق من صحة المسارات

## الأداء والتحسين

### 1. رفع متوازي
- رفع عدة صور في نفس الوقت
- شريط تقدم منفصل لكل صورة

### 2. معالجة الذاكرة
- استخدام streams لمعالجة الملفات الكبيرة
- تنظيف الملفات المؤقتة تلقائياً

### 3. تحسين الشبكة
- ضغط الصور (يمكن إضافته لاحقاً)
- تحسين أحجام الصور للويب

## المتطلبات التقنية

### Dependencies
```json
{
  "formidable": "^3.5.4",
  "@types/formidable": "^3.4.5",
  "jsonwebtoken": "^9.0.2"
}
```

### صلاحيات النظام
- صلاحية كتابة في مجلد `public/uploads`
- صلاحية حذف الملفات

## استكشاف الأخطاء

### إذا لم يعمل الرفع:
1. تحقق من وجود مجلد `public/uploads`
2. تحقق من صلاحيات الكتابة
3. تحقق من حجم الملف (أقل من 10MB)
4. تحقق من نوع الملف (صورة)
5. تحقق من المصادقة (JWT token)

### إذا لم تظهر الصور:
1. تحقق من مسار الصورة
2. تحقق من وجود الملف في `public/uploads`
3. تحقق من console المتصفح للأخطاء

### إذا لم يعمل الحذف:
1. تحقق من المصادقة
2. تحقق من وجود الملف
3. تحقق من صلاحيات الحذف

## التطوير المستقبلي

### ميزات مقترحة:
- **ضغط الصور**: تقليل حجم الصور تلقائياً
- **تحسين الأبعاد**: تغيير حجم الصور للأبعاد المطلوبة
- **معاينة أكبر**: نافذة معاينة مكبرة للصور
- **السحب والإفلات**: إعادة ترتيب الصور بالسحب
- **تحميل مجمع**: رفع مجلد كامل من الصور
- **تنسيقات متقدمة**: دعم WebP و AVIF

النظام الآن جاهز للاستخدام ويوفر تجربة سلسة لإدارة صور الهيرو! 🎉
