#!/usr/bin/env node

const https = require('https');
const http = require('http');

// قائمة User-Agent للعملاء الخارجيين المختلفين
const USER_AGENTS = [
  'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  'Mozilla/5.0 (compatible; Bingbot/2.0; +http://www.bing.com/bingbot.htm)',
  'Mozilla/5.0 (compatible; ChatGPT-User/1.0; +https://openai.com/bot)',
  'Mozilla/5.0 (compatible; GPTBot/1.0; +https://openai.com/gptbot)',
  'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)',
  'Twitterbot/1.0',
  'LinkedInBot/1.0 (compatible; Mozilla/5.0; Apache-HttpClient +http://www.linkedin.com/)',
  'WhatsApp/2.19.81 A',
  'TelegramBot (like TwitterBot)',
  'Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)',
  'Mozilla/5.0 (compatible; DuckDuckBot-Https/1.1; https://duckduckgo.com/duckduckbot)',
  'curl/7.68.0',
  'PostmanRuntime/7.28.0'
];

// قائمة المسارات للاختبار
const TEST_PATHS = [
  '/en/product/RH02636',
  '/ar/product/RH02636',
  '/api/products/RH02636',
  '/api/health',
  '/en/products',
  '/ar/products'
];

const BASE_URL = 'https://droobhajer.com';

function makeRequest(url, userAgent) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const options = {
      method: 'GET',
      headers: {
        'User-Agent': userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      timeout: 15000
    };

    const startTime = Date.now();
    
    const req = protocol.request(url, options, (res) => {
      const duration = Date.now() - startTime;
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          duration,
          size: data.length,
          userAgent,
          url
        });
      });
    });

    req.on('error', (error) => {
      const duration = Date.now() - startTime;
      reject({
        error: error.message,
        duration,
        userAgent,
        url
      });
    });

    req.on('timeout', () => {
      req.destroy();
      const duration = Date.now() - startTime;
      reject({
        error: 'Request timeout',
        duration,
        userAgent,
        url
      });
    });

    req.end();
  });
}

async function testPath(path, userAgent) {
  const url = `${BASE_URL}${path}`;
  
  try {
    const result = await makeRequest(url, userAgent);
    
    const status = result.statusCode >= 200 && result.statusCode < 300 ? '✅' : 
                  result.statusCode >= 400 && result.statusCode < 500 ? '⚠️' : '❌';
    
    console.log(`${status} ${result.statusCode} | ${result.duration}ms | ${path} | ${userAgent.substring(0, 50)}...`);
    
    return result;
  } catch (error) {
    console.log(`❌ ERROR | ${error.duration}ms | ${path} | ${userAgent.substring(0, 50)}... | ${error.error}`);
    return error;
  }
}

async function runTests() {
  console.log('🚀 Starting external client tests...\n');
  console.log('Status | Code | Time | Path | User-Agent');
  console.log('-------|------|------|------|------------');
  
  const results = [];
  
  for (const path of TEST_PATHS) {
    console.log(`\n📍 Testing path: ${path}`);
    
    for (const userAgent of USER_AGENTS) {
      const result = await testPath(path, userAgent);
      results.push(result);
      
      // تأخير قصير بين الطلبات
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  // تحليل النتائج
  console.log('\n📊 Test Summary:');
  console.log('================');
  
  const successful = results.filter(r => r.statusCode >= 200 && r.statusCode < 300);
  const clientErrors = results.filter(r => r.statusCode >= 400 && r.statusCode < 500);
  const serverErrors = results.filter(r => r.statusCode >= 500);
  const networkErrors = results.filter(r => r.error);
  
  console.log(`✅ Successful: ${successful.length}`);
  console.log(`⚠️  Client Errors (4xx): ${clientErrors.length}`);
  console.log(`❌ Server Errors (5xx): ${serverErrors.length}`);
  console.log(`🔌 Network Errors: ${networkErrors.length}`);
  console.log(`📊 Total Requests: ${results.length}`);
  
  if (successful.length > 0) {
    const avgDuration = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
    console.log(`⏱️  Average Response Time: ${Math.round(avgDuration)}ms`);
  }
  
  // عرض الأخطاء إن وجدت
  if (serverErrors.length > 0) {
    console.log('\n❌ Server Errors (5xx):');
    serverErrors.forEach(error => {
      console.log(`   ${error.url} | ${error.userAgent.substring(0, 30)}...`);
    });
  }
  
  if (networkErrors.length > 0) {
    console.log('\n🔌 Network Errors:');
    networkErrors.forEach(error => {
      console.log(`   ${error.url} | ${error.error} | ${error.userAgent.substring(0, 30)}...`);
    });
  }
  
  console.log('\n✅ Test completed!');
}

// تشغيل الاختبارات
runTests().catch(console.error);
