import React from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import Image from 'next/image';
import Link from 'next/link';
import { Locale } from '../../lib/i18n';
import { ProductWithDetails, Category } from '../../types/mysql-database';
import { getProductsWithDetailsLimited, getCategories } from '../../lib/mysql-database';
import { generateProductUrl } from '../../utils/generateSlug';

interface HomePageProps {
  locale: Locale;
  categories: Category[];
  featuredProducts: ProductWithDetails[];
}

const HomePage: React.FC<HomePageProps> = ({
  locale,
  categories,
  featuredProducts
}) => {
  const router = useRouter();

  if (router.isFallback) {
    return <div>{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</div>;
  }

  return (
    <>
      {/* SEO Tags */}
      <Head>
        <title>{locale === 'ar' ? 'دروب هجر - معدات فندقية احترافية' : 'DROOB HAJER - Professional Hotel Equipment'}</title>
        <meta name="description" content={locale === 'ar' ? 'متخصصون في توفير أفضل معدات الفنادق والبوفيه عالية الجودة. حلول شاملة لجميع احتياجات الضيافة والمطاعم.' : 'Specialists in providing the best high-quality hotel and buffet equipment. Comprehensive solutions for all hospitality and restaurant needs.'} />
        <meta property="og:title" content={locale === 'ar' ? 'دروب هجر - معدات فندقية احترافية' : 'DROOB HAJER - Professional Hotel Equipment'} />
        <meta property="og:description" content={locale === 'ar' ? 'متخصصون في توفير أفضل معدات الفنادق والبوفيه عالية الجودة' : 'Specialists in providing the best high-quality hotel and buffet equipment'} />
        <meta property="og:url" content={`https://droobhajer.com/${locale}`} />
        <meta property="og:type" content="website" />
        <link rel="canonical" href={`https://droobhajer.com/${locale}`} />
      </Head>

      {/* Navigation */}
      <nav className="bg-white shadow-md sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <Link href={`/${locale}`} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">DH</span>
              </div>
              <div className="hidden sm:block">
                <div className="font-bold text-gray-900 text-lg">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </div>
                <div className="text-xs text-gray-600">
                  {locale === 'ar' ? 'معدات فندقية' : 'Hotel Equipment'}
                </div>
              </div>
            </Link>

            <div className="hidden md:flex items-center space-x-8">
              <Link href={`/${locale}`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'الرئيسية' : 'Home'}
              </Link>
              <Link href={`/${locale}/products`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'المنتجات' : 'Products'}
              </Link>
              <Link href={`/${locale}/contact`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'تواصل معنا' : 'Contact'}
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link href={locale === 'ar' ? '/en' : '/ar'} className="text-sm text-gray-600 hover:text-primary transition-colors">
                {locale === 'ar' ? 'EN' : 'العربية'}
              </Link>
              <a href="https://wa.me/966599252259" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors">
                <span className="text-lg">📱</span>
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary via-blue-600 to-purple-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">
            {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
          </h1>
          <p className="text-xl mb-8 opacity-90">
            {locale === 'ar' 
              ? 'التجهيزات الفندقية الاحترافية وحلول البوفيه المتطورة'
              : 'Professional Hotel Equipment & Advanced Buffet Solutions'
            }
          </p>
          <Link href={`/${locale}/products`} className="inline-block bg-white text-primary px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors">
            {locale === 'ar' ? 'تصفح المنتجات' : 'Browse Products'}
          </Link>
        </div>
      </section>

      {/* Categories Section */}
      {categories.length > 0 && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {locale === 'ar' ? 'فئات المنتجات' : 'Product Categories'}
              </h2>
              <p className="text-gray-600 text-lg">
                {locale === 'ar' 
                  ? 'اكتشف مجموعتنا الواسعة من معدات الفنادق والبوفيه'
                  : 'Discover our wide range of hotel and buffet equipment'
                }
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {categories.slice(0, 8).map((category) => (
                <Link key={category.id} href={`/${locale}/products?category=${category.id}`} className="bg-gray-50 rounded-xl p-6 text-center hover:shadow-lg transition-all hover:bg-white group">
                  {category.image_url && (
                    <div className="w-20 h-20 mx-auto mb-4 rounded-lg overflow-hidden">
                      <Image
                        src={category.image_url}
                        alt={locale === 'ar' ? category.name_ar : category.name}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform"
                      />
                    </div>
                  )}
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {locale === 'ar' ? category.name_ar : category.name}
                  </h3>
                  {category.description && (
                    <p className="text-sm text-gray-600">
                      {locale === 'ar' ? category.description_ar : category.description}
                    </p>
                  )}
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Featured Products Section */}
      {featuredProducts.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
              </h2>
              <p className="text-gray-600 text-lg">
                {locale === 'ar' 
                  ? 'أحدث وأفضل منتجاتنا عالية الجودة'
                  : 'Our latest and best high-quality products'
                }
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredProducts.map((product) => {
                const productTitle = locale === 'ar' ? product.title_ar : product.title;
                const productUrl = generateProductUrl(product, locale);
                
                return (
                  <Link key={product.id} href={productUrl} className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all group">
                    <div className="aspect-square bg-gray-100 overflow-hidden">
                      {product.images && product.images.length > 0 ? (
                        <Image
                          src={product.images[0].image_url}
                          alt={productTitle}
                          width={400}
                          height={400}
                          className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          <div className="text-center">
                            <div className="text-6xl mb-2">📷</div>
                            <p>{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="p-6">
                      <h3 className="font-bold text-gray-900 mb-2 text-lg">
                        {productTitle}
                      </h3>
                      
                      {product.description && (
                        <p className="text-gray-600 mb-4">
                          {locale === 'ar' ? product.description_ar : product.description}
                        </p>
                      )}

                      {product.price && (
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-xl font-bold text-primary">
                            {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                          </span>
                          <div className={`text-xs px-3 py-1 rounded-full ${
                            product.is_available 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {product.is_available 
                              ? (locale === 'ar' ? 'متوفر' : 'Available')
                              : (locale === 'ar' ? 'غير متوفر' : 'Out of Stock')
                            }
                          </div>
                        </div>
                      )}

                      <div className="flex space-x-3">
                        <div className="flex-1 bg-primary text-white text-center py-3 rounded-lg font-semibold">
                          {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                        </div>
                        <a
                          href={`https://wa.me/966599252259?text=${encodeURIComponent(`${locale === 'ar' ? 'مرحباً، أريد الاستفسار عن هذا المنتج:' : 'Hello, I want to inquire about this product:'} ${productTitle}`)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-14 h-12 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <span className="text-xl">📱</span>
                        </a>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        </section>
      )}

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-6">
            <h3 className="text-2xl font-bold mb-2">
              {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
            </h3>
            <p className="text-gray-400">
              {locale === 'ar' ? 'معدات فندقية احترافية' : 'Professional Hotel Equipment'}
            </p>
          </div>
          
          <div className="flex justify-center space-x-6 mb-6">
            <a href="https://wa.me/966599252259" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
              {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
            </a>
            <a href="tel:+966599252259" className="text-gray-400 hover:text-white transition-colors">
              {locale === 'ar' ? 'اتصال' : 'Call'}
            </a>
          </div>
          
          <div className="text-gray-400 text-sm">
            © 2024 {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}. 
            {locale === 'ar' ? ' جميع الحقوق محفوظة.' : ' All rights reserved.'}
          </div>
        </div>
      </footer>
    </>
  );
};

// ✅ هذا هو الحل الصحيح - getServerSideProps
export const getServerSideProps: GetServerSideProps = async (context) => {
  const { locale } = context.params!;

  try {
    console.log('🚀 SSR: Fetching homepage data from database...');
    
    // جلب البيانات مباشرة من قاعدة البيانات على السيرفر
    const [allCategories, featuredProducts] = await Promise.all([
      getCategories(),
      getProductsWithDetailsLimited(1, 6)
    ]);
    
    const categories = allCategories.filter((cat: Category) => cat.is_active);
    
    console.log(`✅ SSR: Fetched ${featuredProducts.length} products and ${categories.length} categories`);

    return {
      props: {
        locale: locale as Locale,
        categories,
        featuredProducts
      }
    };
  } catch (error) {
    console.error('❌ SSR: Error fetching homepage data:', error);
    
    return {
      props: {
        locale: locale as Locale,
        categories: [],
        featuredProducts: []
      }
    };
  }
};

export default HomePage;
