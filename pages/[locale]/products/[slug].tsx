import React from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import Image from 'next/image';
import Link from 'next/link';
import { Locale } from '../../../lib/i18n';
import { ProductWithDetails, Category } from '../../../types/mysql-database';
import { getProductWithDetails, getCategories } from '../../../lib/mysql-database';

interface ProductDetailPageProps {
  locale: Locale;
  product: ProductWithDetails;
  category: Category | null;
}

const ProductDetailPage: React.FC<ProductDetailPageProps> = ({
  locale,
  product,
  category
}) => {
  const router = useRouter();

  if (router.isFallback) {
    return <div>{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</div>;
  }

  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;

  return (
    <>
      {/* SEO Tags */}
      <Head>
        <title>{productTitle} - {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}</title>
        <meta name="description" content={productDescription || `${productTitle} - ${locale === 'ar' ? 'معدات فندقية احترافية عالية الجودة' : 'Professional high-quality hotel equipment'}`} />
        <meta property="og:title" content={`${productTitle} - ${locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}`} />
        <meta property="og:description" content={productDescription || `${productTitle} - ${locale === 'ar' ? 'معدات فندقية احترافية عالية الجودة' : 'Professional high-quality hotel equipment'}`} />
        <meta property="og:url" content={`https://droobhajer.com/${locale}/products/${router.query.slug}`} />
        <meta property="og:type" content="product" />
        {product.images && product.images.length > 0 && (
          <meta property="og:image" content={product.images[0].image_url} />
        )}
        <link rel="canonical" href={`https://droobhajer.com/${locale}/products/${router.query.slug}`} />
        
        {/* Product Schema */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Product",
              "name": productTitle,
              "description": productDescription,
              "brand": {
                "@type": "Brand",
                "name": "DROOB HAJER"
              },
              "category": category ? (locale === 'ar' ? category.name_ar : category.name) : undefined,
              "offers": {
                "@type": "Offer",
                "price": product.price,
                "priceCurrency": "SAR",
                "availability": product.is_available ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
                "seller": {
                  "@type": "Organization",
                  "name": "DROOB HAJER"
                }
              },
              "image": product.images?.map(img => img.image_url) || [],
              "additionalProperty": product.specifications?.map(spec => ({
                "@type": "PropertyValue",
                "name": locale === 'ar' ? spec.spec_key_ar : spec.spec_key,
                "value": locale === 'ar' ? spec.spec_value_ar : spec.spec_value
              })) || []
            })
          }}
        />
      </Head>

      {/* Navigation */}
      <nav className="bg-white shadow-md sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <Link href={`/${locale}`} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">DH</span>
              </div>
              <div className="hidden sm:block">
                <div className="font-bold text-gray-900 text-lg">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </div>
                <div className="text-xs text-gray-600">
                  {locale === 'ar' ? 'معدات فندقية' : 'Hotel Equipment'}
                </div>
              </div>
            </Link>

            <div className="hidden md:flex items-center space-x-8">
              <Link href={`/${locale}`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'الرئيسية' : 'Home'}
              </Link>
              <Link href={`/${locale}/products`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'المنتجات' : 'Products'}
              </Link>
              <Link href={`/${locale}/contact`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'تواصل معنا' : 'Contact'}
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link href={locale === 'ar' ? `/en/products/${router.query.slug}` : `/ar/products/${router.query.slug}`} className="text-sm text-gray-600 hover:text-primary transition-colors">
                {locale === 'ar' ? 'EN' : 'العربية'}
              </Link>
              <a href="https://wa.me/966599252259" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors">
                <span className="text-lg">📱</span>
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Breadcrumb */}
      <div className="bg-gray-50 py-4">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href={`/${locale}`} className="text-gray-600 hover:text-primary">
              {locale === 'ar' ? 'الرئيسية' : 'Home'}
            </Link>
            <span className="text-gray-400">/</span>
            <Link href={`/${locale}/products`} className="text-gray-600 hover:text-primary">
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </Link>
            {category && (
              <>
                <span className="text-gray-400">/</span>
                <Link href={`/${locale}/products?category=${category.id}`} className="text-gray-600 hover:text-primary">
                  {locale === 'ar' ? category.name_ar : category.name}
                </Link>
              </>
            )}
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">{productTitle}</span>
          </nav>
        </div>
      </div>

      {/* Product Details */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            
            {/* Product Images */}
            <div className="space-y-4">
              {product.images && product.images.length > 0 ? (
                <>
                  <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={product.images[0].image_url}
                      alt={productTitle}
                      width={600}
                      height={600}
                      className="w-full h-full object-contain"
                      priority
                    />
                  </div>
                  
                  {product.images.length > 1 && (
                    <div className="grid grid-cols-4 gap-2">
                      {product.images.slice(1, 5).map((image, index) => (
                        <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                          <Image
                            src={image.image_url}
                            alt={`${productTitle} - ${locale === 'ar' ? 'صورة' : 'Image'} ${index + 2}`}
                            width={150}
                            height={150}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <div className="text-6xl mb-2">📷</div>
                    <p>{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Product Information */}
            <div className="space-y-6">
              {/* Title and Category */}
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{productTitle}</h1>
                {category && (
                  <h2 className="text-lg font-semibold text-gray-700">
                    {locale === 'ar'
                      ? `${category.name_ar} - معدات فنادق احترافية عالية الجودة`
                      : `${category.name} - Professional High Quality Hotel Equipment`
                    }
                  </h2>
                )}
              </div>

              {/* Price */}
              {product.price && (
                <div className="border-b border-gray-200 pb-6">
                  <div className="flex items-center space-x-4">
                    <span className="text-4xl font-bold text-primary">
                      {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                    </span>
                    {product.original_price && product.original_price > product.price && (
                      <>
                        <span className="text-xl text-gray-400 line-through">
                          {product.original_price} {locale === 'ar' ? 'ريال' : 'SAR'}
                        </span>
                        <span className="bg-red-500 text-white text-sm font-bold px-3 py-1 rounded-full">
                          {Math.round(((product.original_price - product.price) / product.original_price) * 100)}% {locale === 'ar' ? 'خصم' : 'OFF'}
                        </span>
                      </>
                    )}
                  </div>
                  <div className="text-sm text-gray-500 mt-2">
                    {locale === 'ar' ? 'شامل ضريبة القيمة المضافة' : 'VAT included'}
                  </div>
                </div>
              )}

              {/* Description */}
              {productDescription && (
                <div className="border-b border-gray-200 pb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-primary/20">
                    {locale === 'ar' ? 'وصف المنتج' : 'Product Description'}
                  </h2>
                  <p className="text-gray-700 leading-relaxed text-lg">{productDescription}</p>
                </div>
              )}

              {/* Features */}
              {product.features && product.features.length > 0 && (
                <div className="border-b border-gray-200 pb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-green-500/20">
                    {locale === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
                  </h2>
                  <ul className="space-y-3">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mt-0.5">
                          <span className="text-white text-sm">✓</span>
                        </div>
                        <span className="text-gray-700 text-lg leading-relaxed">
                          {locale === 'ar' ? feature.feature_text_ar : feature.feature_text}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Specifications */}
              {product.specifications && product.specifications.length > 0 && (
                <div className="border-b border-gray-200 pb-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-blue-500/20">
                    {locale === 'ar' ? 'المواصفات الأساسية' : 'Key Specifications'}
                  </h2>
                  <div className="space-y-3">
                    {product.specifications.map((spec, index) => (
                      <div key={index} className="flex justify-between items-center py-3 border-b border-gray-200 last:border-b-0">
                        <span className="font-semibold text-gray-800 text-lg">
                          {locale === 'ar' ? spec.spec_key_ar : spec.spec_key}:
                        </span>
                        <span className="text-gray-600 text-lg">
                          {locale === 'ar' ? spec.spec_value_ar : spec.spec_value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Contact Buttons */}
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="text-center space-y-4">
                  <p className="text-gray-600 text-lg">
                    {locale === 'ar' ? 'للطلب والاستفسار:' : 'For orders and inquiries:'}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <a
                      href={`https://wa.me/966599252259?text=${encodeURIComponent(`${locale === 'ar' ? 'مرحباً، أريد الاستفسار عن هذا المنتج:' : 'Hello, I want to inquire about this product:'} ${productTitle}`)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-green-500 text-white px-6 py-4 rounded-lg font-bold text-lg hover:bg-green-600 transition-colors flex items-center justify-center space-x-2"
                    >
                      <span className="text-2xl">📱</span>
                      <span>{locale === 'ar' ? 'واتساب' : 'WhatsApp'}</span>
                    </a>
                    <a
                      href="tel:+966599252259"
                      className="flex-1 bg-primary text-white px-6 py-4 rounded-lg font-bold text-lg hover:bg-primary-dark transition-colors flex items-center justify-center space-x-2"
                    >
                      <span className="text-2xl">📞</span>
                      <span>{locale === 'ar' ? 'اتصال' : 'Call'}</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-6">
            <h3 className="text-2xl font-bold mb-2">
              {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
            </h3>
            <p className="text-gray-400">
              {locale === 'ar' ? 'معدات فندقية احترافية' : 'Professional Hotel Equipment'}
            </p>
          </div>
          
          <div className="flex justify-center space-x-6 mb-6">
            <a href="https://wa.me/966599252259" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
              {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
            </a>
            <a href="tel:+966599252259" className="text-gray-400 hover:text-white transition-colors">
              {locale === 'ar' ? 'اتصال' : 'Call'}
            </a>
          </div>
          
          <div className="text-gray-400 text-sm">
            © 2024 {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}. 
            {locale === 'ar' ? ' جميع الحقوق محفوظة.' : ' All rights reserved.'}
          </div>
        </div>
      </footer>
    </>
  );
};

// ✅ SSR لتفاصيل المنتج
export const getServerSideProps: GetServerSideProps = async (context) => {
  const { locale, slug } = context.params!;

  try {
    console.log('🚀 SSR: Fetching product details from database...');
    
    // استخراج ID المنتج من slug
    const productId = slug?.toString().split('-').pop();
    
    if (!productId) {
      return { notFound: true };
    }

    // جلب تفاصيل المنتج مباشرة من قاعدة البيانات
    const product = await getProductWithDetails(productId);
    
    if (!product) {
      return { notFound: true };
    }

    // جلب معلومات الفئة
    let category = null;
    if (product.category_id) {
      const categories = await getCategories();
      category = categories.find(cat => cat.id === product.category_id) || null;
    }
    
    console.log(`✅ SSR: Fetched product ${product.id} with ${product.images?.length || 0} images`);

    return {
      props: {
        locale: locale as Locale,
        product,
        category
      }
    };
  } catch (error) {
    console.error('❌ SSR: Error fetching product details:', error);
    return { notFound: true };
  }
};

export default ProductDetailPage;
