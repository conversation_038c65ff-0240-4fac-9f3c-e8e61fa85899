import React from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import Image from 'next/image';
import Link from 'next/link';
import { Locale } from '../../../lib/i18n';
import { ProductWithDetails, Category } from '../../../types/mysql-database';
import { getProductsWithDetailsLimited, getCategories } from '../../../lib/mysql-database';
import { generateProductUrl } from '../../../utils/generateSlug';

interface ProductsPageProps {
  locale: Locale;
  products: ProductWithDetails[];
  categories: Category[];
  selectedCategory?: string;
}

const ProductsPage: React.FC<ProductsPageProps> = ({
  locale,
  products,
  categories,
  selectedCategory
}) => {
  const router = useRouter();

  if (router.isFallback) {
    return <div>{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</div>;
  }

  const selectedCategoryData = selectedCategory 
    ? categories.find(cat => cat.id.toString() === selectedCategory)
    : null;

  return (
    <>
      {/* SEO Tags */}
      <Head>
        <title>
          {selectedCategoryData 
            ? `${locale === 'ar' ? selectedCategoryData.name_ar : selectedCategoryData.name} - ${locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}`
            : `${locale === 'ar' ? 'المنتجات - دروب هجر' : 'Products - DROOB HAJER'}`
          }
        </title>
        <meta name="description" content={
          selectedCategoryData 
            ? (locale === 'ar' ? selectedCategoryData.description_ar : selectedCategoryData.description)
            : (locale === 'ar' ? 'تصفح مجموعتنا الواسعة من معدات الفنادق والبوفيه عالية الجودة' : 'Browse our wide range of high-quality hotel and buffet equipment')
        } />
        <meta property="og:title" content={
          selectedCategoryData 
            ? `${locale === 'ar' ? selectedCategoryData.name_ar : selectedCategoryData.name} - ${locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}`
            : `${locale === 'ar' ? 'المنتجات - دروب هجر' : 'Products - DROOB HAJER'}`
        } />
        <meta property="og:description" content={
          selectedCategoryData 
            ? (locale === 'ar' ? selectedCategoryData.description_ar : selectedCategoryData.description)
            : (locale === 'ar' ? 'تصفح مجموعتنا الواسعة من معدات الفنادق والبوفيه عالية الجودة' : 'Browse our wide range of high-quality hotel and buffet equipment')
        } />
        <meta property="og:url" content={`https://droobhajer.com/${locale}/products${selectedCategory ? `?category=${selectedCategory}` : ''}`} />
        <meta property="og:type" content="product.group" />
        <link rel="canonical" href={`https://droobhajer.com/${locale}/products${selectedCategory ? `?category=${selectedCategory}` : ''}`} />
      </Head>

      {/* Navigation */}
      <nav className="bg-white shadow-md sticky top-0 z-50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <Link href={`/${locale}`} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">DH</span>
              </div>
              <div className="hidden sm:block">
                <div className="font-bold text-gray-900 text-lg">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </div>
                <div className="text-xs text-gray-600">
                  {locale === 'ar' ? 'معدات فندقية' : 'Hotel Equipment'}
                </div>
              </div>
            </Link>

            <div className="hidden md:flex items-center space-x-8">
              <Link href={`/${locale}`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'الرئيسية' : 'Home'}
              </Link>
              <Link href={`/${locale}/products`} className="text-primary font-medium">
                {locale === 'ar' ? 'المنتجات' : 'Products'}
              </Link>
              <Link href={`/${locale}/contact`} className="text-gray-700 hover:text-primary font-medium transition-colors">
                {locale === 'ar' ? 'تواصل معنا' : 'Contact'}
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              <Link href={locale === 'ar' ? '/en/products' : '/ar/products'} className="text-sm text-gray-600 hover:text-primary transition-colors">
                {locale === 'ar' ? 'EN' : 'العربية'}
              </Link>
              <a href="https://wa.me/966599252259" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors">
                <span className="text-lg">📱</span>
              </a>
            </div>
          </div>
        </div>
      </nav>

      {/* Breadcrumb */}
      <div className="bg-gray-50 py-4">
        <div className="container mx-auto px-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link href={`/${locale}`} className="text-gray-600 hover:text-primary">
              {locale === 'ar' ? 'الرئيسية' : 'Home'}
            </Link>
            <span className="text-gray-400">/</span>
            <Link href={`/${locale}/products`} className="text-gray-600 hover:text-primary">
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </Link>
            {selectedCategoryData && (
              <>
                <span className="text-gray-400">/</span>
                <span className="text-gray-900 font-medium">
                  {locale === 'ar' ? selectedCategoryData.name_ar : selectedCategoryData.name}
                </span>
              </>
            )}
          </nav>
        </div>
      </div>

      {/* Page Header */}
      <section className="bg-primary py-12">
        <div className="container mx-auto px-4">
          <div className="text-center text-white">
            <h1 className="text-4xl font-bold mb-4">
              {selectedCategoryData 
                ? (locale === 'ar' ? selectedCategoryData.name_ar : selectedCategoryData.name)
                : (locale === 'ar' ? 'جميع المنتجات' : 'All Products')
              }
            </h1>
            <p className="text-xl opacity-90">
              {selectedCategoryData 
                ? (locale === 'ar' ? selectedCategoryData.description_ar : selectedCategoryData.description)
                : (locale === 'ar' ? 'اكتشف مجموعتنا الواسعة من معدات الفنادق والبوفيه عالية الجودة' : 'Discover our wide range of high-quality hotel and buffet equipment')
              }
            </p>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      {categories.length > 0 && (
        <section className="py-8 bg-white border-b">
          <div className="container mx-auto px-4">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {locale === 'ar' ? 'تصفية حسب الفئة' : 'Filter by Category'}
            </h2>
            <div className="flex flex-wrap gap-3">
              <Link
                href={`/${locale}/products`}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  !selectedCategory 
                    ? 'bg-primary text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {locale === 'ar' ? 'جميع الفئات' : 'All Categories'}
              </Link>
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/${locale}/products?category=${category.id}`}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    selectedCategory === category.id.toString()
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {locale === 'ar' ? category.name_ar : category.name}
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Products Grid */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          {products.length > 0 ? (
            <>
              <div className="mb-6">
                <p className="text-gray-600">
                  {locale === 'ar' ? `عرض ${products.length} منتج` : `Showing ${products.length} products`}
                </p>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {products.map((product) => {
                  const productTitle = locale === 'ar' ? product.title_ar : product.title;
                  const productUrl = generateProductUrl(product, locale);
                  
                  return (
                    <Link key={product.id} href={productUrl} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all group">
                      <div className="aspect-square bg-gray-100 overflow-hidden">
                        {product.images && product.images.length > 0 ? (
                          <Image
                            src={product.images[0].image_url}
                            alt={productTitle}
                            width={300}
                            height={300}
                            className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            <div className="text-center">
                              <div className="text-4xl mb-2">📷</div>
                              <p className="text-sm">{locale === 'ar' ? 'لا توجد صورة' : 'No Image'}</p>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                          {productTitle}
                        </h3>
                        
                        {product.description && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {locale === 'ar' ? product.description_ar : product.description}
                          </p>
                        )}

                        {product.price && (
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg font-bold text-primary">
                                {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                              </span>
                              {product.original_price && product.original_price > product.price && (
                                <span className="text-sm text-gray-400 line-through">
                                  {product.original_price}
                                </span>
                              )}
                            </div>
                            
                            <div className={`text-xs px-2 py-1 rounded-full ${
                              product.is_available 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {product.is_available 
                                ? (locale === 'ar' ? 'متوفر' : 'Available')
                                : (locale === 'ar' ? 'غير متوفر' : 'Out of Stock')
                              }
                            </div>
                          </div>
                        )}

                        <div className="flex space-x-2">
                          <div className="flex-1 bg-primary text-white text-center py-2 rounded-lg text-sm font-semibold">
                            {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                          </div>
                          <a
                            href={`https://wa.me/966599252259?text=${encodeURIComponent(`${locale === 'ar' ? 'مرحباً، أريد الاستفسار عن هذا المنتج:' : 'Hello, I want to inquire about this product:'} ${productTitle}`)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-12 h-10 bg-green-500 text-white rounded-lg flex items-center justify-center hover:bg-green-600 transition-colors"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <span className="text-lg">📱</span>
                          </a>
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📦</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {locale === 'ar' ? 'لا توجد منتجات' : 'No Products Found'}
              </h3>
              <p className="text-gray-600">
                {locale === 'ar' 
                  ? 'لم يتم العثور على منتجات في هذه الفئة'
                  : 'No products found in this category'
                }
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4 text-center">
          <div className="mb-6">
            <h3 className="text-2xl font-bold mb-2">
              {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
            </h3>
            <p className="text-gray-400">
              {locale === 'ar' ? 'معدات فندقية احترافية' : 'Professional Hotel Equipment'}
            </p>
          </div>
          
          <div className="flex justify-center space-x-6 mb-6">
            <a href="https://wa.me/966599252259" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
              {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
            </a>
            <a href="tel:+966599252259" className="text-gray-400 hover:text-white transition-colors">
              {locale === 'ar' ? 'اتصال' : 'Call'}
            </a>
          </div>
          
          <div className="text-gray-400 text-sm">
            © 2024 {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}. 
            {locale === 'ar' ? ' جميع الحقوق محفوظة.' : ' All rights reserved.'}
          </div>
        </div>
      </footer>
    </>
  );
};

// ✅ SSR مع فلترة حسب الفئة
export const getServerSideProps: GetServerSideProps = async (context) => {
  const { locale, category } = context.query;

  try {
    console.log('🚀 SSR: Fetching products data from database...');
    
    // جلب البيانات مباشرة من قاعدة البيانات على السيرفر
    const [allCategories, allProducts] = await Promise.all([
      getCategories(),
      getProductsWithDetailsLimited(1, 50) // جلب 50 منتج
    ]);
    
    const categories = allCategories.filter((cat: Category) => cat.is_active);
    
    // فلترة المنتجات حسب الفئة إذا تم تحديدها
    let products = allProducts;
    if (category) {
      products = allProducts.filter(product => product.category_id?.toString() === category);
    }
    
    console.log(`✅ SSR: Fetched ${products.length} products and ${categories.length} categories`);

    return {
      props: {
        locale: locale as Locale,
        products,
        categories,
        selectedCategory: category || null
      }
    };
  } catch (error) {
    console.error('❌ SSR: Error fetching products data:', error);
    
    return {
      props: {
        locale: locale as Locale,
        products: [],
        categories: [],
        selectedCategory: null
      }
    };
  }
};

export default ProductsPage;
