#!/bin/bash

# 🔐 تحديث كلمة مرور قاعدة البيانات
# مشروع دروب هاجر

echo "🔐 تحديث كلمة مرور قاعدة البيانات..."

# طلب كلمة المرور الجديدة
read -s -p "أدخل كلمة مرور قاعدة البيانات الجديدة: " DB_PASSWORD
echo ""

if [ -z "$DB_PASSWORD" ]; then
    echo "❌ كلمة المرور لا يمكن أن تكون فارغة!"
    exit 1
fi

# تحديث ملف .env.local
if [ -f ".env.local" ]; then
    # إنشاء نسخة احتياطية
    cp .env.local .env.local.backup
    
    # تحديث كلمة المرور
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env.local
    
    echo "✅ تم تحديث كلمة مرور قاعدة البيانات في .env.local"
else
    echo "❌ ملف .env.local غير موجود!"
    exit 1
fi

# تحديث ملف .env.production إذا كان موجود
if [ -f ".env.production" ]; then
    cp .env.production .env.production.backup
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env.production
    echo "✅ تم تحديث كلمة مرور قاعدة البيانات في .env.production"
fi

echo ""
echo "🔄 لتطبيق التغييرات على السيرفر:"
echo "1. ارفع ملف .env.local المحدث إلى السيرفر"
echo "2. أعد تشغيل التطبيق: pm2 restart droobhajer"
echo ""
echo "💾 تم حفظ نسخة احتياطية في: .env.local.backup"
