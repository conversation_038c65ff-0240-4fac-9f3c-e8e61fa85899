# إعدادات خاصة بملفات sitemap و robots
<Files "sitemap.xml">
    Header set Content-Type "application/xml; charset=UTF-8"
    Header set Cache-Control "public, max-age=3600"
</Files>

<Files "robots.txt">
    Header set Content-Type "text/plain; charset=UTF-8"
    Header set Cache-Control "public, max-age=86400"
</Files>

# السماح بالوصول لجميع الملفات في public
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # السماح بالوصول المباشر للملفات الثابتة
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule .* - [L]
</IfModule>

# ضغط ملفات XML
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# إعدادات خاصة بالأيقونات لضمان ظهورها في محركات البحث
<IfModule mod_mime.c>
    AddType image/x-icon .ico
    AddType image/png .png
    AddType image/svg+xml .svg
</IfModule>

# إعدادات Cache للأيقونات
<IfModule mod_expires.c>
    ExpiresActive On

    # الأيقونات - cache لمدة سنة
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# إعدادات Headers للأيقونات المفضلة
<Files "icons8-circled-d-ios-17-filled-16.png">
    Header set Content-Type "image/png"
    Header set Cache-Control "public, max-age=31536000, immutable"
    Header set Access-Control-Allow-Origin "*"
</Files>

<Files "icons8-circled-d-ios-17-filled-32.png">
    Header set Content-Type "image/png"
    Header set Cache-Control "public, max-age=31536000, immutable"
    Header set Access-Control-Allow-Origin "*"
</Files>
