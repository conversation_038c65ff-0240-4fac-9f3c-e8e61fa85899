<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار HTTPS - دروب هجر</title>
    
    <!-- الأيقونات المفضلة -->
    <link rel="icon" type="image/png" sizes="16x16" href="/icons8-circled-d-ios-17-filled-16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons8-circled-d-ios-17-filled-32.png">
    <link rel="shortcut icon" href="/icons8-circled-d-ios-17-filled-32.png">
    
    <!-- Meta tags محسنة -->
    <meta name="description" content="صفحة اختبار HTTPS لموقع دروب هجر">
    <meta name="robots" content="index, follow">
    
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 900px;
            margin: 0 auto;
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            display: block;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        h1 {
            color: #3B82F6;
            margin: 0;
            font-size: 2.5em;
        }
        .status {
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            border: 2px solid #28a745;
            color: #155724;
        }
        .status.warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            color: #856404;
        }
        .status.error {
            background: #f8d7da;
            border: 2px solid #dc3545;
            color: #721c24;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .test-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        .test-item h3 {
            color: #3B82F6;
            margin-top: 0;
        }
        .check-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .check-result.pass {
            background: #d4edda;
            color: #155724;
        }
        .check-result.fail {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3B82F6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px 5px;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #2563eb;
        }
        .info-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 5px solid #2196f3;
        }
        .steps {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #3B82F6;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="/icons8-circled-d-ios-17-filled-32.png" alt="شعار دروب هجر" class="logo">
            <h1>اختبار HTTPS</h1>
            <p>التحقق من إعدادات الأمان والشهادة</p>
        </div>
        
        <div id="https-status" class="status">
            <span id="status-text">جاري فحص حالة HTTPS...</span>
        </div>
        
        <div class="test-grid">
            <div class="test-item">
                <h3>🔒 حالة البروتوكول</h3>
                <div id="protocol-check" class="check-result">جاري الفحص...</div>
                <p>يتحقق من استخدام HTTPS بدلاً من HTTP</p>
            </div>
            
            <div class="test-item">
                <h3>🛡️ شهادة SSL</h3>
                <div id="ssl-check" class="check-result">جاري الفحص...</div>
                <p>يتحقق من صحة شهادة SSL</p>
            </div>
            
            <div class="test-item">
                <h3>🔐 HSTS</h3>
                <div id="hsts-check" class="check-result">جاري الفحص...</div>
                <p>يتحقق من وجود Strict Transport Security</p>
            </div>
            
            <div class="test-item">
                <h3>🚫 Mixed Content</h3>
                <div id="mixed-content-check" class="check-result">جاري الفحص...</div>
                <p>يتحقق من عدم وجود محتوى مختلط</p>
            </div>
        </div>
        
        <div class="info-box">
            <h3>ما هو HTTPS؟</h3>
            <p>HTTPS (HyperText Transfer Protocol Secure) هو بروتوكول آمن يحمي البيانات المتبادلة بين المتصفح والخادم من خلال التشفير. جوجل يتطلب HTTPS لتصنيف الصفحات كـ "جيدة" في تجربة المستخدم.</p>
        </div>
        
        <div class="steps">
            <h2>خطوات حل مشكلة HTTPS:</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>الحصول على شهادة SSL</strong>
                <p>احصل على شهادة SSL من Let's Encrypt (مجانية) أو من مزود الاستضافة</p>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>تثبيت الشهادة</strong>
                <p>قم بتثبيت الشهادة على الخادم وتحديث إعدادات Nginx/Apache</p>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>إعادة التوجيه</strong>
                <p>أضف قواعد إعادة التوجيه من HTTP إلى HTTPS</p>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>تحديث الروابط</strong>
                <p>تأكد من أن جميع الروابط الداخلية تستخدم HTTPS</p>
            </div>
            
            <div class="step">
                <span class="step-number">5</span>
                <strong>اختبار الموقع</strong>
                <p>اختبر الموقع للتأكد من عمل HTTPS بشكل صحيح</p>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="/" class="btn">العودة للموقع الرئيسي</a>
            <a href="/icon-test.html" class="btn">اختبار الأيقونات</a>
        </div>
    </div>
    
    <script>
        // فحص حالة HTTPS
        function checkHTTPS() {
            const protocol = window.location.protocol;
            const isHTTPS = protocol === 'https:';
            
            // فحص البروتوكول
            const protocolCheck = document.getElementById('protocol-check');
            if (isHTTPS) {
                protocolCheck.textContent = '✅ يتم استخدام HTTPS';
                protocolCheck.className = 'check-result pass';
            } else {
                protocolCheck.textContent = '❌ يتم استخدام HTTP (غير آمن)';
                protocolCheck.className = 'check-result fail';
            }
            
            // فحص HSTS
            const hstsCheck = document.getElementById('hsts-check');
            fetch(window.location.href, { method: 'HEAD' })
                .then(response => {
                    const hstsHeader = response.headers.get('strict-transport-security');
                    if (hstsHeader) {
                        hstsCheck.textContent = '✅ HSTS مفعل';
                        hstsCheck.className = 'check-result pass';
                    } else {
                        hstsCheck.textContent = '❌ HSTS غير مفعل';
                        hstsCheck.className = 'check-result fail';
                    }
                })
                .catch(() => {
                    hstsCheck.textContent = '⚠️ لا يمكن فحص HSTS';
                    hstsCheck.className = 'check-result fail';
                });
            
            // فحص Mixed Content
            const mixedContentCheck = document.getElementById('mixed-content-check');
            const hasInsecureContent = document.querySelectorAll('img[src^="http:"], script[src^="http:"], link[href^="http:"]').length > 0;
            
            if (!hasInsecureContent) {
                mixedContentCheck.textContent = '✅ لا يوجد محتوى مختلط';
                mixedContentCheck.className = 'check-result pass';
            } else {
                mixedContentCheck.textContent = '❌ يوجد محتوى مختلط';
                mixedContentCheck.className = 'check-result fail';
            }
            
            // فحص SSL
            const sslCheck = document.getElementById('ssl-check');
            if (isHTTPS) {
                sslCheck.textContent = '✅ شهادة SSL صالحة';
                sslCheck.className = 'check-result pass';
            } else {
                sslCheck.textContent = '❌ لا توجد شهادة SSL';
                sslCheck.className = 'check-result fail';
            }
            
            // تحديث الحالة العامة
            const statusDiv = document.getElementById('https-status');
            const statusText = document.getElementById('status-text');
            
            if (isHTTPS) {
                statusDiv.className = 'status success';
                statusText.textContent = '✅ الموقع يستخدم HTTPS بشكل صحيح';
            } else {
                statusDiv.className = 'status error';
                statusText.textContent = '❌ الموقع لا يستخدم HTTPS - يجب إصلاح هذه المشكلة';
            }
        }
        
        // تشغيل الفحص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', checkHTTPS);
    </script>
</body>
</html>
