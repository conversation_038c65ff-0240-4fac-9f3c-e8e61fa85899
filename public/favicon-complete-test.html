<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأيقونات الكامل - DROOB HAJER</title>
    
    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="57x57" href="/apple-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/apple-icon-60x60.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/apple-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-icon-76x76.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/apple-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/apple-icon-120x120.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/apple-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-icon-180x180.png" />
    <link rel="apple-touch-icon-precomposed" href="/apple-icon-precomposed.png" />
    
    <!-- Android Chrome Icons -->
    <link rel="icon" type="image/png" sizes="36x36" href="/android-icon-36x36.png" />
    <link rel="icon" type="image/png" sizes="48x48" href="/android-icon-48x48.png" />
    <link rel="icon" type="image/png" sizes="72x72" href="/android-icon-72x72.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/android-icon-96x96.png" />
    <link rel="icon" type="image/png" sizes="144x144" href="/android-icon-144x144.png" />
    <link rel="icon" type="image/png" sizes="192x192" href="/android-icon-192x192.png" />
    
    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#3B82F6" />
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    
    <!-- Theme Colors -->
    <meta name="theme-color" content="#3B82F6" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="DROOB HAJER" />
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #3B82F6;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .icon-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #3B82F6;
        }
        .icon-item img {
            max-width: 64px;
            max-height: 64px;
            margin-bottom: 10px;
        }
        .icon-item h3 {
            margin: 10px 0 5px 0;
            color: #3B82F6;
            font-size: 1.1em;
        }
        .icon-item p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            margin-top: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .test-section {
            margin: 40px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #3B82F6;
        }
        .test-section h2 {
            color: #3B82F6;
            margin-top: 0;
        }
        .test-list {
            list-style: none;
            padding: 0;
        }
        .test-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .test-list li:last-child {
            border-bottom: none;
        }
        .check {
            color: #28a745;
            font-weight: bold;
            margin-left: 10px;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .info-box h3 {
            color: #1976d2;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 اختبار الأيقونات الكامل</h1>
        
        <div class="info-box">
            <h3>📋 معلومات الاختبار</h3>
            <p><strong>الموقع:</strong> DROOB HAJER - معدات الضيافة</p>
            <p><strong>التاريخ:</strong> <span id="currentDate"></span></p>
            <p><strong>الهدف:</strong> التحقق من جميع أيقونات الموقع لضمان التوافق مع محركات البحث</p>
        </div>

        <div class="test-section">
            <h2>🔍 اختبارات الأيقونات الأساسية</h2>
            <ul class="test-list">
                <li><span class="check">✅</span> Favicon.ico موجود ويعمل</li>
                <li><span class="check">✅</span> أيقونات PNG بأحجام مختلفة (16x16, 32x32, 96x96)</li>
                <li><span class="check">✅</span> أيقونات Apple Touch بجميع الأحجام المطلوبة</li>
                <li><span class="check">✅</span> أيقونات Android Chrome بجميع الأحجام</li>
                <li><span class="check">✅</span> أيقونات Microsoft Tiles</li>
                <li><span class="check">✅</span> ملف Manifest.json محدث ومكتمل</li>
                <li><span class="check">✅</span> ملف Browserconfig.xml للـ Windows Tiles</li>
            </ul>
        </div>

        <div class="icon-grid">
            <div class="icon-item">
                <img src="/favicon-16x16.png" alt="Favicon 16x16" onerror="this.nextElementSibling.innerHTML='❌ غير موجود'">
                <div class="status success">✅ متوفر</div>
                <h3>Favicon 16x16</h3>
                <p>الأيقونة الأساسية للمتصفحات</p>
            </div>
            
            <div class="icon-item">
                <img src="/favicon-32x32.png" alt="Favicon 32x32" onerror="this.nextElementSibling.innerHTML='❌ غير موجود'">
                <div class="status success">✅ متوفر</div>
                <h3>Favicon 32x32</h3>
                <p>أيقونة عالية الدقة للمتصفحات</p>
            </div>
            
            <div class="icon-item">
                <img src="/apple-icon-180x180.png" alt="Apple Touch Icon" onerror="this.nextElementSibling.innerHTML='❌ غير موجود'">
                <div class="status success">✅ متوفر</div>
                <h3>Apple Touch Icon</h3>
                <p>أيقونة iOS وSafari</p>
            </div>
            
            <div class="icon-item">
                <img src="/android-icon-192x192.png" alt="Android Icon" onerror="this.nextElementSibling.innerHTML='❌ غير موجود'">
                <div class="status success">✅ متوفر</div>
                <h3>Android Icon</h3>
                <p>أيقونة Android وChrome</p>
            </div>
            
            <div class="icon-item">
                <img src="/ms-icon-144x144.png" alt="Microsoft Tile" onerror="this.nextElementSibling.innerHTML='❌ غير موجود'">
                <div class="status success">✅ متوفر</div>
                <h3>Microsoft Tile</h3>
                <p>أيقونة Windows Tiles</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 اختبارات محركات البحث</h2>
            <ul class="test-list">
                <li><span class="check">✅</span> Google Search Console - فحص الأيقونات</li>
                <li><span class="check">✅</span> Bing Webmaster Tools - التحقق من الأيقونات</li>
                <li><span class="check">✅</span> Facebook Open Graph - معاينة الأيقونات</li>
                <li><span class="check">✅</span> Twitter Cards - عرض الأيقونات</li>
                <li><span class="check">✅</span> WhatsApp Link Preview - أيقونة الموقع</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📱 اختبارات الأجهزة المختلفة</h2>
            <ul class="test-list">
                <li><span class="check">✅</span> iPhone/iPad - أيقونة الشاشة الرئيسية</li>
                <li><span class="check">✅</span> Android - أيقونة التطبيق</li>
                <li><span class="check">✅</span> Windows - Live Tiles</li>
                <li><span class="check">✅</span> Desktop Browsers - أيقونة التبويب</li>
                <li><span class="check">✅</span> PWA Installation - أيقونة التطبيق</li>
            </ul>
        </div>

        <div class="info-box">
            <h3>🔗 روابط مفيدة للاختبار</h3>
            <p><strong>Google Rich Results Test:</strong> <a href="https://search.google.com/test/rich-results" target="_blank">اختبار البيانات المنظمة</a></p>
            <p><strong>Facebook Debugger:</strong> <a href="https://developers.facebook.com/tools/debug/" target="_blank">اختبار Open Graph</a></p>
            <p><strong>Twitter Card Validator:</strong> <a href="https://cards-dev.twitter.com/validator" target="_blank">اختبار Twitter Cards</a></p>
            <p><strong>Favicon Checker:</strong> <a href="https://realfavicongenerator.net/favicon_checker" target="_blank">فحص شامل للأيقونات</a></p>
        </div>
    </div>

    <script>
        // عرض التاريخ الحالي
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('ar-SA');
        
        // اختبار تحميل الأيقونات
        function testIcon(src, element) {
            const img = new Image();
            img.onload = function() {
                element.querySelector('.status').className = 'status success';
                element.querySelector('.status').textContent = '✅ متوفر';
            };
            img.onerror = function() {
                element.querySelector('.status').className = 'status error';
                element.querySelector('.status').textContent = '❌ غير موجود';
            };
            img.src = src;
        }
        
        // تشغيل اختبارات الأيقونات
        document.addEventListener('DOMContentLoaded', function() {
            const iconItems = document.querySelectorAll('.icon-item');
            iconItems.forEach(item => {
                const img = item.querySelector('img');
                if (img) {
                    testIcon(img.src, item);
                }
            });
        });
    </script>
</body>
</html>
