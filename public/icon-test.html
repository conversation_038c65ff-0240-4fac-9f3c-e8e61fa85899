<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأيقونات - دروب هجر</title>
    
    <!-- الأيقونات المفضلة -->
    <link rel="icon" type="image/png" sizes="16x16" href="/icons8-circled-d-ios-17-filled-16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons8-circled-d-ios-17-filled-32.png">
    <link rel="shortcut icon" href="/icons8-circled-d-ios-17-filled-32.png">
    
    <!-- Meta tags لمحركات البحث -->
    <meta name="description" content="اختبار أيقونات موقع دروب هجر">
    <meta name="msapplication-TileImage" content="/icons8-circled-d-ios-17-filled-32.png">
    <meta name="msapplication-TileColor" content="#3B82F6">
    <meta name="theme-color" content="#3B82F6">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 40px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .icon-test {
            display: flex;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #3B82F6;
        }
        .icon-test img {
            margin-left: 15px;
            border: 1px solid #ddd;
            padding: 5px;
            background: white;
            border-radius: 4px;
        }
        .status {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
        }
        h1 {
            color: #3B82F6;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-right: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار أيقونات الموقع</h1>
        
        <div class="info">
            <strong>ملاحظة:</strong> هذه الصفحة لاختبار ظهور الأيقونات في المتصفح ومحركات البحث.
        </div>
        
        <div class="icon-test">
            <img src="/icons8-circled-d-ios-17-filled-16.png" alt="أيقونة 16x16" width="16" height="16">
            <div>
                <strong>أيقونة 16x16:</strong> 
                <span class="status">✅ تم التحميل بنجاح</span>
                <br>
                <small>هذه الأيقونة تظهر في تبويبات المتصفح</small>
            </div>
        </div>
        
        <div class="icon-test">
            <img src="/icons8-circled-d-ios-17-filled-32.png" alt="أيقونة 32x32" width="32" height="32">
            <div>
                <strong>أيقونة 32x32:</strong> 
                <span class="status">✅ تم التحميل بنجاح</span>
                <br>
                <small>هذه الأيقونة تظهر في المفضلة ونتائج البحث</small>
            </div>
        </div>
        
        <div class="info">
            <h3>خطوات التحقق من ظهور الأيقونة في جوجل:</h3>
            <ol>
                <li>تأكد من أن الموقع مفهرس في جوجل</li>
                <li>استخدم Google Search Console للتحقق من الفهرسة</li>
                <li>انتظر 24-48 ساعة لظهور التغييرات</li>
                <li>تحقق من ظهور الأيقونة في نتائج البحث</li>
            </ol>
        </div>
        
        <div class="info">
            <h3>روابط مفيدة للتحقق:</h3>
            <ul>
                <li><a href="/favicon-test.html">صفحة اختبار Favicon</a></li>
                <li><a href="/manifest.json">ملف Manifest</a></li>
                <li><a href="/robots.txt">ملف Robots</a></li>
                <li><a href="/sitemap.xml">خريطة الموقع</a></li>
            </ul>
        </div>
    </div>
    
    <script>
        // التحقق من تحميل الأيقونات
        function checkIcon(src, size) {
            const img = new Image();
            img.onload = function() {
                console.log(`✅ أيقونة ${size} تم تحميلها بنجاح`);
            };
            img.onerror = function() {
                console.error(`❌ فشل في تحميل أيقونة ${size}`);
            };
            img.src = src;
        }
        
        // اختبار الأيقونات
        checkIcon('/icons8-circled-d-ios-17-filled-16.png', '16x16');
        checkIcon('/icons8-circled-d-ios-17-filled-32.png', '32x32');
    </script>
</body>
</html>
