<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل تحديث ID الفئة - DROOB HAJER</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .update-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .update-section h2 {
            color: #FFD700;
            margin-top: 0;
            border-bottom: 2px solid #FFD700;
            padding-bottom: 10px;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            border-left: 4px solid #F44336;
        }
        .after {
            border-left: 4px solid #4CAF50;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            word-break: break-all;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        .warning {
            color: #FF9800;
            font-weight: bold;
        }
        .info {
            color: #2196F3;
            font-weight: bold;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #FFD700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 سجل تحديث ID الفئة الرئيسية</h1>
        
        <div class="update-section">
            <h2>🎯 تفاصيل التحديث</h2>
            <div class="before-after">
                <div class="before">
                    <h3>❌ قبل التحديث</h3>
                    <div class="code">
                        ID: 6663f301-f0f8-4b31-8709-fe5bb6ae276b
                    </div>
                    <div class="code">
                        URL: /en/category/6663f301-f0f8-4b31-8709-fe5bb6ae276b
                    </div>
                </div>
                <div class="after">
                    <h3>✅ بعد التحديث</h3>
                    <div class="code">
                        ID: F_B
                    </div>
                    <div class="code">
                        URL: /en/category/F_B
                    </div>
                </div>
            </div>
        </div>

        <div class="update-section">
            <h2>📊 إحصائيات التحديث</h2>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div>فئة رئيسية محدثة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">7</div>
                    <div>فئة فرعية محدثة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div>منتج محدث</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">57</div>
                    <div>رابط في Sitemap</div>
                </div>
            </div>
        </div>

        <div class="update-section">
            <h2>🔧 الأوامر المستخدمة</h2>
            <div class="code">
                SET FOREIGN_KEY_CHECKS = 0;
            </div>
            <div class="code">
                UPDATE categories SET id = 'F_B' WHERE id = '6663f301-f0f8-4b31-8709-fe5bb6ae276b';
            </div>
            <div class="code">
                UPDATE subcategories SET category_id = 'F_B' WHERE category_id = '6663f301-f0f8-4b31-8709-fe5bb6ae276b';
            </div>
            <div class="code">
                UPDATE products SET category_id = 'F_B' WHERE category_id = '6663f301-f0f8-4b31-8709-fe5bb6ae276b';
            </div>
            <div class="code">
                SET FOREIGN_KEY_CHECKS = 1;
            </div>
        </div>

        <div class="update-section">
            <h2>✅ التحقق من النتائج</h2>
            <p><span class="success">✅</span> تم تحديث ID الفئة الرئيسية بنجاح</p>
            <p><span class="success">✅</span> تم تحديث جميع الفئات الفرعية المرتبطة</p>
            <p><span class="success">✅</span> تم تحديث جميع المنتجات المرتبطة</p>
            <p><span class="success">✅</span> تم الحفاظ على سلامة قاعدة البيانات</p>
            <p><span class="success">✅</span> تم إعادة إنشاء Sitemap بالروابط الجديدة</p>
            <p><span class="info">ℹ️</span> الرابط الجديد: <a href="https://droobhajer.com/en/category/F_B" target="_blank" style="color: #FFD700;">https://droobhajer.com/en/category/F_B</a></p>
        </div>

        <div class="update-section">
            <h2>📋 معلومات الفئة</h2>
            <p><strong>الاسم بالإنجليزية:</strong> Food and Beverage Services</p>
            <p><strong>الاسم بالعربية:</strong> مستلزمات المشروبات والتغذية</p>
            <p><strong>عدد الفئات الفرعية:</strong> 7</p>
            <p><strong>عدد المنتجات:</strong> 1</p>
            <p><strong>حالة الفئة:</strong> نشطة</p>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
            <h3>🎉 تم تحديث ID الفئة بنجاح!</h3>
            <p>الرابط الآن أقصر وأسهل في الاستخدام: <strong>F_B</strong></p>
            <p>تاريخ التحديث: 6 يوليو 2025</p>
        </div>
    </div>

    <script>
        // Test the new URL
        fetch('/api/categories/F_B')
            .then(response => {
                if (response.ok) {
                    console.log('✅ New category ID F_B is working correctly');
                } else {
                    console.log('❌ New category ID F_B is not working');
                }
            })
            .catch(error => {
                console.log('⚠️ Error testing new category ID:', error);
            });
    </script>
</body>
</html>
