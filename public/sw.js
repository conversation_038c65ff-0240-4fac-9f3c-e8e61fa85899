// Service Worker for DROOB HAJER PWA
// Version 1.0.0

const CACHE_NAME = 'droobhajer-v1.0.0';
const STATIC_CACHE_NAME = 'droobhajer-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'droobhajer-dynamic-v1.0.0';

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/ar',
  '/en',
  '/manifest.json',
  '/favicon.ico',
  '/favicon.svg',
  '/favicon-16x16.png',
  '/favicon-32x32.png',
  '/android-icon-192x192.png',
  '/android-icon-512x512.png',
  '/_next/static/css/app/layout.css',
  '/_next/static/chunks/webpack.js',
  '/_next/static/chunks/main.js',
  '/_next/static/chunks/pages/_app.js'
];

// API routes to cache
const API_CACHE_PATTERNS = [
  /^\/api\/categories/,
  /^\/api\/products/,
  /^\/api\/featured-products/,
  /^\/api\/uploads/
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('📦 Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('✅ Service Worker: Static files cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ Service Worker: Error caching static files:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName !== CACHE_NAME) {
              console.log('🗑️ Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker: Activated successfully');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests with caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Handle different types of requests
  if (isStaticFile(url.pathname)) {
    // Static files: Cache First strategy
    event.respondWith(cacheFirst(request));
  } else if (isAPIRequest(url.pathname)) {
    // API requests: Network First strategy
    event.respondWith(networkFirst(request));
  } else if (isImageRequest(url.pathname)) {
    // Images: Cache First strategy
    event.respondWith(cacheFirst(request));
  } else {
    // Other requests: Network First strategy
    event.respondWith(networkFirst(request));
  }
});

// Cache First strategy - for static files
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache First strategy failed:', error);
    return new Response('Offline content not available', { status: 503 });
  }
}

// Network First strategy - for dynamic content
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/') || new Response('Offline', { status: 503 });
    }
    
    return new Response('Offline content not available', { status: 503 });
  }
}

// Helper functions
function isStaticFile(pathname) {
  return pathname.startsWith('/_next/static/') ||
         pathname.startsWith('/static/') ||
         pathname.includes('.css') ||
         pathname.includes('.js') ||
         pathname.includes('.woff') ||
         pathname.includes('.woff2') ||
         pathname === '/manifest.json' ||
         pathname.includes('favicon');
}

function isAPIRequest(pathname) {
  return API_CACHE_PATTERNS.some(pattern => pattern.test(pathname));
}

function isImageRequest(pathname) {
  return pathname.includes('.jpg') ||
         pathname.includes('.jpeg') ||
         pathname.includes('.png') ||
         pathname.includes('.webp') ||
         pathname.includes('.svg') ||
         pathname.startsWith('/uploads/') ||
         pathname.startsWith('/imge/');
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('🔄 Service Worker: Background sync triggered');
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  try {
    // Handle any pending offline actions here
    console.log('📡 Service Worker: Performing background sync');
  } catch (error) {
    console.error('❌ Service Worker: Background sync failed:', error);
  }
}

// Push notification handler
self.addEventListener('push', (event) => {
  console.log('📬 Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New notification from DROOB HAJER',
    icon: '/android-icon-192x192.png',
    badge: '/android-icon-96x96.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'عرض المنتجات',
        icon: '/android-icon-96x96.png'
      },
      {
        action: 'close',
        title: 'إغلاق',
        icon: '/android-icon-96x96.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('DROOB HAJER', options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 Service Worker: Notification clicked');
  
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/ar/products')
    );
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Error handler
self.addEventListener('error', (event) => {
  console.error('❌ Service Worker: Error occurred:', event.error);
});

// Unhandled rejection handler
self.addEventListener('unhandledrejection', (event) => {
  console.error('❌ Service Worker: Unhandled promise rejection:', event.reason);
});

console.log('🎉 Service Worker: Loaded successfully');
