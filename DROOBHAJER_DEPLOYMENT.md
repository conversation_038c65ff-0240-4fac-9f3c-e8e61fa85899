# 🚀 نشر مشروع دروب هاجر على Hostinger

## 📋 معلومات المشروع:
- **النطاق**: droobhajer.com
- **IP السيرفر**: **************
- **اسم المستخدم في Hostinger**: wahid.alduais
- **اسم المستخدم في السيرفر**: DROOBHAJER
- **قاعدة البيانات**: droobhajer_db
- **مستخدم قاعدة البيانات**: root
- **كلمة مرور قاعدة البيانات**: (ستضيفها لاحقاً)

## 🚀 طريقة النشر السريع:

### الخطوة 1: تحضير المشروع
```bash
# تشغيل سكريبت الإعداد
chmod +x setup-production.sh
./setup-production.sh
```

### الخطوة 2: رفع الملفات
1. ارفع ملف `droobhajer-production.tar.gz` إلى `/var/www/html/`
2. عبر SSH أو FTP

### الخطوة 3: استخراج الملفات على السيرفر
```bash
# عبر SSH
cd /var/www/html/
tar -xzf droobhajer-production.tar.gz
rm droobhajer-production.tar.gz
```

### الخطوة 4: تثبيت Dependencies
```bash
npm install --production
```

### الخطوة 5: إعداد قاعدة البيانات
1. اذهب إلى phpMyAdmin في لوحة تحكم Hostinger
2. اختر قاعدة البيانات `droobhajer_db`
3. قم بتشغيل ملف `database-setup-production.sql`

### الخطوة 6: تشغيل التطبيق
```bash
# تثبيت PM2 إذا لم يكن مثبت
npm install -g pm2

# تشغيل التطبيق
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

## 🧪 اختبار المشروع:

### 1. اختبار الموقع الرئيسي:
```
http://droobhajer.com
```

### 2. اختبار لوحة الإدارة:
```
http://droobhajer.com/admin
اسم المستخدم: admin
كلمة المرور: DroobHajer@2024!ProductionAdmin#Secure
```

### 3. اختبار قاعدة البيانات:
```
http://droobhajer.com/api/test-db-connection
```

## 🔧 إعدادات Hostinger المطلوبة:

### 1. Node.js:
- تفعيل Node.js من لوحة التحكم
- اختيار إصدار 18+ أو 20+
- تحديد مجلد التطبيق: `/public_html/`

### 2. قاعدة البيانات:
- ✅ قاعدة البيانات: `droobhajer_db`
- ✅ المستخدم: `root`
- ⚠️ إضافة كلمة مرور لاحقاً

### 3. النطاق:
- ✅ النطاق: `droobhajer.com`
- ✅ IP: `**************`

## 🚨 ملاحظات مهمة:

### بعد النشر الناجح:
1. **غيّر كلمة مرور المدير** من لوحة الإدارة
2. **أضف كلمة مرور لقاعدة البيانات** وحدث ملف `.env.local`
3. **اختبر جميع الوظائف** (المنتجات، الفئات، طلبات التسعير)
4. **أعد تشغيل التطبيق** بعد تحديث كلمة مرور قاعدة البيانات:
   ```bash
   pm2 restart droobhajer
   ```

### مراقبة التطبيق:
```bash
# حالة التطبيق
pm2 status

# سجلات التطبيق
pm2 logs droobhajer

# مراقبة مباشرة
pm2 monit

# إعادة تشغيل
pm2 restart droobhajer
```

### استكشاف الأخطاء:
```bash
# إذا لم يعمل التطبيق
pm2 logs droobhajer --lines 50

# إذا كان هناك خطأ في قاعدة البيانات
mysql -u root -p droobhajer_db
SHOW TABLES;

# إذا كان هناك خطأ في الملفات
ls -la /home/<USER>/public_html/
```

## 📞 الدعم:

### ملفات مفيدة:
- `HOSTINGER_DEPLOYMENT_GUIDE.md` - دليل شامل
- `QUICK_DEPLOYMENT_STEPS.md` - خطوات سريعة
- `database-setup-production.sql` - إعداد قاعدة البيانات

### أوامر مفيدة:
```bash
# تحديث المشروع
git pull origin main
npm run build
pm2 restart droobhajer

# نسخة احتياطية من قاعدة البيانات
mysqldump -u root -p droobhajer_db > backup.sql

# استعادة قاعدة البيانات
mysql -u root -p droobhajer_db < backup.sql
```

---

**🎉 مبروك! مشروع دروب هاجر جاهز للنشر على droobhajer.com**
