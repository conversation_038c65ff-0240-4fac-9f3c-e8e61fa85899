const https = require('https');
const http = require('http');

function testSitemap(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const options = {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
        'Accept': 'application/xml,text/xml,*/*',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'en-US,en;q=0.9'
      }
    };

    const req = protocol.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data.substring(0, 500) + (data.length > 500 ? '...' : ''),
          size: data.length
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function main() {
  const sitemapUrl = 'https://droobhajer.com/sitemap.xml';
  
  console.log('🔍 اختبار sitemap.xml...');
  console.log('URL:', sitemapUrl);
  console.log('');
  
  try {
    const result = await testSitemap(sitemapUrl);
    
    console.log('✅ النتائج:');
    console.log('Status Code:', result.statusCode);
    console.log('Content-Type:', result.headers['content-type']);
    console.log('Content-Length:', result.headers['content-length']);
    console.log('Cache-Control:', result.headers['cache-control']);
    console.log('Last-Modified:', result.headers['last-modified']);
    console.log('');
    console.log('📄 محتوى الملف (أول 500 حرف):');
    console.log(result.data);
    console.log('');
    console.log('📊 حجم الملف:', result.size, 'bytes');
    
    // فحص صحة XML
    if (result.data.includes('<?xml') && result.data.includes('<urlset')) {
      console.log('✅ الملف يحتوي على XML صحيح');
    } else {
      console.log('❌ الملف لا يحتوي على XML صحيح');
    }
    
    // فحص Content-Type
    if (result.headers['content-type'] && result.headers['content-type'].includes('xml')) {
      console.log('✅ Content-Type صحيح');
    } else {
      console.log('⚠️  Content-Type قد يكون غير صحيح:', result.headers['content-type']);
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

main();
