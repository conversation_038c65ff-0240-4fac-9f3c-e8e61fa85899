#!/bin/bash

# 🔒 سكريبت فحص إعدادات HTTPS لموقع دروب هجر
# يتحقق من جميع جوانب HTTPS والأمان

echo "🔒 فحص إعدادات HTTPS لموقع دروب هجر"
echo "=========================================="

DOMAIN="droobhajer.com"
WWW_DOMAIN="www.droobhajer.com"

# ألوان للعرض
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# دالة للطباعة الملونة
print_status() {
    if [ "$2" = "OK" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    elif [ "$2" = "WARNING" ]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
    fi
}

echo -e "${BLUE}1. فحص إعادة التوجيه من HTTP إلى HTTPS${NC}"
echo "-------------------------------------------"

# فحص إعادة التوجيه للدومين الرئيسي
HTTP_REDIRECT=$(curl -s -I "http://$DOMAIN" | grep -i "location: https://" | wc -l)
if [ $HTTP_REDIRECT -gt 0 ]; then
    print_status "إعادة التوجيه من HTTP إلى HTTPS للدومين الرئيسي" "OK"
else
    print_status "إعادة التوجيه من HTTP إلى HTTPS للدومين الرئيسي" "ERROR"
fi

# فحص إعادة التوجيه للدومين مع www
WWW_REDIRECT=$(curl -s -I "http://$WWW_DOMAIN" | grep -i "location: https://" | wc -l)
if [ $WWW_REDIRECT -gt 0 ]; then
    print_status "إعادة التوجيه من HTTP إلى HTTPS للدومين مع www" "OK"
else
    print_status "إعادة التوجيه من HTTP إلى HTTPS للدومين مع www" "ERROR"
fi

echo ""
echo -e "${BLUE}2. فحص شهادة SSL${NC}"
echo "-------------------"

# فحص شهادة SSL
SSL_CHECK=$(echo | openssl s_client -connect $DOMAIN:443 -servername $DOMAIN 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
if [ $? -eq 0 ]; then
    print_status "شهادة SSL صالحة" "OK"
    echo "$SSL_CHECK"
else
    print_status "شهادة SSL غير صالحة أو غير موجودة" "ERROR"
fi

echo ""
echo -e "${BLUE}3. فحص HSTS Headers${NC}"
echo "---------------------"

# فحص HSTS
HSTS_CHECK=$(curl -s -I "https://$DOMAIN" | grep -i "strict-transport-security" | wc -l)
if [ $HSTS_CHECK -gt 0 ]; then
    print_status "HSTS Header موجود" "OK"
    curl -s -I "https://$DOMAIN" | grep -i "strict-transport-security"
else
    print_status "HSTS Header غير موجود" "ERROR"
fi

echo ""
echo -e "${BLUE}4. فحص Security Headers${NC}"
echo "-------------------------"

# فحص Headers الأمان
HEADERS=$(curl -s -I "https://$DOMAIN")

# X-Frame-Options
if echo "$HEADERS" | grep -qi "x-frame-options"; then
    print_status "X-Frame-Options Header موجود" "OK"
else
    print_status "X-Frame-Options Header غير موجود" "WARNING"
fi

# X-Content-Type-Options
if echo "$HEADERS" | grep -qi "x-content-type-options"; then
    print_status "X-Content-Type-Options Header موجود" "OK"
else
    print_status "X-Content-Type-Options Header غير موجود" "WARNING"
fi

# Content-Security-Policy
if echo "$HEADERS" | grep -qi "content-security-policy"; then
    print_status "Content-Security-Policy Header موجود" "OK"
else
    print_status "Content-Security-Policy Header غير موجود" "WARNING"
fi

echo ""
echo -e "${BLUE}5. فحص استجابة HTTPS${NC}"
echo "---------------------"

# فحص استجابة HTTPS
HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN")
if [ "$HTTPS_STATUS" = "200" ]; then
    print_status "الموقع يستجيب عبر HTTPS (HTTP $HTTPS_STATUS)" "OK"
else
    print_status "الموقع لا يستجيب بشكل صحيح عبر HTTPS (HTTP $HTTPS_STATUS)" "ERROR"
fi

echo ""
echo -e "${BLUE}6. فحص Mixed Content${NC}"
echo "---------------------"

# فحص المحتوى المختلط (تحقق بسيط)
MIXED_CONTENT=$(curl -s "https://$DOMAIN" | grep -i "http://" | grep -v "localhost" | wc -l)
if [ $MIXED_CONTENT -eq 0 ]; then
    print_status "لا يوجد محتوى مختلط واضح" "OK"
else
    print_status "قد يوجد محتوى مختلط - يحتاج فحص يدوي" "WARNING"
fi

echo ""
echo -e "${BLUE}7. اختبار أدوات خارجية${NC}"
echo "-------------------------"

echo "🔗 روابط مفيدة للاختبار:"
echo "• SSL Labs: https://www.ssllabs.com/ssltest/analyze.html?d=$DOMAIN"
echo "• Security Headers: https://securityheaders.com/?q=$DOMAIN"
echo "• Why No Padlock: https://www.whynopadlock.com/check.php?uri=$DOMAIN"

echo ""
echo -e "${BLUE}8. ملخص النتائج${NC}"
echo "----------------"

# حساب النقاط
TOTAL_CHECKS=6
PASSED_CHECKS=0

[ $HTTP_REDIRECT -gt 0 ] && ((PASSED_CHECKS++))
[ $WWW_REDIRECT -gt 0 ] && ((PASSED_CHECKS++))
[ $? -eq 0 ] && ((PASSED_CHECKS++))  # SSL
[ $HSTS_CHECK -gt 0 ] && ((PASSED_CHECKS++))
[ "$HTTPS_STATUS" = "200" ] && ((PASSED_CHECKS++))
[ $MIXED_CONTENT -eq 0 ] && ((PASSED_CHECKS++))

SCORE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

echo "النقاط: $PASSED_CHECKS/$TOTAL_CHECKS ($SCORE%)"

if [ $SCORE -ge 80 ]; then
    echo -e "${GREEN}🎉 إعدادات HTTPS جيدة!${NC}"
elif [ $SCORE -ge 60 ]; then
    echo -e "${YELLOW}⚠️  إعدادات HTTPS تحتاج تحسين${NC}"
else
    echo -e "${RED}🚨 إعدادات HTTPS تحتاج إصلاح عاجل${NC}"
fi

echo ""
echo "📋 الخطوات التالية:"
echo "1. إذا كانت النتائج سيئة، راجع دليل HTTPS_SETUP_GUIDE.md"
echo "2. تأكد من تفعيل SSL في لوحة تحكم Hostinger"
echo "3. اختبر الموقع في Google Search Console"
echo "4. راقب logs الخادم للأخطاء"

echo ""
echo "✅ انتهى الفحص"
