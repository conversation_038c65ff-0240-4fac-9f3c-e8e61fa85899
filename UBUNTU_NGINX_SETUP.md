# 🐧 إعداد مشروع دروب هاجر على Ubuntu + Nginx

## 📋 معلومات السيرفر:
- **النطاق**: droobhajer.com
- **IP السيرفر**: **************
- **نظام التشغيل**: Ubuntu Server
- **خادم الويب**: Nginx
- **مسار المشروع**: `/var/www/html/`
- **مستخدم السيرفر**: DROOBHAJER

---

## 🚀 خطوات الإعداد الكاملة:

### 1️⃣ إعداد السيرفر الأساسي:

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Node.js (إصدار 20)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# تثبيت Nginx
sudo apt install nginx -y

# تثبيت MySQL
sudo apt install mysql-server -y

# تثبيت PM2
sudo npm install -g pm2

# تثبيت Git (إذا لم يكن مثبت)
sudo apt install git -y
```

### 2️⃣ إعداد MySQL:

```bash
# تأمين MySQL
sudo mysql_secure_installation

# الدخول إلى MySQL
sudo mysql -u root -p

# إنشاء قاعدة البيانات
CREATE DATABASE droobhajer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# إنشاء مستخدم (اختياري)
CREATE USER 'droobhajer'@'localhost' IDENTIFIED BY 'your_password_here';
GRANT ALL PRIVILEGES ON droobhajer_db.* TO 'droobhajer'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3️⃣ إعداد Nginx:

```bash
# إنشاء ملف إعداد الموقع
sudo nano /etc/nginx/sites-available/droobhajer

# نسخ محتوى ملف nginx.conf إلى الملف أعلاه

# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/droobhajer /etc/nginx/sites-enabled/

# حذف الموقع الافتراضي
sudo rm /etc/nginx/sites-enabled/default

# اختبار إعدادات Nginx
sudo nginx -t

# إعادة تشغيل Nginx
sudo systemctl restart nginx
```

### 4️⃣ إعداد الصلاحيات:

```bash
# إنشاء مستخدم للمشروع
sudo useradd -m -s /bin/bash DROOBHAJER

# إضافة المستخدم إلى مجموعة www-data
sudo usermod -a -G www-data DROOBHAJER

# تعيين صلاحيات مجلد الويب
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/

# السماح للمستخدم بالكتابة
sudo chmod -R 775 /var/www/html/
```

### 5️⃣ رفع وتثبيت المشروع:

```bash
# الانتقال إلى مجلد الويب
cd /var/www/html/

# رفع ملف المشروع (droobhajer-production.tar.gz)
# يمكن استخدام scp أو rsync أو رفع مباشر

# استخراج الملفات
sudo tar -xzf droobhajer-production.tar.gz

# تعيين الصلاحيات
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/

# تثبيت Dependencies
sudo npm install --production

# إنشاء مجلدات الرفع
sudo mkdir -p /var/www/html/uploads
sudo mkdir -p /var/www/html/secure-uploads
sudo mkdir -p /var/www/html/logs

# تعيين صلاحيات المجلدات
sudo chmod -R 777 /var/www/html/uploads
sudo chmod -R 700 /var/www/html/secure-uploads
sudo chmod -R 755 /var/www/html/logs
```

### 6️⃣ إعداد قاعدة البيانات:

```bash
# تشغيل ملف إعداد قاعدة البيانات
mysql -u root -p droobhajer_db < /var/www/html/database-setup-production.sql
```

### 7️⃣ تشغيل المشروع:

```bash
# تشغيل التطبيق بـ PM2
cd /var/www/html/
sudo pm2 start ecosystem.config.js --env production

# حفظ إعدادات PM2
sudo pm2 save

# تفعيل بدء تلقائي مع النظام
sudo pm2 startup
sudo pm2 save
```

---

## 🔧 إعدادات إضافية:

### SSL Certificate (Let's Encrypt):

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx -y

# الحصول على شهادة SSL
sudo certbot --nginx -d droobhajer.com -d www.droobhajer.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall:

```bash
# تفعيل UFW
sudo ufw enable

# السماح بـ SSH
sudo ufw allow ssh

# السماح بـ HTTP و HTTPS
sudo ufw allow 'Nginx Full'

# السماح بـ MySQL (إذا كان خارجي)
sudo ufw allow 3306

# عرض الحالة
sudo ufw status
```

### مراقبة النظام:

```bash
# مراقبة PM2
pm2 monit

# مراقبة Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# مراقبة MySQL
sudo tail -f /var/log/mysql/error.log
```

---

## 🧪 اختبار المشروع:

### 1. اختبار Nginx:
```bash
curl -I http://droobhajer.com
```

### 2. اختبار Node.js:
```bash
curl http://localhost:3000
```

### 3. اختبار قاعدة البيانات:
```bash
curl http://droobhajer.com/api/test-db-connection
```

### 4. اختبار لوحة الإدارة:
```
http://droobhajer.com/admin
```

---

## 🚨 استكشاف الأخطاء:

### مشكلة: Nginx لا يعمل
```bash
sudo systemctl status nginx
sudo nginx -t
sudo tail -f /var/log/nginx/error.log
```

### مشكلة: Node.js لا يعمل
```bash
pm2 status
pm2 logs droobhajer
pm2 restart droobhajer
```

### مشكلة: قاعدة البيانات
```bash
sudo systemctl status mysql
mysql -u root -p -e "SHOW DATABASES;"
```

### مشكلة: الصلاحيات
```bash
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

---

## 📞 أوامر مفيدة:

```bash
# إعادة تشغيل الخدمات
sudo systemctl restart nginx
sudo systemctl restart mysql
pm2 restart droobhajer

# مراقبة الموارد
htop
df -h
free -h

# نسخة احتياطية
mysqldump -u root -p droobhajer_db > backup_$(date +%Y%m%d).sql
tar -czf project_backup_$(date +%Y%m%d).tar.gz /var/www/html/
```

---

**🎉 مبروك! مشروع دروب هاجر جاهز على Ubuntu + Nginx!**
