#!/usr/bin/env node

const fs = require('fs');

// قراءة ملف المنتجات
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../chafing-dishes-products.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إصلاح التطابق بين العناوين
function fixTitleMatching(product) {
  // استخراج السعة من العنوان الإنجليزي
  const capacityMatch = product.title.match(/(\d+(?:\.\d+)?)\s*L\b/i);
  
  let arabicTitle = product.titleAr;
  
  // إضافة السعة للعنوان العربي إذا كانت موجودة في الإنجليزي
  if (capacityMatch && !arabicTitle.includes('لتر')) {
    const capacity = capacityMatch[1];
    arabicTitle += ` ${capacity} لتر`;
  }
  
  return arabicTitle;
}

// إصلاح التطابق بين الأوصاف
function fixDescriptionMatching(product) {
  // استخراج السعة من العنوان
  const capacityMatch = product.title.match(/(\d+(?:\.\d+)?)\s*L\b/i);
  const capacity = capacityMatch ? capacityMatch[1] : '';
  
  // تحديد الشكل
  let shape = '';
  if (product.title.toLowerCase().includes('rectangular')) {
    shape = 'مستطيل الشكل';
  } else if (product.title.toLowerCase().includes('round')) {
    shape = 'دائري الشكل';
  } else if (product.title.toLowerCase().includes('square')) {
    shape = 'مربع الشكل';
  } else if (product.title.toLowerCase().includes('oval')) {
    shape = 'بيضاوي الشكل';
  }
  
  // إنشاء وصف عربي متطابق
  let arabicDesc = `طبق تسخين ${shape} ستانلس ستيل`;
  if (capacity) {
    arabicDesc += ` ${capacity} لتر`;
  }
  arabicDesc += ' من دروب هجر\n\n';
  
  arabicDesc += 'هذا الطبق الفاخر المصنوع من الفولاذ المقاوم للصدأ، مصمم لتسخين الطعام بكفاءة. ';
  arabicDesc += 'يوفر الغطاء الزجاجي رؤية واضحة. ';
  arabicDesc += 'الطبقة الداخلية الملامسة للطعام مصنوعة من الفولاذ المقاوم للصدأ S/S304 - المعروف بمقاومته للتآكل وأدائه الصحي - ';
  arabicDesc += 'بينما تتميز الطبقة الخارجية بطبقة S/S201 لدعم هيكلي وتصميم أنيق.\n\n';
  
  if (capacity) {
    arabicDesc += `السعة: ${capacity} لتر\n`;
  }
  
  // إضافة الحجم من المواصفات
  const dimensionsSpec = product.specifications.find(spec => spec.nameAr === 'الحجم');
  if (dimensionsSpec) {
    arabicDesc += `الحجم: ${dimensionsSpec.valueAr}\n`;
  }
  
  arabicDesc += 'المادة: من الداخل S/S 304، من الخارج S/S201\n\n';
  arabicDesc += 'مثالي لفعاليات تقديم الطعام، وبوفيهات الفنادق، وحفلات الطعام الرسمية. ';
  arabicDesc += 'يحافظ على دفء الطعام مع توزيع حراري فعال.';
  
  return arabicDesc;
}

// إصلاح التطابق بين المميزات
function fixFeaturesMatching(product) {
  const englishFeatures = [
    "High-quality stainless steel construction",
    "Heat-resistant and durable design", 
    "Easy to clean and maintain",
    "Professional grade for commercial use",
    "Elegant and practical design",
    "Inner S/S304, Outer S/S201 layers"
  ];
  
  const arabicFeatures = [
    "مصنوع من الستانلس ستيل عالي الجودة",
    "مقاوم للحرارة وتصميم متين",
    "سهل التنظيف والصيانة", 
    "جودة احترافية للاستخدام التجاري",
    "تصميم أنيق وعملي",
    "طبقات من الداخل S/S304، من الخارج S/S201"
  ];
  
  return { en: englishFeatures, ar: arabicFeatures };
}

// إصلاح منتج واحد
function fixProductMatching(product) {
  console.log(`🔧 إصلاح التطابق للمنتج ${product.id}...`);
  
  // إصلاح العنوان العربي
  const newArabicTitle = fixTitleMatching(product);
  console.log(`   العنوان العربي: "${product.titleAr}" → "${newArabicTitle}"`);
  
  // إصلاح الوصف العربي
  const newArabicDesc = fixDescriptionMatching(product);
  
  // إصلاح المميزات
  const newFeatures = fixFeaturesMatching(product);
  
  // تحديث المنتج
  product.titleAr = newArabicTitle;
  product.descriptionAr = newArabicDesc;
  product.features = newFeatures.en;
  product.featuresAr = newFeatures.ar;
  
  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إصلاح التطابق بين اللغات...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح التطابق...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = fixProductMatching(product);
    console.log('');
    return fixed;
  });
  
  // حفظ النتيجة
  const outputPath = '../chafing-dishes-products.json';
  const jsonString = JSON.stringify(fixedProducts, null, 2);
  fs.writeFileSync(outputPath, jsonString, 'utf8');
  
  console.log(`✅ تم إصلاح التطابق لـ ${fixedProducts.length} منتج وحفظهم في: ${outputPath}`);
  
  // عرض عينة من النتائج
  console.log('\n🔍 عينة من التطابق المُصلح:');
  console.log('=' .repeat(80));
  const sample = fixedProducts[0];
  console.log(`📦 المنتج: ${sample.id}`);
  console.log(`   العنوان الإنجليزي: ${sample.title}`);
  console.log(`   العنوان العربي: ${sample.titleAr}`);
  console.log(`   عدد المميزات الإنجليزية: ${sample.features.length}`);
  console.log(`   عدد المميزات العربية: ${sample.featuresAr.length}`);
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
