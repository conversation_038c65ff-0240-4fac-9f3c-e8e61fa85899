#!/usr/bin/env node

const fs = require('fs');

// قراءة ملف المنتجات
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../chafing-dishes-products.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إصلاح الأسعار
function fixPrices(product) {
  // تقريب السعر إلى رقمين عشريين
  product.price = Math.round(product.price * 100) / 100;
  
  // تعيين السعر الأصلي إلى 0.00
  product.originalPrice = 0.00;
  
  return product;
}

// حفظ المنتجات مع تنسيق صحيح للأسعار
function saveProductsWithFormattedPrices(products, filePath) {
  const jsonString = JSON.stringify(products, null, 2);
  
  // استبدال "originalPrice": 0 بـ "originalPrice": 0.00
  const formattedJson = jsonString.replace(/"originalPrice":\s*0(?![.])/g, '"originalPrice": 0.00');
  
  fs.writeFileSync(filePath, formattedJson, 'utf8');
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إصلاح أسعار منتجات الشافنج ديش...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح الأسعار...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = fixPrices(product);
    if ((index + 1) % 5 === 0) {
      console.log(`   ✅ تم إصلاح ${index + 1} منتج`);
    }
    return fixed;
  });
  
  // حفظ النتيجة
  const outputPath = '../chafing-dishes-products.json';
  saveProductsWithFormattedPrices(fixedProducts, outputPath);
  
  console.log(`\n✅ تم إصلاح ${fixedProducts.length} منتج وحفظهم في: ${outputPath}`);
  
  // عرض عينة من النتائج
  console.log('\n🔍 عينة من الأسعار المُصلحة:');
  fixedProducts.slice(0, 3).forEach((product, index) => {
    console.log(`   ${index + 1}. ${product.id}: السعر ${product.price} - السعر الأصلي ${product.originalPrice}`);
  });
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
