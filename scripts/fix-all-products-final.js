#!/usr/bin/env node

const fs = require('fs');

// قراءة الملف الحالي
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../professional-products-titles-fixed.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// استخراج العنوان العربي من الوصف الإنجليزي
function extractArabicTitleFromEnglishDescription(englishDescription) {
  let title = '';
  
  // تحديد نوع المنتج بدقة أكبر
  if (/serving bowl|soup bowl|salad bowl|deep bowl|conical bowl|bowl/i.test(englishDescription)) {
    if (/soup|deep/i.test(englishDescription)) {
      title = 'وعاء شوربة';
    } else if (/salad/i.test(englishDescription)) {
      title = 'وعاء سلطة';
    } else if (/serving/i.test(englishDescription)) {
      title = 'وعاء تقديم';
    } else if (/conical/i.test(englishDescription)) {
      title = 'وعاء مخروطي';
    } else {
      title = 'وعاء';
    }
  } else if (/serving plate|plate|dish/i.test(englishDescription)) {
    if (/serving plate|serving/i.test(englishDescription)) {
      title = 'طبق تقديم';
    } else if (/soup dish|soup plate/i.test(englishDescription)) {
      title = 'طبق شوربة';
    } else if (/square/i.test(englishDescription)) {
      title = 'طبق مربع';
    } else if (/round/i.test(englishDescription)) {
      title = 'طبق دائري';
    } else if (/oval/i.test(englishDescription)) {
      title = 'طبق بيضاوي';
    } else if (/rectangle/i.test(englishDescription)) {
      title = 'طبق مستطيل';
    } else {
      title = 'طبق';
    }
  } else if (/tray/i.test(englishDescription)) {
    title = 'صينية تقديم';
  } else if (/container/i.test(englishDescription)) {
    if (/sugar/i.test(englishDescription)) {
      title = 'حاوية سكر';
    } else {
      title = 'حاوية';
    }
  } else if (/board/i.test(englishDescription)) {
    if (/serving board/i.test(englishDescription)) {
      title = 'لوح تقديم';
    } else {
      title = 'لوح';
    }
  } else {
    title = 'منتج بوفيه';
  }
  
  // إضافة المادة
  if (/melamine/i.test(englishDescription)) {
    title += ' ميلامين';
  }
  
  // إضافة اللون
  if (/black/i.test(englishDescription)) {
    if (/matte/i.test(englishDescription)) {
      title += ' أسود مطفي';
    } else {
      title += ' أسود';
    }
  } else if (/white/i.test(englishDescription)) {
    title += ' أبيض';
  } else if (/yellow/i.test(englishDescription)) {
    title += ' أصفر';
  } else if (/gray|grey/i.test(englishDescription)) {
    title += ' رمادي';
  }
  
  // إضافة الحجم
  const sizeMatch = englishDescription.match(/(\d+(?:\.\d+)?)\s*(?:inches?|cm)/i);
  if (sizeMatch) {
    const size = parseFloat(sizeMatch[1]);
    const unit = sizeMatch[0].toLowerCase().includes('cm') ? 'سم' : 'بوصة';
    title += ` ${size} ${unit}`;
  }
  
  // إضافة وصف الجودة
  title += ' عالي الجودة';
  
  return title.length > 60 ? title.substring(0, 57) + '...' : title;
}

// إصلاح منتج واحد
function fixProduct(product) {
  // إصلاح العنوان العربي إذا كان يبدأ بـ "منتج بوفيه ميلامين"
  if (product.titleAr && product.titleAr.startsWith('منتج بوفيه ميلامين')) {
    const newArabicTitle = extractArabicTitleFromEnglishDescription(product.description);
    console.log(`🔧 إصلاح ${product.id}: "${product.titleAr}" → "${newArabicTitle}"`);
    product.titleAr = newArabicTitle;
  }
  
  // إضافة أو إصلاح السعر الأصلي
  product.originalPrice = 0.00;
  
  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء الإصلاح الشامل للمنتجات...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح المنتجات...');
  
  let fixedCount = 0;
  const fixedProducts = products.map((product, index) => {
    const originalTitleAr = product.titleAr;
    const fixed = fixProduct(product);
    
    if (originalTitleAr !== fixed.titleAr) {
      fixedCount++;
    }
    
    if ((index + 1) % 10 === 0) {
      console.log(`   📊 تم معالجة ${index + 1} منتج`);
    }
    
    return fixed;
  });
  
  // حفظ النتيجة مع تنسيق صحيح
  const jsonString = JSON.stringify(fixedProducts, null, 2);
  const formattedJson = jsonString.replace(/"originalPrice":\s*0(?![.])/g, '"originalPrice": 0.00');
  
  const outputPath = '../professional-products-titles-fixed.json';
  fs.writeFileSync(outputPath, formattedJson, 'utf8');
  
  console.log(`\n✅ تم إصلاح ${fixedCount} عنوان عربي`);
  console.log(`✅ تم إضافة originalPrice: 0.00 لجميع المنتجات`);
  console.log(`✅ تم حفظ النتيجة في: ${outputPath}`);
  
  // فحص النتيجة
  const savedContent = fs.readFileSync(outputPath, 'utf8');
  const originalPriceCount = (savedContent.match(/"originalPrice": 0\.00/g) || []).length;
  const badTitlesCount = (savedContent.match(/"titleAr": "منتج بوفيه ميلامين/g) || []).length;
  
  console.log(`\n📊 النتائج النهائية:`);
  console.log(`   - منتجات مع originalPrice: 0.00 = ${originalPriceCount}`);
  console.log(`   - عناوين لم يتم إصلاحها = ${badTitlesCount}`);
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
