#!/usr/bin/env node

const fs = require('fs');

// قراءة ملف المنتجات
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../chafing-dishes-products.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إضافة المواصفات المفقودة
function addMissingSpecifications(product) {
  console.log(`🔧 إصلاح مواصفات المنتج ${product.id}...`);
  
  // البحث عن السعة في الوصف الإنجليزي
  const capacityMatch = product.description.match(/(\d+(?:\.\d+)?)\s*(?:liter|litre|l)\b/i);
  
  // البحث عن الحجم في الوصف العربي الأصلي
  const originalArabicDesc = product.descriptionAr;
  const sizeMatch = originalArabicDesc.match(/الحجم:\s*([^\n\r]+)/);
  
  // إنشاء المواصفات الجديدة
  const newSpecs = [
    {
      nameEn: "Material",
      nameAr: "المادة",
      valueEn: "Inner S/S304, Outer S/S201",
      valueAr: "من الداخل S/S 304، من الخارج S/S201"
    },
    {
      nameEn: "Type",
      nameAr: "النوع",
      valueEn: "Chafing Dish",
      valueAr: "طبق تسخين"
    }
  ];
  
  // إضافة السعة إذا وجدت
  if (capacityMatch) {
    const capacity = capacityMatch[1];
    newSpecs.push({
      nameEn: "Capacity",
      nameAr: "السعة",
      valueEn: `${capacity} Liter`,
      valueAr: `${capacity} لتر`
    });
    console.log(`   ✅ تمت إضافة السعة: ${capacity} لتر`);
  }
  
  // إضافة الحجم إذا وجد
  if (sizeMatch) {
    const size = sizeMatch[1].trim();
    newSpecs.push({
      nameEn: "Dimensions",
      nameAr: "الحجم",
      valueEn: size.replace('سم', 'cm'),
      valueAr: size
    });
    console.log(`   ✅ تمت إضافة الحجم: ${size}`);
  }
  
  // إضافة الاستخدام
  newSpecs.push({
    nameEn: "Usage",
    nameAr: "الاستخدام",
    valueEn: "Commercial Buffet",
    valueAr: "بوفيه تجاري"
  });
  
  // تحديث المواصفات
  product.specifications = newSpecs;
  
  // إصلاح السعر الأصلي
  product.originalPrice = 0.00;
  
  console.log(`   📊 إجمالي المواصفات: ${newSpecs.length}`);
  
  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إضافة المواصفات المفقودة...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إضافة المواصفات...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = addMissingSpecifications(product);
    console.log('');
    return fixed;
  });
  
  // حفظ النتيجة
  const outputPath = '../chafing-dishes-products.json';
  const jsonString = JSON.stringify(fixedProducts, null, 2);
  
  // استبدال "originalPrice": 0 بـ "originalPrice": 0.00
  const formattedJson = jsonString.replace(/"originalPrice":\s*0(?![.])/g, '"originalPrice": 0.00');
  
  fs.writeFileSync(outputPath, formattedJson, 'utf8');
  
  console.log(`✅ تم إصلاح ${fixedProducts.length} منتج وحفظهم في: ${outputPath}`);
  
  // عرض عينة من النتائج
  console.log('\n🔍 عينة من المواصفات:');
  console.log('=' .repeat(80));
  const sample = fixedProducts[0];
  console.log(`📦 المنتج: ${sample.id}`);
  sample.specifications.forEach((spec, index) => {
    console.log(`   ${index + 1}. ${spec.nameAr}: ${spec.valueAr}`);
  });
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
