#!/usr/bin/env node

const fs = require('fs');
const XLSX = require('xlsx');

// قراءة ملف Excel والبحث عن منتجات محددة
function findSpecificProducts() {
  try {
    console.log('📖 قراءة ملف boox2.xlsx والبحث عن المنتجات المحددة...');
    const workbook = XLSX.readFile('../boox2.xlsx');
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // قراءة البيانات كمصفوفة
    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    // المنتجات المطلوب البحث عنها
    const targetProducts = ['RH 02856', 'RH 02855', 'RH02856', 'RH02855'];
    
    console.log('\n🔍 البحث عن المنتجات المحددة...\n');
    
    let foundCount = 0;
    
    for (let i = 0; i < rawData.length; i++) {
      const row = rawData[i];
      
      // تحقق من وجود كود منتج في العمود الثاني
      if (row[1] && typeof row[1] === 'string') {
        const productCode = row[1].trim();
        
        // تحقق من تطابق مع المنتجات المطلوبة
        const isTarget = targetProducts.some(target => 
          productCode.includes(target.replace(/\s+/g, '')) || 
          productCode.includes(target) ||
          productCode === target
        );
        
        if (isTarget) {
          foundCount++;
          console.log(`📦 المنتج ${foundCount}: ${productCode}`);
          console.log(`   الصف: ${i + 1}`);
          console.log(`   كود المنتج: ${row[1] || 'غير موجود'}`);
          console.log(`   الوصف الإنجليزي: ${row[2] || 'غير موجود'}`);
          console.log(`   العمود 3: ${row[3] || 'فارغ'}`);
          console.log(`   الوصف العربي: ${row[4] || 'غير موجود'}`);
          console.log(`   العمود 5: ${row[5] || 'فارغ'}`);
          console.log(`   السعر: ${row[6] || 'غير موجود'}`);
          console.log('=' .repeat(80));
        }
      }
    }
    
    if (foundCount === 0) {
      console.log('❌ لم يتم العثور على المنتجات المحددة');
      
      // عرض عينة من البيانات للمساعدة في التشخيص
      console.log('\n📋 عينة من البيانات الموجودة:');
      for (let i = 0; i < Math.min(10, rawData.length); i++) {
        const row = rawData[i];
        if (row[1] && typeof row[1] === 'string' && row[1].includes('RH')) {
          console.log(`   الصف ${i + 1}: ${row[1]} | ${row[2] ? row[2].substring(0, 50) + '...' : 'لا يوجد وصف'}`);
        }
      }
    } else {
      console.log(`\n✅ تم العثور على ${foundCount} منتج`);
    }
    
    return foundCount;
  } catch (error) {
    console.error('❌ خطأ في قراءة ملف Excel:', error.message);
    return 0;
  }
}

// تشغيل السكريبت
if (require.main === module) {
  findSpecificProducts();
}
