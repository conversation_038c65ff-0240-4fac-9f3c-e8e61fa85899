#!/usr/bin/env node

const fs = require('fs');

// قراءة ملف المنتجات
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../chafing-dishes-products.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إصلاح العنوان العربي
function fixArabicTitle(englishDescription) {
  let title = '';
  
  // تحديد نوع المنتج
  if (/rectangular.*chafing\s*dish/i.test(englishDescription)) {
    title = 'طبق تسخين مستطيل الشكل';
  } else if (/round.*chafing\s*dish/i.test(englishDescription)) {
    title = 'طبق تسخين دائري الشكل';
  } else if (/square.*chafing\s*dish/i.test(englishDescription)) {
    title = 'طبق تسخين مربع الشكل';
  } else if (/oval.*chafing\s*dish/i.test(englishDescription)) {
    title = 'طبق تسخين بيضاوي الشكل';
  } else if (/chafing\s*dish/i.test(englishDescription)) {
    title = 'طبق تسخين';
  } else {
    title = 'طبق تسخين';
  }
  
  // إضافة المادة
  if (/stainless\s*steel/i.test(englishDescription)) {
    title += ' ستانلس ستيل';
  }
  
  // إضافة السعة
  const capacityMatch = englishDescription.match(/(\d+(?:\.\d+)?)\s*(?:liter|litre|l)\b/i);
  if (capacityMatch) {
    title += ` ${capacityMatch[1]} لتر`;
  }
  
  return title;
}

// إنشاء الوصف العربي المحسن
function generateEnhancedArabicDescription(englishDescription, arabicTitle) {
  let description = `${arabicTitle} من دروب هجر\n\n`;
  
  description += 'هذا الطبق الفاخر المصنوع من الفولاذ المقاوم للصدأ، مصمم لتسخين الطعام بكفاءة. ';
  description += 'يوفر الغطاء الزجاجي رؤية واضحة. ';
  description += 'الطبقة الداخلية الملامسة للطعام مصنوعة من الفولاذ المقاوم للصدأ S/S304 - المعروف بمقاومته للتآكل وأدائه الصحي - ';
  description += 'بينما تتميز الطبقة الخارجية بطبقة S/S201 لدعم هيكلي وتصميم أنيق.\n\n';
  
  description += 'مثالي لفعاليات تقديم الطعام، وبوفيهات الفنادق، وحفلات الطعام الرسمية. ';
  description += 'يحافظ على دفء الطعام مع توزيع حراري فعال.';
  
  return description;
}

// إنشاء المواصفات المحسنة
function generateEnhancedSpecifications(englishDescription, originalArabicDesc) {
  const specs = [
    {
      nameEn: "Material",
      nameAr: "المادة",
      valueEn: "Inner S/S304, Outer S/S201",
      valueAr: "من الداخل S/S 304، من الخارج S/S201"
    },
    {
      nameEn: "Type",
      nameAr: "النوع",
      valueEn: "Chafing Dish",
      valueAr: "طبق تسخين"
    },
    {
      nameEn: "Usage",
      nameAr: "الاستخدام",
      valueEn: "Commercial Buffet",
      valueAr: "بوفيه تجاري"
    }
  ];
  
  // إضافة السعة
  const capacityMatch = englishDescription.match(/(\d+(?:\.\d+)?)\s*(?:liter|litre|l)\b/i);
  if (capacityMatch) {
    specs.push({
      nameEn: "Capacity",
      nameAr: "السعة",
      valueEn: `${capacityMatch[1]} Liter`,
      valueAr: `${capacityMatch[1]} لتر`
    });
  }
  
  // إضافة الحجم من الوصف العربي الأصلي
  const sizeMatch = originalArabicDesc.match(/الحجم:\s*([^\n\r]+)/);
  if (sizeMatch) {
    const size = sizeMatch[1].trim();
    specs.push({
      nameEn: "Dimensions",
      nameAr: "الحجم",
      valueEn: size.replace('سم', 'cm'),
      valueAr: size
    });
  }
  
  return specs;
}

// إصلاح العنوان الإنجليزي لمعايير SEO
function optimizeEnglishTitle(title) {
  // الحد الأقصى 60 حرف لمحركات البحث
  if (title.length > 60) {
    return title.substring(0, 57) + '...';
  }
  return title;
}

// إصلاح الوصف الإنجليزي لمعايير SEO
function optimizeEnglishDescription(description) {
  // الحد الأقصى 160 حرف للوصف التعريفي
  if (description.length > 160) {
    return description.substring(0, 157) + '...';
  }
  return description;
}

// إصلاح منتج واحد
function fixProduct(product) {
  console.log(`🔧 إصلاح المنتج ${product.id}...`);
  
  // إصلاح العنوان العربي
  const newArabicTitle = fixArabicTitle(product.description);
  console.log(`   العنوان العربي: "${product.titleAr}" → "${newArabicTitle}"`);
  
  // إصلاح العنوان الإنجليزي لمعايير SEO
  const optimizedEnglishTitle = optimizeEnglishTitle(product.title);
  
  // إنشاء الوصف العربي المحسن
  const enhancedArabicDesc = generateEnhancedArabicDescription(product.description, newArabicTitle);
  
  // إنشاء المواصفات المحسنة
  const enhancedSpecs = generateEnhancedSpecifications(product.description, product.descriptionAr);
  
  // تحديث المنتج
  product.title = optimizedEnglishTitle;
  product.titleAr = newArabicTitle;
  product.descriptionAr = enhancedArabicDesc;
  product.specifications = enhancedSpecs;
  
  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إصلاح محتوى منتجات الشافنج ديش...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح المحتوى...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = fixProduct(product);
    console.log('');
    return fixed;
  });
  
  // حفظ النتيجة
  const outputPath = '../chafing-dishes-products.json';
  const jsonString = JSON.stringify(fixedProducts, null, 2);
  fs.writeFileSync(outputPath, jsonString, 'utf8');
  
  console.log(`✅ تم إصلاح ${fixedProducts.length} منتج وحفظهم في: ${outputPath}`);
  
  // عرض عينة من النتائج
  console.log('\n🔍 عينة من النتائج:');
  console.log('=' .repeat(80));
  const sample = fixedProducts[0];
  console.log(`📦 المنتج: ${sample.id}`);
  console.log(`   العنوان العربي: ${sample.titleAr}`);
  console.log(`   العنوان الإنجليزي: ${sample.title}`);
  console.log(`   عدد المواصفات: ${sample.specifications.length}`);
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
