#!/usr/bin/env node

const fs = require('fs');
const XLSX = require('xlsx');

// قراءة ملف Excel وعرض عينة
function readExcelSample() {
  try {
    console.log('📖 قراءة ملف boox2.xlsx...');
    const workbook = XLSX.readFile('../boox2.xlsx');

    console.log('\n📋 أسماء الأوراق:');
    workbook.SheetNames.forEach((name, index) => {
      console.log(`   ${index + 1}. ${name}`);
    });

    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // قراءة البيانات الخام
    console.log('\n🔍 البيانات الخام (أول 20 صف):');
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    console.log(`   النطاق: ${worksheet['!ref']}`);

    // عرض البيانات صف بصف
    for (let row = range.s.r; row <= Math.min(range.s.r + 20, range.e.r); row++) {
      let rowData = [];
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];
        rowData.push(cell ? cell.v : '');
      }
      if (rowData.some(cell => cell !== '')) {
        console.log(`   الصف ${row + 1}: [${rowData.join(' | ')}]`);
      }
    }

    // محاولة قراءة البيانات كـ JSON
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log(`\n✅ تم تحميل ${data.length} صف من الملف`);

    return data;
  } catch (error) {
    console.error('❌ خطأ في قراءة ملف Excel:', error.message);
    return null;
  }
}

// تشغيل السكريبت
if (require.main === module) {
  readExcelSample();
}
