#!/usr/bin/env node

// استيراد الدوال من السكريبت الرئيسي
const { extractArabicTitleFromEnglishDescription } = require('./fix-products-json.js');

// اختبار بعض الأوصاف
const testDescriptions = [
  "Stylish and practical Drop hajer sugar bag container Material: Melamine Size: 3.25 inches Color: White",
  "Drop hajer Deep square Soup dish Material: Melamine Size: 4.25 inches Color: White",
  "round soup dish from Drop hajer Material: Melamine Size: 3 inches Color: White",
  "oval dish from Drop hajer Material: Melamine Size: 4.5 inches Color: White",
  "Thick melamine square serving plate from drop hajer Material: Melamine Size: 10 inches Color: yellow"
];

console.log('🧪 اختبار استخراج العناوين العربية:\n');

testDescriptions.forEach((desc, index) => {
  const arabicTitle = extractArabicTitleFromEnglishDescription(desc);
  console.log(`${index + 1}. الوصف الإنجليزي:`);
  console.log(`   ${desc}`);
  console.log(`   العنوان العربي المستخرج: ${arabicTitle}`);
  console.log('');
});
