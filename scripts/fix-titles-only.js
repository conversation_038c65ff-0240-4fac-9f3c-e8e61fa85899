#!/usr/bin/env node

const fs = require('fs');

// استيراد الدوال من السكريبت الرئيسي
const { extractArabicTitleFromEnglishDescription, extractTitleFromEnglishDescription } = require('./fix-products-json.js');

// قراءة الملف
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../professional-products.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إصلاح العناوين والسعر الأصلي فقط
function fixTitlesAndPrice(product) {
  // إنشاء عناوين جديدة من الوصف الإنجليزي
  const newEnglishTitle = extractTitleFromEnglishDescription(product.description);
  const newArabicTitle = extractArabicTitleFromEnglishDescription(product.description);

  // تحديث العناوين
  product.title = newEnglishTitle;
  product.titleAr = newArabicTitle;

  // تعيين السعر الأصلي إلى 0.00 (كرقم عشري)
  product.originalPrice = parseFloat("0.00");

  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إصلاح عناوين المنتجات...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح العناوين...');
  
  // إصلاح جميع المنتجات
  const fixedProducts = products.map((product, index) => {
    const fixed = fixTitlesAndPrice(product);
    if ((index + 1) % 10 === 0) {
      console.log(`   ✅ تم إصلاح ${index + 1} منتج`);
    }
    return fixed;
  });
  
  // حفظ النتيجة
  const outputPath = '../professional-products-fixed.json';
  fs.writeFileSync(outputPath, JSON.stringify(fixedProducts, null, 2), 'utf8');
  
  console.log(`\n✅ تم إصلاح العناوين وحفظها في: ${outputPath}`);
}

// تشغيل السكربت
if (require.main === module) {
  main();
}
