#!/usr/bin/env node

const fs = require('fs');
const XLSX = require('xlsx');

// قراءة ملف Excel
function readExcelFile() {
  try {
    console.log('📖 قراءة ملف boox2.xlsx...');
    const workbook = XLSX.readFile('../boox2.xlsx');
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // قراءة البيانات كمصفوفة
    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    // تصفية البيانات وتحويلها إلى كائنات
    const products = [];

    for (let i = 0; i < rawData.length; i++) {
      const row = rawData[i];

      // تحقق من وجود كود منتج (العمود الثاني)
      if (row[1] && typeof row[1] === 'string' && row[1].includes('RH')) {
        const productCode = row[1].trim();
        const englishDescription = row[2] || '';
        const arabicDescription = row[4] || '';
        const price = parseFloat(row[6]) || 0;

        // تحقق من وجود وصف إنجليزي
        if (englishDescription && englishDescription.length > 10) {
          products.push({
            code: productCode,
            englishDescription: englishDescription.trim(),
            arabicDescription: arabicDescription.trim(),
            price: price
          });
        }
      }
    }

    console.log(`✅ تم استخراج ${products.length} منتج من ${rawData.length} صف`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة ملف Excel:', error.message);
    return null;
  }
}

// تنظيف النص من "Drop Hajer" أو "دروب هجر"
function cleanText(text) {
  if (!text) return '';
  return text
    .replace(/drop\s*hajer/gi, '')
    .replace(/دروب\s*هجر/g, '')
    .trim()
    .replace(/\s+/g, ' ');
}

// إنشاء عنوان إنجليزي منطقي
function generateEnglishTitle(description) {
  if (!description) return 'Chafing Dish';
  
  const cleanDesc = cleanText(description);
  
  // البحث عن أنماط المنتجات
  let title = '';
  
  if (/chafing\s*dish/i.test(cleanDesc)) {
    title = 'Chafing Dish';
  } else if (/food\s*warmer/i.test(cleanDesc)) {
    title = 'Food Warmer';
  } else if (/buffet\s*server/i.test(cleanDesc)) {
    title = 'Buffet Server';
  } else if (/serving\s*tray/i.test(cleanDesc)) {
    title = 'Serving Tray';
  } else if (/warming\s*tray/i.test(cleanDesc)) {
    title = 'Warming Tray';
  } else {
    title = 'Chafing Dish';
  }
  
  // إضافة المادة
  if (/stainless\s*steel/i.test(cleanDesc)) {
    title = 'Stainless Steel ' + title;
  }
  
  // إضافة الحجم
  const sizeMatch = cleanDesc.match(/(\d+(?:\.\d+)?)\s*(?:liter|litre|l|qt|quart)/i);
  if (sizeMatch) {
    title += ` ${sizeMatch[1]}L`;
  }
  
  // إضافة الشكل
  if (/round/i.test(cleanDesc)) {
    title = 'Round ' + title;
  } else if (/rectangular/i.test(cleanDesc)) {
    title = 'Rectangular ' + title;
  } else if (/oval/i.test(cleanDesc)) {
    title = 'Oval ' + title;
  }
  
  return title.length > 60 ? title.substring(0, 57) + '...' : title;
}

// إنشاء عنوان عربي منطقي
function generateArabicTitle(description) {
  if (!description) return 'طبق تسخين عالي الجودة';
  
  const cleanDesc = cleanText(description);
  
  let title = '';
  
  // تحديد نوع المنتج
  if (/chafing\s*dish/i.test(cleanDesc)) {
    title = 'طبق تسخين';
  } else if (/food\s*warmer/i.test(cleanDesc)) {
    title = 'جهاز تدفئة طعام';
  } else if (/buffet\s*server/i.test(cleanDesc)) {
    title = 'خادم بوفيه';
  } else if (/serving\s*tray/i.test(cleanDesc)) {
    title = 'صينية تقديم';
  } else if (/warming\s*tray/i.test(cleanDesc)) {
    title = 'صينية تدفئة';
  } else {
    title = 'طبق تسخين';
  }
  
  // إضافة المادة
  if (/stainless\s*steel/i.test(cleanDesc)) {
    title += ' ستانلس ستيل';
  }
  
  // إضافة الشكل
  if (/round/i.test(cleanDesc)) {
    title = 'دائري ' + title;
  } else if (/rectangular/i.test(cleanDesc)) {
    title = 'مستطيل ' + title;
  } else if (/oval/i.test(cleanDesc)) {
    title = 'بيضاوي ' + title;
  }
  
  // إضافة الحجم
  const sizeMatch = cleanDesc.match(/(\d+(?:\.\d+)?)\s*(?:liter|litre|l|qt|quart)/i);
  if (sizeMatch) {
    title += ` ${sizeMatch[1]} لتر`;
  }
  
  // إضافة وصف الجودة
  title += ' عالي الجودة';
  
  return title.length > 60 ? title.substring(0, 57) + '...' : title;
}

// إنشاء وصف عربي
function generateArabicDescription(englishDesc) {
  if (!englishDesc) return 'طبق تسخين عالي الجودة مصنوع من مواد متينة ومقاومة للحرارة. مثالي للاستخدام في البوفيهات والمطاعم والفنادق. سهل التنظيف والصيانة مع تصميم أنيق وعملي.';
  
  const cleanDesc = cleanText(englishDesc);
  let arabicDesc = '';
  
  // تحديد نوع المنتج للوصف
  if (/chafing\s*dish/i.test(cleanDesc)) {
    arabicDesc = 'طبق تسخين عالي الجودة مصنوع من مواد متينة ومقاومة للحرارة. مثالي للاستخدام في البوفيهات والمطاعم والفنادق لتقديم الطعام الساخن. يحافظ على درجة حرارة الطعام لفترات طويلة مع تصميم أنيق وعملي. سهل التنظيف والصيانة ومقاوم للصدأ والتآكل.';
  } else if (/food\s*warmer/i.test(cleanDesc)) {
    arabicDesc = 'جهاز تدفئة طعام عالي الجودة مصنوع من مواد متينة ومقاومة للحرارة. مثالي للحفاظ على درجة حرارة الطعام في المطاعم والفنادق. تصميم عملي وأنيق يناسب جميع أنواع البوفيهات. سهل الاستخدام والتنظيف مع ضمان الأداء الممتاز.';
  } else {
    arabicDesc = 'منتج بوفيه عالي الجودة مصنوع من مواد متينة ومقاومة للحرارة. مثالي للاستخدام في البوفيهات والمطاعم والفنادق. سهل التنظيف والصيانة مع تصميم أنيق وعملي. مقاوم للصدأ والتآكل ويضمن الأداء الممتاز لفترات طويلة.';
  }
  
  return arabicDesc.length > 200 ? arabicDesc.substring(0, 197) + '...' : arabicDesc;
}

// إنشاء مميزات المنتج
function generateFeatures(description) {
  const features = {
    en: [],
    ar: []
  };
  
  // مميزات أساسية حسب نوع المنتج
  if (/chafing\s*dish/i.test(description)) {
    features.en.push('Maintains food temperature for extended periods');
    features.ar.push('يحافظ على درجة حرارة الطعام لفترات طويلة');
    
    features.en.push('Perfect for buffet service');
    features.ar.push('مثالي لخدمة البوفيه');
  }
  
  if (/stainless\s*steel/i.test(description)) {
    features.en.push('Durable stainless steel construction');
    features.ar.push('مصنوع من الستانلس ستيل المتين');
    
    features.en.push('Rust and corrosion resistant');
    features.ar.push('مقاوم للصدأ والتآكل');
  }
  
  // مميزات عامة
  features.en.push('Easy to clean and maintain');
  features.ar.push('سهل التنظيف والصيانة');
  
  features.en.push('Professional grade quality');
  features.ar.push('جودة احترافية عالية');
  
  features.en.push('Elegant and practical design');
  features.ar.push('تصميم أنيق وعملي');
  
  features.en.push('Suitable for commercial use');
  features.ar.push('مناسب للاستخدام التجاري');
  
  return {
    en: features.en.slice(0, 6),
    ar: features.ar.slice(0, 6)
  };
}

// تحويل منتج واحد إلى JSON
function convertProductToJson(product, index) {
  // استخراج البيانات من المنتج
  const productCode = product.code.replace(/\s+/g, '');
  const englishDesc = product.englishDescription;
  const arabicDesc = product.arabicDescription;
  const price = product.price;
  
  // إنشاء العناوين
  const englishTitle = generateEnglishTitle(englishDesc);
  const arabicTitle = generateArabicTitle(englishDesc);

  // إنشاء الأوصاف
  const cleanEnglishDesc = cleanText(englishDesc);
  const finalArabicDesc = arabicDesc || generateArabicDescription(englishDesc, arabicTitle);
  
  // إنشاء المميزات
  const features = generateFeatures(englishDesc);
  
  // إنشاء المواصفات
  const specifications = [
    {
      nameEn: "Material",
      nameAr: "المادة",
      valueEn: /stainless\s*steel/i.test(englishDesc) ? "Stainless Steel" : "Metal",
      valueAr: /stainless\s*steel/i.test(englishDesc) ? "ستانلس ستيل" : "معدن"
    },
    {
      nameEn: "Type",
      nameAr: "النوع",
      valueEn: "Chafing Dish",
      valueAr: "طبق تسخين"
    },
    {
      nameEn: "Usage",
      nameAr: "الاستخدام",
      valueEn: "Commercial Buffet",
      valueAr: "بوفيه تجاري"
    }
  ];
  
  // إضافة مواصفة الحجم إذا وجدت
  const sizeMatch = englishDesc.match(/(\d+(?:\.\d+)?)\s*(?:liter|litre|l|qt|quart)/i);
  if (sizeMatch) {
    specifications.push({
      nameEn: "Capacity",
      nameAr: "السعة",
      valueEn: `${sizeMatch[1]} Liter`,
      valueAr: `${sizeMatch[1]} لتر`
    });
  }
  
  return {
    id: productCode,
    title: englishTitle.length > 60 ? englishTitle.substring(0, 57) + '...' : englishTitle,
    titleAr: arabicTitle,
    description: cleanEnglishDesc.length > 200 ? cleanEnglishDesc.substring(0, 197) + '...' : cleanEnglishDesc,
    descriptionAr: finalArabicDesc.length > 200 ? finalArabicDesc.substring(0, 197) + '...' : finalArabicDesc,
    price: price,
    originalPrice: 0.00,
    available: true,
    categoryId: "Buffetware",
    subcategoryId: "Chafing-Dishes",
    features: features.en,
    featuresAr: features.ar,
    specifications: specifications,
    isActive: true,
    isFeatured: false
  };
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء تحويل ملف boox2.xlsx إلى JSON...\n');
  
  const excelData = readExcelFile();
  if (!excelData) return;
  
  console.log('🔄 تحويل البيانات إلى منتجات...');
  
  const products = excelData.map((productData, index) => {
    try {
      const product = convertProductToJson(productData, index);
      if ((index + 1) % 5 === 0) {
        console.log(`   ✅ تم تحويل ${index + 1} منتج`);
      }
      return product;
    } catch (error) {
      console.error(`❌ خطأ في تحويل المنتج ${index + 1}:`, error.message);
      return null;
    }
  }).filter(product => product !== null);
  
  // حفظ النتيجة
  const outputPath = '../chafing-dishes-products.json';
  const jsonString = JSON.stringify(products, null, 2);
  fs.writeFileSync(outputPath, jsonString, 'utf8');
  
  console.log(`\n✅ تم تحويل ${products.length} منتج وحفظهم في: ${outputPath}`);
  
  // عرض عينة
  console.log('\n🔍 عينة من المنتجات:');
  console.log('=' .repeat(80));
  products.slice(0, 2).forEach((product, index) => {
    console.log(`\n📦 المنتج ${index + 1}:`);
    console.log(`   ID: ${product.id}`);
    console.log(`   العنوان العربي: ${product.titleAr}`);
    console.log(`   العنوان الإنجليزي: ${product.title}`);
    console.log(`   السعر: ${product.price}`);
    console.log(`   السعر الأصلي: ${product.originalPrice}`);
  });
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
