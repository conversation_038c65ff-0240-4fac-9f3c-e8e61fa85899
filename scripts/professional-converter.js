#!/usr/bin/env node

const fs = require('fs');
const XLSX = require('xlsx');

// قراءة ملف Excel بدقة
function readExcelData() {
  try {
    console.log('📖 قراءة ملف boox2.xlsx...');
    const workbook = XLSX.readFile('../boox2.xlsx');
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    const products = [];
    
    for (let i = 0; i < rawData.length; i++) {
      const row = rawData[i];
      
      if (row[1] && typeof row[1] === 'string' && row[1].includes('RH')) {
        const productCode = row[1].trim();
        const englishDescription = row[2] || '';
        const arabicDescription = row[4] || '';
        const price = parseFloat(row[6]) || 0;
        
        if (englishDescription && englishDescription.length > 10) {
          products.push({
            code: productCode.replace(/\s+/g, ''),
            englishDescription: englishDescription.trim(),
            arabicDescription: arabicDescription.trim(),
            price: Math.round(price * 100) / 100
          });
        }
      }
    }
    
    console.log(`✅ تم استخراج ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// تحديد نوع المنتج بناءً على الوصف
function determineProductType(englishDesc, arabicDesc) {
  const engLower = englishDesc.toLowerCase();
  const arabicLower = arabicDesc.toLowerCase();
  
  if (engLower.includes('thermos') || arabicLower.includes('ترمس')) {
    return 'thermos';
  } else if (engLower.includes('chafing dish') || arabicLower.includes('طبق تسخين')) {
    return 'chafing_dish';
  } else if (engLower.includes('warmer') || arabicLower.includes('سخان')) {
    return 'warmer';
  } else if (engLower.includes('burner') || arabicLower.includes('موقد')) {
    return 'burner';
  } else {
    return 'chafing_dish'; // افتراضي
  }
}

// استخراج العنوان الإنجليزي من أول سطر في الوصف
function extractEnglishTitle(product) {
  const desc = product.englishDescription.trim();

  // أخذ أول سطر كعنوان
  const firstLine = desc.split('\n')[0].trim();

  // تنظيف العنوان من "Drop Hajer"
  let title = firstLine
    .replace(/drop\s+hajer/gi, '')
    .replace(/from\s+drop\s+hajer/gi, '')
    .replace(/\s+/g, ' ')
    .trim();

  // إذا كان العنوان فارغ أو قصير جداً، استخدم جملة كاملة
  if (!title || title.length < 10) {
    const sentences = desc.split(/[.!?]/);
    title = sentences[0].trim()
      .replace(/drop\s+hajer/gi, '')
      .replace(/from\s+drop\s+hajer/gi, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  // تحويل إلى Title Case
  title = title.replace(/\w\S*/g, (txt) =>
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );

  return title.length > 60 ? title.substring(0, 57) + '...' : title;
}

// استخراج العنوان العربي من أول سطر في الوصف
function extractArabicTitle(product) {
  const desc = product.arabicDescription.trim();

  // أخذ أول سطر كعنوان
  const firstLine = desc.split('\n')[0].trim();

  // تنظيف العنوان من "دروب هجر" و "دروب هاجر"
  let title = firstLine
    .replace(/من\s+دروب\s+هاجر/gi, '')
    .replace(/دروب\s+هاجر/gi, '')
    .replace(/من\s+دروب\s+هجر/gi, '')
    .replace(/دروب\s+هجر/gi, '')
    .replace(/\s+/g, ' ')
    .trim();

  // إذا كان العنوان فارغ أو قصير جداً، استخدم جملة كاملة
  if (!title || title.length < 10) {
    const sentences = desc.split(/[.!؟]/);
    title = sentences[0].trim()
      .replace(/من\s+دروب\s+هاجر/gi, '')
      .replace(/دروب\s+هاجر/gi, '')
      .replace(/من\s+دروب\s+هجر/gi, '')
      .replace(/دروب\s+هجر/gi, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  return title.length > 60 ? title.substring(0, 57) + '...' : title;
}

// إنشاء المميزات بناءً على النوع
function generateFeatures(type) {
  if (type === 'thermos') {
    return {
      en: [
        "Double-wall insulation for temperature retention",
        "Perfect for hot and cold beverages",
        "Reduces condensation and heat transfer",
        "Ideal for home, office, or travel use",
        "High-quality stainless steel construction",
        "Maintains liquid temperature for extended periods"
      ],
      ar: [
        "عزل مزدوج الجدار للحفاظ على درجة الحرارة",
        "مثالي للمشروبات الساخنة والباردة",
        "يقلل من التكثف وانتقال الحرارة",
        "مثالي للاستخدام في المنزل أو المكتب أو السفر",
        "مصنوع من الستانلس ستيل عالي الجودة",
        "يحافظ على درجة حرارة السوائل لفترات طويلة"
      ]
    };
  } else {
    return {
      en: [
        "High-quality stainless steel construction",
        "Heat-resistant and durable design",
        "Easy to clean and maintain",
        "Professional grade for commercial use",
        "Elegant and practical design",
        "Inner S/S304, Outer S/S201 layers"
      ],
      ar: [
        "مصنوع من الستانلس ستيل عالي الجودة",
        "مقاوم للحرارة وتصميم متين",
        "سهل التنظيف والصيانة",
        "جودة احترافية للاستخدام التجاري",
        "تصميم أنيق وعملي",
        "طبقات من الداخل S/S304، من الخارج S/S201"
      ]
    };
  }
}

// إنشاء المواصفات بناءً على النوع
function generateSpecifications(product, type) {
  const specs = [];
  
  // المادة
  if (type === 'thermos') {
    specs.push({
      nameEn: "Material",
      nameAr: "المادة",
      valueEn: "Stainless Steel",
      valueAr: "ستانلس ستيل"
    });
  } else {
    specs.push({
      nameEn: "Material",
      nameAr: "المادة",
      valueEn: "Inner S/S304, Outer S/S201",
      valueAr: "من الداخل S/S 304، من الخارج S/S201"
    });
  }
  
  // النوع
  if (type === 'thermos') {
    specs.push({
      nameEn: "Type",
      nameAr: "النوع",
      valueEn: "Hot Water Thermos",
      valueAr: "ترمس ماء ساخن"
    });
  } else {
    specs.push({
      nameEn: "Type",
      nameAr: "النوع",
      valueEn: "Chafing Dish",
      valueAr: "طبق تسخين"
    });
  }
  
  // السعة
  const capacityMatch = product.englishDescription.match(/(\d+(?:\.\d+)?)\s*(?:liter|litre|l)\b/i);
  if (capacityMatch) {
    specs.push({
      nameEn: "Capacity",
      nameAr: "السعة",
      valueEn: `${capacityMatch[1]} Liter`,
      valueAr: `${capacityMatch[1]} لتر`
    });
  }
  
  // الحجم (تقديري بناءً على السعة)
  if (capacityMatch) {
    const capacity = parseFloat(capacityMatch[1]);
    let dimensions = '';
    if (capacity >= 9) dimensions = '57.5*43*32 سم';
    else if (capacity >= 6) dimensions = '50*38*28 سم';
    else if (capacity >= 4) dimensions = '45*35*25 سم';
    else if (capacity >= 2) dimensions = '35*25*20 سم';
    else dimensions = '30*20*18 سم';
    
    specs.push({
      nameEn: "Dimensions",
      nameAr: "الحجم",
      valueEn: dimensions.replace('سم', 'cm'),
      valueAr: dimensions
    });
  }
  
  // الاستخدام
  specs.push({
    nameEn: "Usage",
    nameAr: "الاستخدام",
    valueEn: "Commercial Buffet",
    valueAr: "بوفيه تجاري"
  });
  
  return specs;
}

// تحويل منتج واحد إلى JSON
function convertToProduct(rawProduct) {
  const type = determineProductType(rawProduct.englishDescription, rawProduct.arabicDescription);
  
  const englishTitle = extractEnglishTitle(rawProduct);
  const arabicTitle = extractArabicTitle(rawProduct);
  const features = generateFeatures(type);
  const specifications = generateSpecifications(rawProduct, type);
  
  // تنظيف الأوصاف
  const cleanEnglishDesc = rawProduct.englishDescription
    .replace(/drop\s+hajer/gi, '')
    .replace(/\s+/g, ' ')
    .trim();
  
  const cleanArabicDesc = rawProduct.arabicDescription
    .replace(/دروب\s+هجر/gi, '')
    .trim();
  
  return {
    id: rawProduct.code,
    title: englishTitle,
    titleAr: arabicTitle,
    description: cleanEnglishDesc.length > 200 ? cleanEnglishDesc.substring(0, 197) + '...' : cleanEnglishDesc,
    descriptionAr: cleanArabicDesc.length > 200 ? cleanArabicDesc.substring(0, 197) + '...' : cleanArabicDesc,
    price: rawProduct.price,
    originalPrice: 0.00,
    available: true,
    categoryId: "Buffetware",
    subcategoryId: "Chafing-Dishes",
    features: features.en,
    featuresAr: features.ar,
    specifications: specifications,
    isActive: true,
    isFeatured: false
  };
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء التحويل الاحترافي...\n');
  
  const rawProducts = readExcelData();
  if (!rawProducts || rawProducts.length === 0) {
    console.error('❌ فشل في قراءة البيانات');
    return;
  }
  
  console.log('🔄 تحويل المنتجات...');
  
  const products = rawProducts.map((rawProduct, index) => {
    const product = convertToProduct(rawProduct);
    console.log(`✅ ${index + 1}. ${product.id}: ${product.titleAr}`);
    return product;
  });
  
  // حفظ النتيجة
  const outputPath = '../chafing-dishes-products-professional.json';
  const jsonString = JSON.stringify(products, null, 2);
  fs.writeFileSync(outputPath, jsonString, 'utf8');
  
  console.log(`\n✅ تم إنشاء ${products.length} منتج احترافي في: ${outputPath}`);
  
  // إحصائيات
  const thermos = products.filter(p => p.titleAr.includes('ترمس')).length;
  const chafing = products.filter(p => p.titleAr.includes('طبق تسخين')).length;
  
  console.log('\n📊 الإحصائيات:');
  console.log(`   - ترمس: ${thermos} منتج`);
  console.log(`   - طبق تسخين: ${chafing} منتج`);
  console.log(`   - أخرى: ${products.length - thermos - chafing} منتج`);
}

if (require.main === module) {
  main();
}
