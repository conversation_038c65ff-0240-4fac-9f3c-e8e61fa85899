#!/usr/bin/env node

const fs = require('fs');

// قراءة الملف
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../professional-products-fixed.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إصلاح السعر الأصلي ليظهر كـ 0.00
function fixOriginalPriceFormat(product) {
  // تعيين السعر الأصلي كرقم عشري بدقة
  product.originalPrice = parseFloat("0.00");
  return product;
}

// دالة مخصصة لحفظ JSON مع تنسيق الأسعار
function saveProductsWithFormattedPrices(products, filePath) {
  // تحويل إلى JSON مع تنسيق مخصص
  const jsonString = JSON.stringify(products, (key, value) => {
    // إذا كان المفتاح originalPrice والقيمة 0، نعيدها كـ 0.00
    if (key === 'originalPrice' && value === 0) {
      return 0.00;
    }
    return value;
  }, 2);
  
  // استبدال "originalPrice": 0 بـ "originalPrice": 0.00
  const formattedJson = jsonString.replace(/"originalPrice":\s*0(?![.])/g, '"originalPrice": 0.00');
  
  fs.writeFileSync(filePath, formattedJson, 'utf8');
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إصلاح تنسيق السعر الأصلي...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح تنسيق السعر الأصلي...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = fixOriginalPriceFormat(product);
    if ((index + 1) % 10 === 0) {
      console.log(`   ✅ تم إصلاح ${index + 1} منتج`);
    }
    return fixed;
  });
  
  // حفظ النتيجة مع التنسيق المخصص
  const outputPath = '../professional-products-fixed.json';
  saveProductsWithFormattedPrices(fixedProducts, outputPath);
  
  console.log(`\n✅ تم إصلاح تنسيق السعر الأصلي وحفظه في: ${outputPath}`);
  
  // التحقق من النتيجة
  console.log('\n🔍 فحص النتيجة...');
  const savedContent = fs.readFileSync(outputPath, 'utf8');
  const firstOriginalPriceMatch = savedContent.match(/"originalPrice":\s*([0-9.]+)/);
  if (firstOriginalPriceMatch) {
    console.log(`   السعر الأصلي في الملف: ${firstOriginalPriceMatch[1]}`);
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
