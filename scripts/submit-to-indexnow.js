#!/usr/bin/env node

/**
 * سكريبت لإرسال الصفحات الرئيسية إلى IndexNow
 * يمكن تشغيله يدوياً أو عبر cron job
 */

const https = require('https');

const API_KEY = 'f6d29396e4f1414ea4bffac192eb5e7f';
const BASE_URL = 'https://droobhajer.com';
const KEY_LOCATION = `${BASE_URL}/${API_KEY}.txt`;

// الصفحات الرئيسية للإرسال
const MAIN_PAGES = [
  `${BASE_URL}/ar`,
  `${BASE_URL}/en`,
  `${BASE_URL}/ar/products`,
  `${BASE_URL}/en/products`,
  `${BASE_URL}/ar/categories`,
  `${BASE_URL}/en/categories`,
  `${BASE_URL}/ar/about`,
  `${BASE_URL}/en/about`,
  `${BASE_URL}/ar/contact`,
  `${BASE_URL}/en/contact`,
  `${BASE_URL}/sitemap.xml`,
  `${BASE_URL}/robots.txt`
];

/**
 * إرسال URLs إلى IndexNow
 */
async function submitToIndexNow(urls) {
  const submission = {
    host: new URL(BASE_URL).hostname,
    key: API_KEY,
    keyLocation: KEY_LOCATION,
    urlList: urls
  };

  const data = JSON.stringify(submission);
  
  const options = {
    hostname: 'api.indexnow.org',
    port: 443,
    path: '/indexnow',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length,
      'User-Agent': 'DROOB-HAJER-IndexNow/1.0'
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve({
            success: true,
            statusCode: res.statusCode,
            message: `تم إرسال ${urls.length} URL بنجاح`,
            response: responseData
          });
        } else {
          reject({
            success: false,
            statusCode: res.statusCode,
            message: `فشل الإرسال: ${res.statusCode}`,
            response: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        success: false,
        message: `خطأ في الشبكة: ${error.message}`,
        error: error
      });
    });

    req.write(data);
    req.end();
  });
}

/**
 * التحقق من صحة ملف المفتاح
 */
async function verifyKeyFile() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: new URL(BASE_URL).hostname,
      port: 443,
      path: `/${API_KEY}.txt`,
      method: 'GET'
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200 && data.trim() === API_KEY) {
          resolve(true);
        } else {
          reject(new Error(`ملف المفتاح غير صحيح. الحالة: ${res.statusCode}, المحتوى: ${data}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

/**
 * الدالة الرئيسية
 */
async function main() {
  console.log('🚀 بدء إرسال الصفحات إلى IndexNow...');
  
  try {
    // التحقق من ملف المفتاح
    console.log('🔍 التحقق من ملف المفتاح...');
    await verifyKeyFile();
    console.log('✅ ملف المفتاح صحيح');

    // إرسال الصفحات
    console.log(`📤 إرسال ${MAIN_PAGES.length} صفحة إلى IndexNow...`);
    const result = await submitToIndexNow(MAIN_PAGES);
    
    console.log('✅ تم الإرسال بنجاح!');
    console.log(`📊 الحالة: ${result.statusCode}`);
    console.log(`📝 الرسالة: ${result.message}`);
    
    if (result.response) {
      console.log(`📄 الاستجابة: ${result.response}`);
    }

  } catch (error) {
    console.error('❌ فشل في الإرسال:');
    console.error(`📝 الرسالة: ${error.message || error}`);
    
    if (error.statusCode) {
      console.error(`📊 الحالة: ${error.statusCode}`);
    }
    
    if (error.response) {
      console.error(`📄 الاستجابة: ${error.response}`);
    }
    
    process.exit(1);
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  main();
}

module.exports = {
  submitToIndexNow,
  verifyKeyFile,
  MAIN_PAGES,
  API_KEY,
  BASE_URL
};
