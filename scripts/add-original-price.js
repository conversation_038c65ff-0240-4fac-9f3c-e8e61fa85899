#!/usr/bin/env node

const fs = require('fs');

// قراءة الملف
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../professional-products-titles-fixed.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إضافة originalPrice إذا لم يكن موجود
function addOriginalPrice(product) {
  if (!product.hasOwnProperty('originalPrice')) {
    product.originalPrice = 0.00;
    console.log(`   ✅ تمت إضافة originalPrice للمنتج ${product.id}`);
  } else {
    product.originalPrice = 0.00;
  }
  return product;
}

// دالة مخصصة لحفظ JSON مع تنسيق الأسعار
function saveProductsWithFormattedPrices(products, filePath) {
  // تحويل إلى JSON مع تنسيق مخصص
  const jsonString = JSON.stringify(products, null, 2);
  
  // استبدال "originalPrice": 0 بـ "originalPrice": 0.00
  const formattedJson = jsonString.replace(/"originalPrice":\s*0(?![.])/g, '"originalPrice": 0.00');
  
  fs.writeFileSync(filePath, formattedJson, 'utf8');
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إضافة originalPrice للمنتجات...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إضافة originalPrice...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = addOriginalPrice(product);
    if ((index + 1) % 10 === 0) {
      console.log(`   📊 تم معالجة ${index + 1} منتج`);
    }
    return fixed;
  });
  
  // حفظ النتيجة مع التنسيق المخصص
  const outputPath = '../professional-products-titles-fixed.json';
  saveProductsWithFormattedPrices(fixedProducts, outputPath);
  
  console.log(`\n✅ تم إضافة originalPrice وحفظه في: ${outputPath}`);
  
  // التحقق من النتيجة
  console.log('\n🔍 فحص النتيجة...');
  const savedContent = fs.readFileSync(outputPath, 'utf8');
  const originalPriceMatches = savedContent.match(/"originalPrice":\s*0\.00/g);
  console.log(`   عدد المنتجات مع originalPrice: 0.00 = ${originalPriceMatches ? originalPriceMatches.length : 0}`);
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
