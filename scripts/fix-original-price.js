#!/usr/bin/env node

const fs = require('fs');

// قراءة الملف
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../professional-products-fixed.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إصلاح السعر الأصلي
function fixOriginalPrice(product) {
  product.originalPrice = 0.00;
  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إصلاح السعر الأصلي...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إصلاح السعر الأصلي...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = fixOriginalPrice(product);
    if ((index + 1) % 10 === 0) {
      console.log(`   ✅ تم إصلاح ${index + 1} منتج`);
    }
    return fixed;
  });
  
  // حفظ النتيجة
  const outputPath = '../professional-products-fixed.json';
  fs.writeFileSync(outputPath, JSON.stringify(fixedProducts, null, 2), 'utf8');
  
  console.log(`\n✅ تم إصلاح السعر الأصلي وحفظه في: ${outputPath}`);
  
  // التحقق من النتيجة
  const firstProduct = fixedProducts[0];
  console.log(`\n🔍 مثال على السعر الأصلي: ${firstProduct.originalPrice} (نوع البيانات: ${typeof firstProduct.originalPrice})`);
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
