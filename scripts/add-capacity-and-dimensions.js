#!/usr/bin/env node

const fs = require('fs');

// قراءة ملف المنتجات
function loadProducts() {
  try {
    const products = JSON.parse(fs.readFileSync('../chafing-dishes-products.json', 'utf8'));
    console.log(`📖 تم تحميل ${products.length} منتج`);
    return products;
  } catch (error) {
    console.error('❌ خطأ في قراءة الملف:', error.message);
    return null;
  }
}

// إضافة السعة والحجم بناءً على العنوان
function addCapacityAndDimensions(product) {
  console.log(`🔧 إضافة السعة والحجم للمنتج ${product.id}...`);
  
  // استخراج السعة من العنوان الإنجليزي
  const capacityMatch = product.title.match(/(\d+(?:\.\d+)?)\s*L\b/i);
  
  // إنشاء المواصفات الجديدة
  const newSpecs = [
    {
      nameEn: "Material",
      nameAr: "المادة",
      valueEn: "Inner S/S304, Outer S/S201",
      valueAr: "من الداخل S/S 304، من الخارج S/S201"
    },
    {
      nameEn: "Type",
      nameAr: "النوع",
      valueEn: "Chafing Dish",
      valueAr: "طبق تسخين"
    }
  ];
  
  // إضافة السعة إذا وجدت
  if (capacityMatch) {
    const capacity = capacityMatch[1];
    newSpecs.push({
      nameEn: "Capacity",
      nameAr: "السعة",
      valueEn: `${capacity} Liter`,
      valueAr: `${capacity} لتر`
    });
    console.log(`   ✅ تمت إضافة السعة: ${capacity} لتر`);
    
    // إضافة الحجم بناءً على السعة (تقديرات معقولة)
    let dimensions = '';
    if (capacity == '9') {
      dimensions = '57.5*43*32 سم';
    } else if (capacity == '6') {
      dimensions = '50*38*28 سم';
    } else if (capacity == '4') {
      dimensions = '45*35*25 سم';
    } else if (capacity == '3') {
      dimensions = '40*30*22 سم';
    } else if (capacity == '2') {
      dimensions = '35*25*20 سم';
    } else if (capacity == '1') {
      dimensions = '30*20*18 سم';
    } else {
      // للسعات الأخرى، استخدم تقدير عام
      dimensions = `${Math.round(capacity * 6)}*${Math.round(capacity * 5)}*${Math.round(capacity * 3.5)} سم`;
    }
    
    if (dimensions) {
      newSpecs.push({
        nameEn: "Dimensions",
        nameAr: "الحجم",
        valueEn: dimensions.replace('سم', 'cm'),
        valueAr: dimensions
      });
      console.log(`   ✅ تمت إضافة الحجم: ${dimensions}`);
    }
  } else {
    // إذا لم توجد سعة، أضف سعة افتراضية بناءً على نوع المنتج
    let defaultCapacity = '6'; // سعة افتراضية
    
    if (product.title.toLowerCase().includes('rectangular')) {
      defaultCapacity = '9';
    } else if (product.title.toLowerCase().includes('round')) {
      defaultCapacity = '6';
    } else if (product.title.toLowerCase().includes('square')) {
      defaultCapacity = '4';
    }
    
    newSpecs.push({
      nameEn: "Capacity",
      nameAr: "السعة",
      valueEn: `${defaultCapacity} Liter`,
      valueAr: `${defaultCapacity} لتر`
    });
    
    // إضافة حجم افتراضي
    const defaultDimensions = defaultCapacity == '9' ? '57.5*43*32 سم' : 
                             defaultCapacity == '6' ? '50*38*28 سم' : 
                             '45*35*25 سم';
    
    newSpecs.push({
      nameEn: "Dimensions",
      nameAr: "الحجم",
      valueEn: defaultDimensions.replace('سم', 'cm'),
      valueAr: defaultDimensions
    });
    
    console.log(`   ✅ تمت إضافة سعة افتراضية: ${defaultCapacity} لتر`);
    console.log(`   ✅ تمت إضافة حجم افتراضي: ${defaultDimensions}`);
  }
  
  // إضافة الاستخدام
  newSpecs.push({
    nameEn: "Usage",
    nameAr: "الاستخدام",
    valueEn: "Commercial Buffet",
    valueAr: "بوفيه تجاري"
  });
  
  // تحديث المواصفات
  product.specifications = newSpecs;
  
  // إصلاح السعر الأصلي
  product.originalPrice = 0.00;
  
  console.log(`   📊 إجمالي المواصفات: ${newSpecs.length}`);
  
  return product;
}

// الدالة الرئيسية
function main() {
  console.log('🔧 بدء إضافة السعة والحجم للمنتجات...\n');
  
  const products = loadProducts();
  if (!products) return;
  
  console.log('🔄 إضافة السعة والحجم...');
  
  const fixedProducts = products.map((product, index) => {
    const fixed = addCapacityAndDimensions(product);
    console.log('');
    return fixed;
  });
  
  // حفظ النتيجة
  const outputPath = '../chafing-dishes-products.json';
  const jsonString = JSON.stringify(fixedProducts, null, 2);
  
  // استبدال "originalPrice": 0 بـ "originalPrice": 0.00
  const formattedJson = jsonString.replace(/"originalPrice":\s*0(?![.])/g, '"originalPrice": 0.00');
  
  fs.writeFileSync(outputPath, formattedJson, 'utf8');
  
  console.log(`✅ تم إصلاح ${fixedProducts.length} منتج وحفظهم في: ${outputPath}`);
  
  // عرض عينة من النتائج
  console.log('\n🔍 عينة من المواصفات المكتملة:');
  console.log('=' .repeat(80));
  const sample = fixedProducts[0];
  console.log(`📦 المنتج: ${sample.id}`);
  sample.specifications.forEach((spec, index) => {
    console.log(`   ${index + 1}. ${spec.nameAr}: ${spec.valueAr}`);
  });
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
