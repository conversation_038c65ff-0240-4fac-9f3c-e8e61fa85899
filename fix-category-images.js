const mysql = require('mysql2/promise');

async function fixCategoryImages() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'droobhajer_db'
    });

    console.log('🔧 إصلاح صور الفئات وإزالة الروابط المعطلة...');

    // أولاً: جلب جميع الفئات الحالية لمعرفة الروابط المشكلة
    const [currentCategories] = await connection.execute('SELECT id, name, name_ar, image_url FROM categories WHERE deleted_at IS NULL');

    console.log('\n📋 الفئات الحالية:');
    currentCategories.forEach(cat => {
      console.log(`- ${cat.name_ar} (${cat.name})`);
      console.log(`  الصورة الحالية: ${cat.image_url}`);
      console.log('');
    });

    // تحديث جميع الروابط المعطلة من freerangestock.com و istockphoto.com
    console.log('🔄 تحديث الروابط المعطلة...');

    // تحديث الفئات بصور Unsplash موثوقة
    await connection.execute(`
      UPDATE categories
      SET image_url = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
      WHERE image_url LIKE '%freerangestock.com%' OR image_url LIKE '%istockphoto.com%' OR name LIKE '%F&B%' OR name LIKE '%food%'
    `);

    await connection.execute(`
      UPDATE categories
      SET image_url = 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop'
      WHERE (name LIKE '%kitchen%' OR name_ar LIKE '%مطبخ%') AND image_url != 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
    `);

    await connection.execute(`
      UPDATE categories
      SET image_url = 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=300&fit=crop'
      WHERE (name LIKE '%dining%' OR name_ar LIKE '%طعام%' OR name_ar LIKE '%خدمة%') AND image_url NOT LIKE '%unsplash.com%'
    `);

    await connection.execute(`
      UPDATE categories
      SET image_url = 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop'
      WHERE (name LIKE '%clean%' OR name_ar LIKE '%تنظيف%') AND image_url NOT LIKE '%unsplash.com%'
    `);

    // إضافة فئات أساسية إذا لم تكن موجودة
    const [existingCategories] = await connection.execute('SELECT COUNT(*) as count FROM categories WHERE deleted_at IS NULL');

    if (existingCategories[0].count < 3) {
      console.log('📦 إضافة فئات أساسية...');

      await connection.execute(`
        INSERT IGNORE INTO categories (id, name, name_ar, description, description_ar, image_url, is_active)
        VALUES
        ('cat-kitchen', 'Kitchen Equipment', 'معدات المطبخ', 'Professional kitchen equipment and appliances', 'معدات وأجهزة المطبخ المهنية', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop', 1),
        ('cat-dining', 'Dining & Service', 'الطعام والخدمة', 'Dining room and service equipment', 'معدات غرفة الطعام والخدمة', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop', 1),
        ('cat-cleaning', 'Cleaning Supplies', 'مستلزمات التنظيف', 'Professional cleaning equipment and supplies', 'معدات ومستلزمات التنظيف المهنية', 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop', 1),
        ('cat-refrigeration', 'Refrigeration', 'التبريد والتجميد', 'Commercial refrigeration and freezing equipment', 'معدات التبريد والتجميد التجارية', 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=300&fit=crop', 1),
        ('cat-bakery', 'Bakery Equipment', 'معدات المخابز', 'Professional bakery and pastry equipment', 'معدات المخابز والحلويات المهنية', 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop', 1)
      `);
    }

    console.log('✅ تم إصلاح جميع صور الفئات');

    // عرض النتائج النهائية
    const [updatedCategories] = await connection.execute('SELECT id, name, name_ar, image_url FROM categories WHERE deleted_at IS NULL ORDER BY name_ar');
    console.log('\n📋 الفئات بعد التحديث:');
    updatedCategories.forEach(cat => {
      console.log(`- ${cat.name_ar} (${cat.name})`);
      console.log(`  الصورة الجديدة: ${cat.image_url}`);
      console.log('');
    });

    await connection.end();
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  }
}

fixCategoryImages();
