# دليل إعداد البريد الإلكتروني مع Hostinger

## 📧 نظرة عامة

تم تكوين النظام لاستخدام خدمة البريد الإلكتروني من Hostinger لإرسال طلبات التسعير. هذا الدليل يوضح كيفية إعداد البريد الإلكتروني بشكل صحيح.

## 🔧 الخطوات المطلوبة

### 1. إنشاء حساب بريد إلكتروني في Hostinger

1. **تسجيل الدخول إلى لوحة تحكم Hostinger**
   - اذهب إلى [hpanel.hostinger.com](https://hpanel.hostinger.com)
   - سجل دخولك بحسابك

2. **الانتقال إلى إعدادات البريد الإلكتروني**
   - اختر النطاق الخاص بك
   - اذهب إلى قسم "Email" أو "البريد الإلكتروني"

3. **إنشاء حساب بريد إلكتروني جديد**
   - انقر على "Create Email Account" أو "إنشاء حساب بريد إلكتروني"
   - أدخل اسم المستخدم (مثل: info, admin, noreply)
   - أدخل كلمة مرور قوية
   - احفظ الإعدادات

### 2. الحصول على إعدادات SMTP

**إعدادات SMTP الافتراضية لـ Hostinger:**
- **الخادم (Host):** smtp.hostinger.com
- **المنفذ (Port):** 465 (SSL) أو 587 (TLS)
- **التشفير:** SSL/TLS
- **المصادقة:** مطلوبة

### 3. تحديث متغيرات البيئة

قم بتحديث ملف `.env.local` بالمعلومات التالية:

```env
# إعدادات الإيميل - Hostinger SMTP
EMAIL_USER=<EMAIL>          # الإيميل الذي أنشأته
EMAIL_PASS=your-strong-password         # كلمة مرور الإيميل
ADMIN_EMAIL=<EMAIL>         # الإيميل المستقبل للطلبات
SMTP_HOST=smtp.hostinger.com            # خادم SMTP
SMTP_PORT=465                           # منفذ SSL
```

### 4. اختبار الإعدادات

1. **تشغيل الخادم المحلي**
   ```bash
   npm run dev
   ```

2. **إرسال طلب تسعير تجريبي**
   - اذهب إلى الموقع
   - أضف منتجات إلى السلة
   - أرسل طلب تسعير
   - تحقق من وصول الإيميل

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ "Authentication failed"
**السبب:** كلمة مرور خاطئة أو اسم مستخدم خاطئ
**الحل:**
- تأكد من صحة EMAIL_USER و EMAIL_PASS
- تأكد من أن الإيميل مُفعَّل في Hostinger

#### 2. خطأ "Connection timeout"
**السبب:** إعدادات الخادم أو المنفذ خاطئة
**الحل:**
- تأكد من SMTP_HOST = smtp.hostinger.com
- جرب المنفذ 587 بدلاً من 465
- تأكد من اتصال الإنترنت

#### 3. خطأ "Certificate error"
**السبب:** مشكلة في شهادة SSL
**الحل:**
- تم إضافة `rejectUnauthorized: false` في الكود
- تأكد من استخدام المنفذ الصحيح

#### 4. الإيميل لا يصل
**السبب:** قد يكون في مجلد الرسائل المزعجة
**الحل:**
- تحقق من مجلد Spam/Junk
- أضف الإيميل المرسل إلى قائمة الآمان

## 📝 ملاحظات مهمة

1. **النطاق:** يجب أن يكون الإيميل من نفس النطاق المستضاف على Hostinger
2. **كلمة المرور:** استخدم كلمة مرور قوية ومعقدة
3. **الأمان:** لا تشارك معلومات الإيميل مع أحد
4. **النسخ الاحتياطي:** احتفظ بنسخة من الإعدادات في مكان آمن

## 🔄 إعدادات بديلة

إذا واجهت مشاكل مع المنفذ 465، جرب هذه الإعدادات:

```env
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=587
```

وسيتم تعديل الكود تلقائياً لاستخدام TLS بدلاً من SSL.

## 📞 الدعم الفني

إذا استمرت المشاكل:
1. تواصل مع دعم Hostinger الفني
2. تأكد من أن خدمة البريد الإلكتروني مُفعَّلة في حسابك
3. تحقق من حالة الخادم في لوحة تحكم Hostinger

## ✅ التحقق من نجاح الإعداد

عند نجاح الإعداد، ستظهر هذه الرسائل في console:

```
✅ تم التحقق من إعدادات SMTP بنجاح
✅ تم إرسال الإيميل بنجاح عبر Hostinger SMTP!
🆔 Message ID: [message-id]
```

---

**تاريخ آخر تحديث:** يونيو 2025
**الإصدار:** 1.0
