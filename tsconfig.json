{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "next-env.d.ts", "tailwind.config.js", ".next/types/**/*.ts"], "exclude": ["node_modules"]}